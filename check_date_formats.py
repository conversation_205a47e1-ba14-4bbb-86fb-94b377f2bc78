import sqlite3

conn = sqlite3.connect('scrape_ufc_stats/data/ufc_data.db')
cursor = conn.cursor()

# Sample different date formats
print('Sample of different date formats in database:')
cursor.execute("""
SELECT date, event_name 
FROM events 
WHERE date IS NOT NULL AND date != ''
ORDER BY LENGTH(date), date
""")

results = cursor.fetchall()
seen_lengths = set()
for date, event in results:
    date_len = len(date)
    if date_len not in seen_lengths:
        print(f'\nLength {date_len}: "{date}" - {event}')
        seen_lengths.add(date_len)

# Check for events containing '2024' anywhere in the date
print('\n\nEvents with 2024 in date field:')
cursor.execute("""
SELECT event_name, date 
FROM events 
WHERE date LIKE '%2024%'
ORDER BY date
""")
for row in cursor.fetchall():
    print(f'{row[1]}: {row[0]}')

# Check last 20 events by ID to see if they're recent
print('\n\nLast 20 events by ID:')
cursor.execute("""
SELECT id, event_name, date 
FROM events 
ORDER BY id DESC
LIMIT 20
""")
for row in cursor.fetchall():
    print(f'ID {row[0]}: {row[2]} - {row[1]}')

conn.close()