import sqlite3
from datetime import datetime

conn = sqlite3.connect('scrape_ufc_stats/data/ufc_data.db')
cursor = conn.cursor()

# Get ALL events sorted by date with proper date parsing
cursor.execute("""
SELECT event_name, date 
FROM events 
WHERE date IS NOT NULL AND date != ''
ORDER BY date
""")

all_events = cursor.fetchall()

# Parse dates properly and find events in the gap period
events_in_gap = []
for event_name, date_str in all_events:
    try:
        # Try different date formats
        if len(date_str) == 10 and '-' in date_str:  # YYYY-MM-DD format
            event_date = datetime.strptime(date_str, '%Y-%m-%d')
            if datetime(2023, 10, 21) <= event_date <= datetime(2024, 10, 26):
                events_in_gap.append((event_name, date_str, event_date))
    except:
        pass

print(f'Total events in database: {len(all_events)}')
print(f'\nEvents between Oct 21, 2023 and Oct 26, 2024:')
print('=' * 80)

if events_in_gap:
    # Sort by date
    events_in_gap.sort(key=lambda x: x[2])
    
    for event_name, date_str, _ in events_in_gap:
        print(f'{date_str}: {event_name}')
    
    print(f'\nTotal events in this period: {len(events_in_gap)}')
    
    # Check for gaps within this period
    print('\nChecking for gaps within this period:')
    for i in range(1, len(events_in_gap)):
        prev_date = events_in_gap[i-1][2]
        curr_date = events_in_gap[i][2]
        gap_days = (curr_date - prev_date).days
        if gap_days > 30:
            print(f'  Gap of {gap_days} days between:')
            print(f'    {events_in_gap[i-1][0]} ({events_in_gap[i-1][1]})')
            print(f'    {events_in_gap[i][0]} ({events_in_gap[i][1]})')
else:
    print('NO EVENTS FOUND IN THIS PERIOD!')

# Also check what's around these dates
print('\n\nEvents around the gap period:')
cursor.execute("""
SELECT event_name, date 
FROM events 
WHERE date >= '2023-09-01' AND date <= '2024-12-31'
AND date LIKE '____-__-__'
ORDER BY date
""")
for row in cursor.fetchall():
    print(f'{row[1]}: {row[0]}')

conn.close()