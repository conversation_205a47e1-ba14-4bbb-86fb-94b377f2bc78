"""
UFC Database Utilities

Helper functions and usage examples for the UFC Database system.
"""

from ufc_database import UFCDatabase
import pandas as pd

def setup_database():
    """Initialize and populate the UFC database from existing CSV files."""
    print("Setting up UFC Database...")
    
    # Initialize database
    db = UFCDatabase()
    
    # Import existing CSV data
    db.import_csv_data()
    
    print("Database setup complete!")
    return db

def example_queries():
    """Demonstrate various database queries."""
    db = UFCDatabase()
    
    print("\n=== Example Database Queries ===\n")
    
    # 1. Get recent events
    print("📅 Recent UFC Events:")
    recent_events = db.query("""
        SELECT event_name, date, location 
        FROM events 
        ORDER BY date DESC 
        LIMIT 5
    """)
    print(recent_events.to_string(index=False))
    
    # 2. Fighter statistics
    print("\n🥊 Sample Fighter Records:")
    fighter_stats = db.query("""
        SELECT 
            f.first_name || ' ' || f.last_name as fighter,
            COUNT(fi.id) as total_fights,
            COUNT(CASE WHEN fi.winner_id = f.id THEN 1 END) as wins,
            COUNT(CASE WHEN fi.winner_id != f.id AND fi.winner_id IS NOT NULL THEN 1 END) as losses
        FROM fighters f
        LEFT JOIN fights fi ON (fi.fighter1_id = f.id OR fi.fighter2_id = f.id)
        GROUP BY f.id
        HAVING total_fights > 0
        ORDER BY total_fights DESC
        LIMIT 10
    """)
    print(fighter_stats.to_string(index=False))
    
    # 3. Event statistics
    print("\n📊 Events by Year:")
    events_by_year = db.query("""
        SELECT 
            substr(date, -4) as year,
            COUNT(*) as events
        FROM events 
        WHERE date != ''
        GROUP BY substr(date, -4)
        ORDER BY year DESC
        LIMIT 10
    """)
    print(events_by_year.to_string(index=False))
    
    # 4. Most active fighters
    print("\n🏃 Most Active Fighters:")
    active_fighters = db.query("""
        SELECT 
            f.first_name || ' ' || f.last_name as fighter,
            COUNT(fi.id) as fights,
            MIN(e.date) as first_fight,
            MAX(e.date) as last_fight
        FROM fighters f
        JOIN fights fi ON (fi.fighter1_id = f.id OR fi.fighter2_id = f.id)
        JOIN events e ON fi.event_id = e.id
        WHERE e.date != ''
        GROUP BY f.id
        HAVING fights >= 5
        ORDER BY fights DESC
        LIMIT 10
    """)
    print(active_fighters.to_string(index=False))

def export_reports():
    """Generate CSV reports for analysis."""
    db = UFCDatabase()
    
    print("\n📈 Generating Analysis Reports...")
    
    # Fighter win rates
    win_rates = db.query("""
        SELECT 
            f.first_name || ' ' || f.last_name as fighter,
            COUNT(fi.id) as total_fights,
            COUNT(CASE WHEN fi.winner_id = f.id THEN 1 END) as wins,
            ROUND(
                (COUNT(CASE WHEN fi.winner_id = f.id THEN 1 END) * 100.0) / 
                NULLIF(COUNT(fi.id), 0), 1
            ) as win_percentage
        FROM fighters f
        LEFT JOIN fights fi ON (fi.fighter1_id = f.id OR fi.fighter2_id = f.id)
        GROUP BY f.id
        HAVING total_fights >= 3
        ORDER BY win_percentage DESC, total_fights DESC
    """)
    win_rates.to_csv("reports/fighter_win_rates.csv", index=False)
    
    # Event analysis
    event_analysis = db.query("""
        SELECT 
            e.event_name,
            e.date,
            e.location,
            COUNT(f.id) as total_fights,
            COUNT(CASE WHEN f.result_method LIKE '%KO%' OR f.result_method LIKE '%TKO%' THEN 1 END) as knockouts,
            COUNT(CASE WHEN f.result_method LIKE '%Submission%' THEN 1 END) as submissions
        FROM events e
        LEFT JOIN fights f ON e.id = f.event_id
        GROUP BY e.id
        ORDER BY e.date DESC
    """)
    event_analysis.to_csv("reports/event_analysis.csv", index=False)
    
    print("Reports saved to reports/ directory")

def search_fighter(name: str):
    """Search for a specific fighter and show their record."""
    db = UFCDatabase()
    
    fighter_info = db.query("""
        SELECT 
            f.first_name,
            f.last_name,
            f.nickname,
            COUNT(fi.id) as total_fights,
            COUNT(CASE WHEN fi.winner_id = f.id THEN 1 END) as wins,
            COUNT(CASE WHEN fi.winner_id != f.id AND fi.winner_id IS NOT NULL THEN 1 END) as losses
        FROM fighters f
        LEFT JOIN fights fi ON (fi.fighter1_id = f.id OR fi.fighter2_id = f.id)
        WHERE f.first_name LIKE ? OR f.last_name LIKE ? OR f.nickname LIKE ?
        GROUP BY f.id
    """, (f"%{name}%", f"%{name}%", f"%{name}%"))
    
    if not fighter_info.empty:
        print(f"\n🥊 Fighter: {name}")
        print(fighter_info.to_string(index=False))
        
        # Get recent fights
        recent_fights = db.query("""
            SELECT 
                e.event_name,
                e.date,
                f.bout,
                CASE 
                    WHEN f.winner_id = fi.id THEN 'WIN'
                    WHEN f.winner_id IS NOT NULL THEN 'LOSS'
                    ELSE 'NO RESULT'
                END as result
            FROM fighters fi
            JOIN fights f ON (f.fighter1_id = fi.id OR f.fighter2_id = fi.id)
            JOIN events e ON f.event_id = e.id
            WHERE fi.first_name LIKE ? OR fi.last_name LIKE ? OR fi.nickname LIKE ?
            ORDER BY e.date DESC
            LIMIT 10
        """, (f"%{name}%", f"%{name}%", f"%{name}%"))
        
        if not recent_fights.empty:
            print(f"\n📋 Recent Fights:")
            print(recent_fights.to_string(index=False))
    else:
        print(f"No fighter found matching: {name}")

if __name__ == "__main__":
    # Example usage
    db = setup_database()
    example_queries()
    
    # Search for a specific fighter
    # search_fighter("Jon Jones")
    
    # Export analysis reports
    # export_reports()