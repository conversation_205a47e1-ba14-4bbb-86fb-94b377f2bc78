const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, 'data', 'ufc_data.db');
const db = new Database(dbPath, { readonly: true });

console.log('Checking UFC 308 and UFC 294 data...\n');

// 1. Check if both events exist
console.log('1. CHECKING EVENTS:');
const events = db.prepare(`
  SELECT event_id, event_name, event_date, event_location
  FROM events
  WHERE event_name LIKE '%UFC 308%' 
     OR event_name LIKE '%UFC 294%'
     OR event_name LIKE '%Topuria%Holloway%'
     OR event_name LIKE '%<PERSON><PERSON>chev%Volkanovski%'
  ORDER BY event_date DESC
`).all();

console.log(`Found ${events.length} matching events:`);
events.forEach(event => {
  console.log(`  - ${event.event_name} (ID: ${event.event_id}, Date: ${event.event_date})`);
});

// Get specific event IDs
const ufc308Events = events.filter(e => 
  e.event_name.includes('UFC 308') || 
  (e.event_name.includes('Topuria') && e.event_name.includes('Holloway'))
);
const ufc294Events = events.filter(e => 
  e.event_name.includes('UFC 294') || 
  (e.event_name.includes('Makhachev') && e.event_name.includes('Volkanovski'))
);

console.log(`\nUFC 308 events: ${ufc308Events.length}`);
console.log(`UFC 294 events: ${ufc294Events.length}`);

// 2. Count fights for each event
console.log('\n2. COUNTING FIGHTS PER EVENT:');
events.forEach(event => {
  const fightCount = db.prepare(`
    SELECT COUNT(*) as count
    FROM fights
    WHERE event_id = ?
  `).get(event.event_id);
  
  console.log(`  ${event.event_name}: ${fightCount.count} fights`);
});

// 3. Check for duplicate fights
console.log('\n3. CHECKING FOR DUPLICATE FIGHTS:');
events.forEach(event => {
  const duplicates = db.prepare(`
    SELECT fighter1_id, fighter2_id, COUNT(*) as count
    FROM fights
    WHERE event_id = ?
    GROUP BY fighter1_id, fighter2_id
    HAVING COUNT(*) > 1
  `).all(event.event_id);
  
  if (duplicates.length > 0) {
    console.log(`  ${event.event_name}: Found ${duplicates.length} duplicate fight pairings`);
    duplicates.forEach(dup => {
      const fighterNames = db.prepare(`
        SELECT 
          f1.fighter_name as fighter1_name,
          f2.fighter_name as fighter2_name
        FROM fights
        JOIN fighters f1 ON fights.fighter1_id = f1.fighter_id
        JOIN fighters f2 ON fights.fighter2_id = f2.fighter_id
        WHERE fights.event_id = ? 
          AND fights.fighter1_id = ? 
          AND fights.fighter2_id = ?
        LIMIT 1
      `).get(event.event_id, dup.fighter1_id, dup.fighter2_id);
      
      console.log(`    - ${fighterNames.fighter1_name} vs ${fighterNames.fighter2_name} (${dup.count} times)`);
    });
  } else {
    console.log(`  ${event.event_name}: No duplicate fights found`);
  }
});

// 4. Check fight_stats for these events
console.log('\n4. CHECKING FIGHT STATS:');
events.forEach(event => {
  // Get all fights for this event
  const fights = db.prepare(`
    SELECT fight_id, fighter1_id, fighter2_id
    FROM fights
    WHERE event_id = ?
  `).all(event.event_id);
  
  let statsCount = 0;
  let missingStats = [];
  
  fights.forEach(fight => {
    // Check if both fighters have stats for this fight
    const fighter1Stats = db.prepare(`
      SELECT COUNT(*) as count
      FROM fight_stats
      WHERE fight_id = ? AND fighter_id = ?
    `).get(fight.fight_id, fight.fighter1_id);
    
    const fighter2Stats = db.prepare(`
      SELECT COUNT(*) as count
      FROM fight_stats
      WHERE fight_id = ? AND fighter_id = ?
    `).get(fight.fight_id, fight.fighter2_id);
    
    if (fighter1Stats.count > 0) statsCount++;
    if (fighter2Stats.count > 0) statsCount++;
    
    if (fighter1Stats.count === 0 || fighter2Stats.count === 0) {
      const fighterNames = db.prepare(`
        SELECT 
          f1.fighter_name as fighter1_name,
          f2.fighter_name as fighter2_name
        FROM fights
        JOIN fighters f1 ON fights.fighter1_id = f1.fighter_id
        JOIN fighters f2 ON fights.fighter2_id = f2.fighter_id
        WHERE fights.fight_id = ?
      `).get(fight.fight_id);
      
      missingStats.push({
        fight_id: fight.fight_id,
        fighters: `${fighterNames.fighter1_name} vs ${fighterNames.fighter2_name}`,
        fighter1_has_stats: fighter1Stats.count > 0,
        fighter2_has_stats: fighter2Stats.count > 0
      });
    }
  });
  
  console.log(`  ${event.event_name}:`);
  console.log(`    - Total fight records: ${fights.length * 2} (${fights.length} fights × 2 fighters)`);
  console.log(`    - Fight stats records: ${statsCount}`);
  console.log(`    - Missing stats: ${missingStats.length} fights`);
  
  if (missingStats.length > 0) {
    console.log('    Missing stats for:');
    missingStats.forEach(ms => {
      const missing = [];
      if (!ms.fighter1_has_stats) missing.push('Fighter 1');
      if (!ms.fighter2_has_stats) missing.push('Fighter 2');
      console.log(`      - ${ms.fighters} (Fight ID: ${ms.fight_id}, Missing: ${missing.join(', ')})`);
    });
  }
});

// 5. Data quality issues
console.log('\n5. DATA QUALITY CHECKS:');

// Check for fights with null or invalid data
events.forEach(event => {
  console.log(`\n  ${event.event_name}:`);
  
  // Check for NULL values in critical fields
  const nullChecks = db.prepare(`
    SELECT 
      SUM(CASE WHEN winner_id IS NULL THEN 1 ELSE 0 END) as null_winners,
      SUM(CASE WHEN result_method IS NULL THEN 1 ELSE 0 END) as null_methods,
      SUM(CASE WHEN result_round IS NULL THEN 1 ELSE 0 END) as null_rounds,
      SUM(CASE WHEN result_time IS NULL THEN 1 ELSE 0 END) as null_times
    FROM fights
    WHERE event_id = ?
  `).get(event.event_id);
  
  console.log(`    - Fights with NULL winner: ${nullChecks.null_winners}`);
  console.log(`    - Fights with NULL method: ${nullChecks.null_methods}`);
  console.log(`    - Fights with NULL round: ${nullChecks.null_rounds}`);
  console.log(`    - Fights with NULL time: ${nullChecks.null_times}`);
  
  // Check fight_stats for anomalies
  const statsAnomalies = db.prepare(`
    SELECT 
      fs.fighter_id,
      f.fighter_name,
      fs.total_strikes_att,
      fs.total_strikes_landed,
      fs.takedowns_att,
      fs.takedowns_landed
    FROM fight_stats fs
    JOIN fighters f ON fs.fighter_id = f.fighter_id
    JOIN fights ON fs.fight_id = fights.fight_id
    WHERE fights.event_id = ?
      AND (
        fs.total_strikes_landed > fs.total_strikes_att
        OR fs.takedowns_landed > fs.takedowns_att
        OR fs.total_strikes_att < 0
        OR fs.total_strikes_landed < 0
      )
  `).all(event.event_id);
  
  if (statsAnomalies.length > 0) {
    console.log(`    - Stats anomalies found: ${statsAnomalies.length}`);
    statsAnomalies.forEach(anomaly => {
      console.log(`      - ${anomaly.fighter_name}: Strikes ${anomaly.total_strikes_landed}/${anomaly.total_strikes_att}, Takedowns ${anomaly.takedowns_landed}/${anomaly.takedowns_att}`);
    });
  } else {
    console.log('    - No stats anomalies found');
  }
});

// Additional check: Show sample fight stats
console.log('\n6. SAMPLE FIGHT STATS:');
events.forEach(event => {
  console.log(`\n  ${event.event_name} - First fight stats:`);
  
  const sampleStats = db.prepare(`
    SELECT 
      f.fighter_name,
      fs.total_strikes_landed,
      fs.total_strikes_att,
      fs.significant_strikes_landed,
      fs.significant_strikes_att,
      fs.takedowns_landed,
      fs.takedowns_att,
      fs.ctrl_time
    FROM fight_stats fs
    JOIN fighters f ON fs.fighter_id = f.fighter_id
    JOIN fights ON fs.fight_id = fights.fight_id
    WHERE fights.event_id = ?
    LIMIT 2
  `).all(event.event_id);
  
  sampleStats.forEach(stat => {
    console.log(`    ${stat.fighter_name}:`);
    console.log(`      - Total Strikes: ${stat.total_strikes_landed}/${stat.total_strikes_att}`);
    console.log(`      - Significant Strikes: ${stat.significant_strikes_landed}/${stat.significant_strikes_att}`);
    console.log(`      - Takedowns: ${stat.takedowns_landed}/${stat.takedowns_att}`);
    console.log(`      - Control Time: ${stat.ctrl_time || 'N/A'}`);
  });
});

db.close();
console.log('\nAnalysis complete.');