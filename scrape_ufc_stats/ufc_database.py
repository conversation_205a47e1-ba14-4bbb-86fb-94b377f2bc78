"""
UFC Database Management Module

Hybrid CSV + SQLite approach that maintains compatibility with existing CSV workflow
while adding relational database capabilities for advanced queries and analytics.
"""

import sqlite3
import pandas as pd
import yaml
from pathlib import Path
from typing import Optional, Dict, Any
import logging

class UFCDatabase:
    """Manages UFC data in SQLite with CSV export compatibility."""
    
    def __init__(self, db_path: str = "data/ufc_data.db", config_path: str = "scrape_ufc_stats_config.yaml"):
        self.db_path = Path(db_path)
        self.config_path = config_path
        self.db_path.parent.mkdir(exist_ok=True)
        
        # Load existing config
        with open(self.config_path, 'r') as f:
            self.config = yaml.safe_load(f)
            
        self._init_database()
    
    def _init_database(self):
        """Initialize SQLite database with UFC schema."""
        with sqlite3.connect(self.db_path) as conn:
            # Events table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS events (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_name TEXT UNIQUE NOT NULL,
                    url TEXT,
                    date TEXT,
                    location TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Fighters table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS fighters (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    first_name TEXT,
                    last_name TEXT,
                    nickname TEXT,
                    url TEXT UNIQUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Fighter stats (tale of tape)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS fighter_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    fighter_id INTEGER,
                    height TEXT,
                    weight TEXT,
                    reach TEXT,
                    stance TEXT,
                    wins INTEGER,
                    losses INTEGER,
                    draws INTEGER,
                    FOREIGN KEY (fighter_id) REFERENCES fighters (id)
                )
            """)
            
            # Fights table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS fights (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_id INTEGER,
                    bout TEXT NOT NULL,
                    url TEXT UNIQUE,
                    fighter1_id INTEGER,
                    fighter2_id INTEGER,
                    winner_id INTEGER,
                    result_method TEXT,
                    result_round INTEGER,
                    result_time TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (event_id) REFERENCES events (id),
                    FOREIGN KEY (fighter1_id) REFERENCES fighters (id),
                    FOREIGN KEY (fighter2_id) REFERENCES fighters (id),
                    FOREIGN KEY (winner_id) REFERENCES fighters (id)
                )
            """)
            
            # Fight statistics (round by round)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS fight_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    fight_id INTEGER,
                    fighter_id INTEGER,
                    round_number INTEGER,
                    knockdowns INTEGER,
                    sig_strikes_landed INTEGER,
                    sig_strikes_attempted INTEGER,
                    sig_strikes_pct REAL,
                    total_strikes_landed INTEGER,
                    total_strikes_attempted INTEGER,
                    takedowns_landed INTEGER,
                    takedowns_attempted INTEGER,
                    takedowns_pct REAL,
                    submission_attempts INTEGER,
                    reversals INTEGER,
                    control_time TEXT,
                    head_strikes INTEGER,
                    body_strikes INTEGER,
                    leg_strikes INTEGER,
                    distance_strikes INTEGER,
                    clinch_strikes INTEGER,
                    ground_strikes INTEGER,
                    FOREIGN KEY (fight_id) REFERENCES fights (id),
                    FOREIGN KEY (fighter_id) REFERENCES fighters (id)
                )
            """)
            
            # Create indexes for performance
            conn.execute("CREATE INDEX IF NOT EXISTS idx_events_date ON events (date)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_fights_event ON fights (event_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_fight_stats_fight ON fight_stats (fight_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_fighters_name ON fighters (last_name, first_name)")
            
            conn.commit()
    
    def import_csv_data(self):
        """Import existing CSV data into SQLite database."""
        print("Importing CSV data into SQLite database...")
        
        # Import events
        events_df = pd.read_csv("ufc_event_details.csv")
        events_df.columns = ["event_name", "url", "date", "location"]
        self._import_dataframe(events_df, "events")
        
        # Import fighters
        fighters_df = pd.read_csv("ufc_fighter_details.csv")
        fighters_df.columns = ["first_name", "last_name", "nickname", "url"]
        self._import_dataframe(fighters_df, "fighters")
        
        # Import fighter stats (tale of tape)
        if Path("ufc_fighter_tott.csv").exists():
            fighter_tott_df = pd.read_csv("ufc_fighter_tott.csv")
            self._import_fighter_stats(fighter_tott_df)
        
        # Import fights
        fight_details_df = pd.read_csv("ufc_fight_details.csv")
        fight_results_df = pd.read_csv("ufc_fight_results.csv") if Path("ufc_fight_results.csv").exists() else None
        self._import_fights(fight_details_df, fight_results_df)
        
        # Import fight statistics
        fight_stats_df = pd.read_csv("ufc_fight_stats.csv")
        self._import_fight_statistics(fight_stats_df)
        
        print("CSV import completed!")
    
    def _import_dataframe(self, df: pd.DataFrame, table_name: str):
        """Import DataFrame to SQLite table."""
        with sqlite3.connect(self.db_path) as conn:
            if table_name == "events":
                # Insert events with proper column mapping
                for _, row in df.iterrows():
                    conn.execute("""
                        INSERT OR IGNORE INTO events (event_name, url, date, location)
                        VALUES (?, ?, ?, ?)
                    """, (row['event_name'], row['url'], row['date'], row['location']))
            elif table_name == "fighters":
                # Insert fighters with proper column mapping
                for _, row in df.iterrows():
                    conn.execute("""
                        INSERT OR IGNORE INTO fighters (first_name, last_name, nickname, url)
                        VALUES (?, ?, ?, ?)
                    """, (row['first_name'], row['last_name'], row['nickname'], row['url']))
            else:
                df.to_sql(table_name, conn, if_exists="replace", index=False)
            conn.commit()
    
    def _import_fighter_stats(self, tott_df: pd.DataFrame):
        """Import fighter tale of tape data."""
        # This will need to be implemented based on the actual structure of the TOTT CSV
        pass
    
    def _import_fights(self, fight_details_df: pd.DataFrame, fight_results_df: Optional[pd.DataFrame] = None):
        """Import fight data with results if available."""
        with sqlite3.connect(self.db_path) as conn:
            for _, row in fight_details_df.iterrows():
                # Get event_id
                event_cursor = conn.execute("SELECT id FROM events WHERE event_name = ?", (row['EVENT'],))
                event_result = event_cursor.fetchone()
                if not event_result:
                    continue
                event_id = event_result[0]
                
                # Insert fight
                conn.execute("""
                    INSERT OR IGNORE INTO fights (event_id, bout, url)
                    VALUES (?, ?, ?)
                """, (event_id, row['BOUT'], row['URL']))
            
            conn.commit()
    
    def _import_fight_statistics(self, stats_df: pd.DataFrame):
        """Import detailed fight statistics."""
        with sqlite3.connect(self.db_path) as conn:
            for _, row in stats_df.iterrows():
                # Get fight_id from bout and event
                fight_cursor = conn.execute("""
                    SELECT f.id FROM fights f 
                    JOIN events e ON f.event_id = e.id 
                    WHERE e.event_name = ? AND f.bout = ?
                """, (row['EVENT'], row['BOUT']))
                
                fight_result = fight_cursor.fetchone()
                if not fight_result:
                    continue
                fight_id = fight_result[0]
                
                # Get fighter_id
                fighter_cursor = conn.execute("""
                    SELECT id FROM fighters WHERE 
                    (first_name || ' ' || last_name) = ? OR
                    (first_name || ' ' || nickname || ' ' || last_name) = ?
                """, (row['FIGHTER'], row['FIGHTER']))
                
                fighter_result = fighter_cursor.fetchone()
                if not fighter_result:
                    continue
                fighter_id = fighter_result[0]
                
                # Parse round number
                round_num = int(row['ROUND'].replace('Round ', '')) if 'Round' in str(row['ROUND']) else 1
                
                # Parse statistics
                sig_str_parts = str(row['SIG.STR.']).split(' of ')
                sig_landed = int(sig_str_parts[0]) if len(sig_str_parts) == 2 else 0
                sig_attempted = int(sig_str_parts[1]) if len(sig_str_parts) == 2 else 0
                
                # Insert fight stats
                conn.execute("""
                    INSERT OR IGNORE INTO fight_stats (
                        fight_id, fighter_id, round_number, knockdowns,
                        sig_strikes_landed, sig_strikes_attempted,
                        total_strikes_landed, total_strikes_attempted
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    fight_id, fighter_id, round_num, int(row.get('KD', 0)),
                    sig_landed, sig_attempted,
                    sig_landed, sig_attempted  # Using sig strikes as total for now
                ))
            
            conn.commit()
    
    def export_to_csv(self, output_dir: str = "exports"):
        """Export database data back to CSV format for compatibility."""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            # Export events
            events_df = pd.read_sql("SELECT event_name, url, date, location FROM events", conn)
            events_df.to_csv(output_path / "ufc_events.csv", index=False)
            
            # Export fighters
            fighters_df = pd.read_sql("SELECT first_name, last_name, nickname, url FROM fighters", conn)
            fighters_df.to_csv(output_path / "ufc_fighters.csv", index=False)
            
            # Export fights with event names
            fights_df = pd.read_sql("""
                SELECT e.event_name, f.bout, f.url
                FROM fights f
                JOIN events e ON f.event_id = e.id
                ORDER BY e.date DESC
            """, conn)
            fights_df.to_csv(output_path / "ufc_fights.csv", index=False)
            
            # Export fight statistics
            stats_df = pd.read_sql("""
                SELECT 
                    e.event_name,
                    f.bout,
                    fs.round_number,
                    (fi.first_name || ' ' || fi.last_name) as fighter,
                    fs.knockdowns,
                    (fs.sig_strikes_landed || ' of ' || fs.sig_strikes_attempted) as sig_strikes,
                    fs.total_strikes_landed,
                    fs.total_strikes_attempted
                FROM fight_stats fs
                JOIN fights f ON fs.fight_id = f.id
                JOIN events e ON f.event_id = e.id
                JOIN fighters fi ON fs.fighter_id = fi.id
                ORDER BY e.date DESC, f.id, fs.round_number
            """, conn)
            stats_df.to_csv(output_path / "ufc_fight_stats.csv", index=False)
        
        print(f"CSV exports completed in {output_path}")
    
    def query(self, sql: str, params: tuple = ()) -> pd.DataFrame:
        """Execute SQL query and return DataFrame."""
        with sqlite3.connect(self.db_path) as conn:
            return pd.read_sql(sql, conn, params=params)
    
    def get_fighter_record(self, fighter_name: str) -> Dict[str, Any]:
        """Get complete fighter record and statistics."""
        fighter_data = self.query("""
            SELECT f.*, 
                   COUNT(CASE WHEN fi.winner_id = f.id THEN 1 END) as wins,
                   COUNT(CASE WHEN fi.winner_id != f.id AND fi.winner_id IS NOT NULL THEN 1 END) as losses,
                   COUNT(fi.id) as total_fights
            FROM fighters f
            LEFT JOIN fights fi ON (fi.fighter1_id = f.id OR fi.fighter2_id = f.id)
            WHERE (f.first_name || ' ' || f.last_name) LIKE ?
            GROUP BY f.id
        """, (f"%{fighter_name}%",))
        
        return fighter_data.to_dict('records')[0] if not fighter_data.empty else {}