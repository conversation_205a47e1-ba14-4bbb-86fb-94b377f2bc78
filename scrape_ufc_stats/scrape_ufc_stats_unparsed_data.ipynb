{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["\"\\nOverview\\nrun the notebook 'scape_ufc_stats_all_historical_data.ipynb' first to parse all available past data\\n\\nthis code checks existing files for previously parsed data\\nif there are no new or unparsed events, script stops\\n\\nif there are any unparsed events, script continues with parsing\\ncombine new data and existing data into one and write to file\\n\\nthis notebook can be run manually when desired\\nthe script, 'scrape_ufc_stats_unparsed_data.py' is the same code that can be set to run on a schedule\\n\""]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["'''\n", "Overview\n", "run the notebook 'scape_ufc_stats_all_historical_data.ipynb' first to parse all available past fight data\n", "and the notebook ' scrape_ufc_stats_fighter_tott.ipynb' to parse all available fighter data\n", "\n", "this code checks existing files for previously parsed data\n", "if there are no new or unparsed events, script stops\n", "\n", "if there are any unparsed events, script continues with parsing\n", "combine new data and existing data into one and write to file\n", "\n", "this notebook can be run manually when desired\n", "the script, 'scrape_ufc_stats_unparsed_data.py' is the same code that can be set to run on a schedule\n", "'''"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# imports\n", "import pandas as pd\n", "from tqdm.notebook import tqdm_notebook\n", "\n", "# import library\n", "import scrape_ufc_stats_library as LIB\n", "\n", "# import config\n", "import yaml\n", "config = yaml.safe_load(open('scrape_ufc_stats_config.yaml'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Scrape Unparsed Events and Fight Stats"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["All available events have been parsed.\n"]}], "source": ["### check if there are any unparsed events ###\n", "\n", "# read existing event details\n", "parsed_event_details_df = pd.read_csv(config['event_details_file_name'])\n", "# get list of parsed event names\n", "list_of_parsed_events = list(parsed_event_details_df['EVENT'])\n", "\n", "# get soup\n", "soup = LIB.get_soup(config['completed_events_all_url'])\n", "# parse event details\n", "updated_event_details_df = LIB.parse_event_details(soup)\n", "# get list of all event names\n", "list_of_all_events = list(updated_event_details_df['EVENT'])\n", "\n", "# find list event names that have not been parsed\n", "list_of_unparsed_events = [event for event in list_of_all_events if event not in list_of_parsed_events]\n", "\n", "# check if there are any unparsed events\n", "unparsed_events = False\n", "# if list_of_unparsed_events is empty then all available events have been parsed\n", "if not list_of_unparsed_events:\n", "    print('All available events have been parsed.')\n", "else:\n", "    # set unparsed_events to true\n", "    unparsed_events = True\n", "    # show list of unparsed events\n", "    print(list_of_unparsed_events)\n", "    # write event details to file\n", "    updated_event_details_df.to_csv(config['event_details_file_name'], index=False)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1b0603bd43744ded88727531d18e30f4", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/1 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "63edfcc8d8b7417eb596815e7e9158d5", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/10 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["### parse all missing events ###\n", "# if unparsed_events = True\n", "# the code below continues to run to parse all missing events\n", "# new data is added to existing data and is written to file\n", "\n", "if unparsed_events == True:\n", "    # read existing data files\n", "    parsed_fight_details_df = pd.read_csv(config['fight_details_file_name'])\n", "    parsed_fight_results_df = pd.read_csv(config['fight_results_file_name'])\n", "    parsed_fight_stats_df = pd.read_csv(config['fight_stats_file_name'])\n", "\n", "    ### parse fight details ###\n", "\n", "    # define list of urls of missing fights to parse\n", "    list_of_unparsed_events_urls = list(updated_event_details_df['URL'].loc[(updated_event_details_df['EVENT'].isin(list_of_unparsed_events))])\n", "\n", "    # create empty df to store fight details\n", "    unparsed_fight_details_df = pd.DataFrame(columns=config['fight_details_column_names'])\n", "\n", "    # loop through each event and parse fight details\n", "    for url in tqdm_notebook(list_of_unparsed_events_urls):\n", "\n", "        # get soup\n", "        soup = LIB.get_soup(url)\n", "\n", "        # parse fight links\n", "        fight_details_df = LIB.parse_fight_details(soup)\n", "        \n", "        # concat fight details to parsed fight details\n", "        # concat update fight details to the top of existing df\n", "        unparsed_fight_details_df = pd.concat([unparsed_fight_details_df, fight_details_df])\n", "\n", "    # concat unparsed and parsed fight details\n", "    parsed_fight_details_df = pd.concat([unparsed_fight_details_df, parsed_fight_details_df])\n", "\n", "    # write fight details to file\n", "    parsed_fight_details_df.to_csv(config['fight_details_file_name'], index=False)\n", "\n", "    ### parse fight results and fight stats\n", "\n", "    # define list of urls of fights to parse\n", "    list_of_unparsed_fight_details_urls = list(unparsed_fight_details_df['URL'])\n", "\n", "    # create empty df to store fight results\n", "    unparsed_fight_results_df = pd.DataFrame(columns=config['fight_results_column_names'])\n", "    # create empty df to store fight stats\n", "    unparsed_fight_stats_df = pd.DataFrame(columns=config['fight_stats_column_names'])\n", "\n", "    # loop through each fight and parse fight results and stats\n", "    for url in tqdm_notebook(list_of_unparsed_fight_details_urls):\n", "\n", "        # get soup\n", "        soup = LIB.get_soup(url)\n", "\n", "        # parse fight results and fight stats\n", "        fight_results_df, fight_stats_df = LIB.parse_organise_fight_results_and_stats(\n", "            soup,\n", "            url,\n", "            config['fight_results_column_names'],\n", "            config['totals_column_names'],\n", "            config['significant_strikes_column_names']\n", "            )\n", "\n", "        # concat fight results\n", "        unparsed_fight_results_df = pd.concat([unparsed_fight_results_df, fight_results_df])\n", "        # concat fight stats\n", "        unparsed_fight_stats_df = pd.concat([unparsed_fight_stats_df, fight_stats_df])\n", "\n", "    # concat unparsed fight results and fight stats to parsed fight results and fight stats\n", "    parsed_fight_results_df = pd.concat([unparsed_fight_results_df, parsed_fight_results_df])\n", "    parsed_fight_stats_df = pd.concat([unparsed_fight_stats_df, parsed_fight_stats_df])\n", "\n", "    # write to file\n", "    parsed_fight_results_df.to_csv(config['fight_results_file_name'], index=False)\n", "    # write to file\n", "    parsed_fight_stats_df.to_csv(config['fight_stats_file_name'], index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Scrape Unparsed Fighter Details"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "6a4912f898af4a578f646aab47398caf", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/26 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["All available fighters have been parsed.\n"]}], "source": ["### check if there are any unparsed fighters ###\n", "\n", "# read existing fighter details\n", "parsed_fighter_details_df = pd.read_csv(config['fighter_details_file_name'])\n", "# get list of parsed fighter urls\n", "list_of_parsed_urls = list(parsed_fighter_details_df['URL'])\n", "\n", "# generate list of urls for fighter details\n", "list_of_alphabetical_urls = LIB.generate_alphabetical_urls()\n", "\n", "# create empty dataframe to store all fighter details\n", "all_fighter_details_df = pd.DataFrame()\n", "\n", "# loop through list of alphabetical urls\n", "for url in tqdm_notebook(list_of_alphabetical_urls):\n", "    # get soup\n", "    soup = LIB.get_soup(url)\n", "    # parse fighter details\n", "    fighter_details_df = LIB.parse_fighter_details(soup, config['fighter_details_column_names'])\n", "    # concat fighter_details_df to all_fighter_details_df\n", "    all_fighter_details_df = pd.concat([all_fighter_details_df, fighter_details_df])\n", "\n", "# get all fighter urls\n", "unparsed_fighter_urls = list(all_fighter_details_df['URL'])\n", "\n", "# get list of unparsed fighter urls\n", "list_of_unparsed_fighter_urls = [url for url in unparsed_fighter_urls if url not in list_of_parsed_urls]\n", "\n", "# check if there are any unparsed fighters\n", "unparsed_fighters = False\n", "# if list_of_unparsed_fighter_urls is empty then all available fighters have been parsed\n", "if not list_of_unparsed_fighter_urls:\n", "    print('All available fighters have been parsed.')\n", "else:\n", "    # set unparsed_fighters to true\n", "    unparsed_fighters = True\n", "    # show list of unparsed events\n", "    print(list_of_unparsed_fighter_urls)\n", "    # write event details to file\n", "    all_fighter_details_df.to_csv(config['fighter_details_file_name'], index=False)"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [], "source": ["### parse all missing fighters ###\n", "# if unparsed_fighters = True\n", "# the code below continues to run to parse all missing fighters\n", "# new data is added to existing data and is written to file\n", "\n", "if unparsed_fighters == True:\n", "\n", "    # read existing data files\n", "    parsed_fighter_tott_df = pd.read_csv(config['fighter_tott_file_name'])\n", "\n", "    # create empty df to store fighters' tale of the tape\n", "    unparsed_fighter_tott_df = pd.DataFrame(columns=config['fighter_tott_column_names'])\n", "\n", "    # loop through list_of_fighter_urls\n", "    for url in tqdm_notebook(list_of_unparsed_fighter_urls):\n", "        # get soup\n", "        soup = LIB.get_soup(url)\n", "        # parse fighter tale of the tape\n", "        fighter_tott = LIB.parse_fighter_tott(soup)\n", "        # organise fighter tale of the tape\n", "        fighter_tott_df = LIB.organise_fighter_tott(fighter_tott, config['fighter_tott_column_names'], url)\n", "        # concat fighter\n", "        unparsed_fighter_tott_df = pd.concat([unparsed_fighter_tott_df, fighter_tott_df])\n", "\n", "    # concat unparsed fighter tale of the tape to parsed fighter tale of the tape\n", "    parsed_fighter_tott_df = pd.concat([parsed_fighter_tott_df, unparsed_fighter_tott_df])\n", "    # write to file \n", "    parsed_fighter_tott_df.to_csv(config['fighter_tott_file_name'], index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "9aac8944d03ded7dbafcde28cbfe34d23c9079c2df9125865de5144d9ea8ac9a"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}