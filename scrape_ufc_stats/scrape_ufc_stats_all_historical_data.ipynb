{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Overview\n", "this notebook parses all past ufc fight stats when run, it does not include upcoming fights\n", "\n", "\n", "scrape ufc fight stats\n", "get all event details, name, url, date, location for all ufc events\n", "for each event, get fight details all fights on card\n", "parse each fight to get fight stats of both fighters\n", "'''"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# imports\n", "import pandas as pd\n", "from tqdm.notebook import tqdm_notebook\n", "\n", "# import library\n", "import scrape_ufc_stats_library as LIB\n", "\n", "# import config\n", "import yaml\n", "config = yaml.safe_load(open('scrape_ufc_stats_config.yaml'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse Event Details\n", "Includes:\n", "<br>\n", "Event\n", "<br>\n", "URL\n", "<br>\n", "Date\n", "<br>\n", "Location\n", "<br>"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# define url to parse\n", "events_url = config['completed_events_all_url']"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EVENT</th>\n", "      <th>URL</th>\n", "      <th>DATE</th>\n", "      <th>LOCATION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/f5585e675af7...</td>\n", "      <td>January 15, 2022</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>UFC Fight Night: <PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/2a470ad41c22...</td>\n", "      <td>December 18, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>UFC 269: <PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/ef927e4fe211...</td>\n", "      <td>December 11, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/509697e08673...</td>\n", "      <td>December 04, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/event-details/c95fbc085e17...</td>\n", "      <td>November 20, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>584</th>\n", "      <td>UFC 6: Clash of the Titans</td>\n", "      <td>http://ufcstats.com/event-details/1c3f5e85b59e...</td>\n", "      <td>July 14, 1995</td>\n", "      <td>Casper, Wyoming, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>585</th>\n", "      <td>UFC 5: The Return of the Beast</td>\n", "      <td>http://ufcstats.com/event-details/dedc3bb440d0...</td>\n", "      <td>April 07, 1995</td>\n", "      <td>Charlotte, North Carolina, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>586</th>\n", "      <td>UFC 4: Revenge of the Warriors</td>\n", "      <td>http://ufcstats.com/event-details/b60391da771d...</td>\n", "      <td>December 16, 1994</td>\n", "      <td>Tulsa, Oklahoma, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>587</th>\n", "      <td>UFC 3: The American Dream</td>\n", "      <td>http://ufcstats.com/event-details/1a49e0670dfa...</td>\n", "      <td>September 09, 1994</td>\n", "      <td>Charlotte, North Carolina, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>588</th>\n", "      <td>UFC 2: No Way Out</td>\n", "      <td>http://ufcstats.com/event-details/a6a9ab5a824e...</td>\n", "      <td>March 11, 1994</td>\n", "      <td>Denver, Colorado, USA</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>589 rows × 4 columns</p>\n", "</div>"], "text/plain": ["                                    EVENT  \\\n", "0    UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON>   \n", "1      UFC Fight Night: <PERSON> vs. <PERSON><PERSON><PERSON>   \n", "2           UFC 269: <PERSON> vs. <PERSON><PERSON><PERSON>   \n", "3          UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON>   \n", "4        UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON>   \n", "..                                    ...   \n", "584            UFC 6: Clash of the Titans   \n", "585        UFC 5: The Return of the Beast   \n", "586        UFC 4: Revenge of the Warriors   \n", "587             UFC 3: The American Dream   \n", "588                     UFC 2: No Way Out   \n", "\n", "                                                   URL                DATE  \\\n", "0    http://ufcstats.com/event-details/f5585e675af7...    January 15, 2022   \n", "1    http://ufcstats.com/event-details/2a470ad41c22...   December 18, 2021   \n", "2    http://ufcstats.com/event-details/ef927e4fe211...   December 11, 2021   \n", "3    http://ufcstats.com/event-details/509697e08673...   December 04, 2021   \n", "4    http://ufcstats.com/event-details/c95fbc085e17...   November 20, 2021   \n", "..                                                 ...                 ...   \n", "584  http://ufcstats.com/event-details/1c3f5e85b59e...       July 14, 1995   \n", "585  http://ufcstats.com/event-details/dedc3bb440d0...      April 07, 1995   \n", "586  http://ufcstats.com/event-details/b60391da771d...   December 16, 1994   \n", "587  http://ufcstats.com/event-details/1a49e0670dfa...  September 09, 1994   \n", "588  http://ufcstats.com/event-details/a6a9ab5a824e...      March 11, 1994   \n", "\n", "                           LOCATION  \n", "0            Las Vegas, Nevada, USA  \n", "1            Las Vegas, Nevada, USA  \n", "2            Las Vegas, Nevada, USA  \n", "3            Las Vegas, Nevada, USA  \n", "4            Las Vegas, Nevada, USA  \n", "..                              ...  \n", "584            Casper, Wyoming, USA  \n", "585  Charlotte, North Carolina, USA  \n", "586            Tulsa, Oklahoma, USA  \n", "587  Charlotte, North Carolina, USA  \n", "588           Denver, Colorado, USA  \n", "\n", "[589 rows x 4 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# get soup\n", "soup = LIB.get_soup(events_url)\n", "\n", "# parse event details\n", "all_event_details_df = LIB.parse_event_details(soup)\n", "\n", "# show event details\n", "display(all_event_details_df)\n", "\n", "# write event details to file\n", "all_event_details_df.to_csv(config['event_details_file_name'], index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse Fight Details\n", "Includes:\n", "<br>\n", "Event\n", "<br>\n", "Bout\n", "<br>\n", "URL"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# define list of urls of fights to parse\n", "list_of_events_urls = list(all_event_details_df['URL'])"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "8b151bb6852944c7b567643d6e78ff54", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/589 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m<ipython-input-6-9d6031b4df86>\u001b[0m in \u001b[0;36m<module>\u001b[1;34m\u001b[0m\n\u001b[0;32m      6\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m      7\u001b[0m     \u001b[1;31m# get soup\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m----> 8\u001b[1;33m     \u001b[0msoup\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mLIB\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget_soup\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0murl\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m      9\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     10\u001b[0m     \u001b[1;31m# parse fight links\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32mc:\\Users\\<USER>\\Projects\\scrape_ufc_stats\\scrape_ufc_stats_library.py\u001b[0m in \u001b[0;36mget_soup\u001b[1;34m(url)\u001b[0m\n\u001b[0;32m     29\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     30\u001b[0m     \u001b[1;31m# get page of url\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 31\u001b[1;33m     \u001b[0mpage\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mrequests\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mget\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0murl\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     32\u001b[0m     \u001b[1;31m# create soup\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     33\u001b[0m     \u001b[0msoup\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mBeautifulSoup\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mpage\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mcontent\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;34m'html.parser'\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\api.py\u001b[0m in \u001b[0;36mget\u001b[1;34m(url, params, **kwargs)\u001b[0m\n\u001b[0;32m     74\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     75\u001b[0m     \u001b[0mkwargs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0msetdefault\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'allow_redirects'\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;32mTrue\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 76\u001b[1;33m     \u001b[1;32mreturn\u001b[0m \u001b[0mrequest\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m'get'\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0murl\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mparams\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mparams\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     77\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     78\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\api.py\u001b[0m in \u001b[0;36mrequest\u001b[1;34m(method, url, **kwargs)\u001b[0m\n\u001b[0;32m     59\u001b[0m     \u001b[1;31m# cases, and look like a memory leak in others.\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     60\u001b[0m     \u001b[1;32mwith\u001b[0m \u001b[0msessions\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mSession\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0msession\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m---> 61\u001b[1;33m         \u001b[1;32mreturn\u001b[0m \u001b[0msession\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrequest\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mmethod\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mmethod\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0murl\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0murl\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m     62\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m     63\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\sessions.py\u001b[0m in \u001b[0;36mrequest\u001b[1;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[0;32m    540\u001b[0m         }\n\u001b[0;32m    541\u001b[0m         \u001b[0msend_kwargs\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mupdate\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0msettings\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 542\u001b[1;33m         \u001b[0mresp\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0msend\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mprep\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0msend_kwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    543\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    544\u001b[0m         \u001b[1;32mreturn\u001b[0m \u001b[0mresp\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\sessions.py\u001b[0m in \u001b[0;36msend\u001b[1;34m(self, request, **kwargs)\u001b[0m\n\u001b[0;32m    653\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    654\u001b[0m         \u001b[1;31m# Send the request\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 655\u001b[1;33m         \u001b[0mr\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0madapter\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0msend\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mrequest\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;33m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    656\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    657\u001b[0m         \u001b[1;31m# Total elapsed time of the request (approximately)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\requests\\adapters.py\u001b[0m in \u001b[0;36msend\u001b[1;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[0;32m    437\u001b[0m         \u001b[1;32mtry\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    438\u001b[0m             \u001b[1;32mif\u001b[0m \u001b[1;32mnot\u001b[0m \u001b[0mchunked\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 439\u001b[1;33m                 resp = conn.urlopen(\n\u001b[0m\u001b[0;32m    440\u001b[0m                     \u001b[0mmethod\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mrequest\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mmethod\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    441\u001b[0m                     \u001b[0murl\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0murl\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py\u001b[0m in \u001b[0;36murlopen\u001b[1;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, **response_kw)\u001b[0m\n\u001b[0;32m    697\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    698\u001b[0m             \u001b[1;31m# Make the request on the httplib connection object.\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 699\u001b[1;33m             httplib_response = self._make_request(\n\u001b[0m\u001b[0;32m    700\u001b[0m                 \u001b[0mconn\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    701\u001b[0m                 \u001b[0mmethod\u001b[0m\u001b[1;33m,\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py\u001b[0m in \u001b[0;36m_make_request\u001b[1;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[0;32m    443\u001b[0m                     \u001b[1;31m# Python 3 (including for exceptions like SystemExit).\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    444\u001b[0m                     \u001b[1;31m# Otherwise it looks like a bug in the code.\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 445\u001b[1;33m                     \u001b[0msix\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mraise_from\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0me\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;32mNone\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    446\u001b[0m         \u001b[1;32mexcept\u001b[0m \u001b[1;33m(\u001b[0m\u001b[0mSocketTimeout\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mBaseSSLError\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mSocketError\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    447\u001b[0m             \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_raise_timeout\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0merr\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0me\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0murl\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0murl\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mtimeout_value\u001b[0m\u001b[1;33m=\u001b[0m\u001b[0mread_timeout\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\packages\\six.py\u001b[0m in \u001b[0;36mraise_from\u001b[1;34m(value, from_value)\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\site-packages\\urllib3\\connectionpool.py\u001b[0m in \u001b[0;36m_make_request\u001b[1;34m(self, conn, method, url, timeout, chunked, **httplib_request_kw)\u001b[0m\n\u001b[0;32m    438\u001b[0m                 \u001b[1;31m# Python 3\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    439\u001b[0m                 \u001b[1;32mtry\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 440\u001b[1;33m                     \u001b[0mhttplib_response\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mconn\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mgetresponse\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    441\u001b[0m                 \u001b[1;32mexcept\u001b[0m \u001b[0mBaseException\u001b[0m \u001b[1;32mas\u001b[0m \u001b[0me\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    442\u001b[0m                     \u001b[1;31m# Remove the TypeError from the exception chain in\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\http\\client.py\u001b[0m in \u001b[0;36mgetresponse\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m   1345\u001b[0m         \u001b[1;32mtry\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1346\u001b[0m             \u001b[1;32mtry\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m-> 1347\u001b[1;33m                 \u001b[0mresponse\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mbegin\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m   1348\u001b[0m             \u001b[1;32mexcept\u001b[0m \u001b[0mConnectionError\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m   1349\u001b[0m                 \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mclose\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\http\\client.py\u001b[0m in \u001b[0;36mbegin\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    305\u001b[0m         \u001b[1;31m# read until we get a non-100 response\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    306\u001b[0m         \u001b[1;32mwhile\u001b[0m \u001b[1;32mTrue\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 307\u001b[1;33m             \u001b[0mversion\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mstatus\u001b[0m\u001b[1;33m,\u001b[0m \u001b[0mreason\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_read_status\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    308\u001b[0m             \u001b[1;32mif\u001b[0m \u001b[0mstatus\u001b[0m \u001b[1;33m!=\u001b[0m \u001b[0mCONTINUE\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    309\u001b[0m                 \u001b[1;32mbreak\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\http\\client.py\u001b[0m in \u001b[0;36m_read_status\u001b[1;34m(self)\u001b[0m\n\u001b[0;32m    266\u001b[0m \u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    267\u001b[0m     \u001b[1;32mdef\u001b[0m \u001b[0m_read_status\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 268\u001b[1;33m         \u001b[0mline\u001b[0m \u001b[1;33m=\u001b[0m \u001b[0mstr\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mfp\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mreadline\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0m_MAXLINE\u001b[0m \u001b[1;33m+\u001b[0m \u001b[1;36m1\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m,\u001b[0m \u001b[1;34m\"iso-8859-1\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    269\u001b[0m         \u001b[1;32mif\u001b[0m \u001b[0mlen\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mline\u001b[0m\u001b[1;33m)\u001b[0m \u001b[1;33m>\u001b[0m \u001b[0m_MAXLINE\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    270\u001b[0m             \u001b[1;32mraise\u001b[0m \u001b[0mLineTooLong\u001b[0m\u001b[1;33m(\u001b[0m\u001b[1;34m\"status line\"\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;32m~\\Anaconda3\\lib\\socket.py\u001b[0m in \u001b[0;36mreadinto\u001b[1;34m(self, b)\u001b[0m\n\u001b[0;32m    667\u001b[0m         \u001b[1;32mwhile\u001b[0m \u001b[1;32mTrue\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    668\u001b[0m             \u001b[1;32mtry\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[1;32m--> 669\u001b[1;33m                 \u001b[1;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_sock\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0mrecv_into\u001b[0m\u001b[1;33m(\u001b[0m\u001b[0mb\u001b[0m\u001b[1;33m)\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0m\u001b[0;32m    670\u001b[0m             \u001b[1;32mexcept\u001b[0m \u001b[0mtimeout\u001b[0m\u001b[1;33m:\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n\u001b[0;32m    671\u001b[0m                 \u001b[0mself\u001b[0m\u001b[1;33m.\u001b[0m\u001b[0m_timeout_occurred\u001b[0m \u001b[1;33m=\u001b[0m \u001b[1;32mTrue\u001b[0m\u001b[1;33m\u001b[0m\u001b[1;33m\u001b[0m\u001b[0m\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# create empty df to store fight details\n", "all_fight_details_df = pd.DataFrame(columns=config['fight_details_column_names'])\n", "\n", "# loop through each event and parse fight details\n", "for url in tqdm_notebook(list_of_events_urls):\n", "\n", "    # get soup\n", "    soup = LIB.get_soup(url)\n", "\n", "    # parse fight links\n", "    fight_details_df = LIB.parse_fight_details(soup)\n", "    \n", "    # concat fight details\n", "    all_fight_details_df = pd.concat([all_fight_details_df, fight_details_df])\n", "\n", "# show all fight details\n", "display(all_fight_details_df)\n", "\n", "# write fight details to file\n", "all_fight_details_df.to_csv(config['fight_details_file_name'], index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse Fight Results and Fight Stats\n", "\n", "Fight Results Includes:\n", "<br>\n", "Event\n", "<br>\n", "Bout\n", "<br>\n", "Weightclass\n", "<br>\n", "Method\n", "<br>\n", "Round\n", "<br>\n", "Time\n", "<br>\n", "Time Format\n", "<br>\n", "Referee\n", "<br>\n", "Details\n", "<br>\n", "\n", "Fight Stats Includes:\n", "<br>\n", "Event\n", "<br>\n", "Bout\n", "<br>\n", "Round\n", "<br>\n", "Fighter\n", "<br>\n", "Kd\n", "<br>\n", "Sig.Str.\n", "<br>\n", "Sig.Str. %\n", "<br>\n", "Total Str.\n", "<br>\n", "Td\n", "<br>\n", "Td %\n", "<br>\n", "Sub.Att\n", "<br>\n", "Rev.\n", "<br>\n", "Ctrl\n", "<br>\n", "Head\n", "<br>\n", "Body\n", "<br>\n", "Leg\n", "<br>\n", "Distance\n", "<br>\n", "Clinch\n", "<br>\n", "Ground\n", "<br>"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# define list of urls of fights to parse\n", "list_of_fight_details_urls = list(all_fight_details_df['URL'])"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "620d7e6d49f34c59892364a717efaa75", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/299 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EVENT</th>\n", "      <th>BOUT</th>\n", "      <th>OUTCOME</th>\n", "      <th>WEIGHTCLASS</th>\n", "      <th>METHOD</th>\n", "      <th>ROUND</th>\n", "      <th>TIME</th>\n", "      <th>TIME FORMAT</th>\n", "      <th>REFEREE</th>\n", "      <th>DETAILS</th>\n", "      <th>URL</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON>  vs. <PERSON></td>\n", "      <td>W/L</td>\n", "      <td>UFC Light Heavyweight Title Bout</td>\n", "      <td>Decision - Unanimous</td>\n", "      <td>5</td>\n", "      <td>5:00</td>\n", "      <td>5 Rnd (5-5-5-5-5)</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON> 43 - 50.<PERSON> 44 - 50.<PERSON>...</td>\n", "      <td>http://ufcstats.com/fight-details/ea64af61ed03...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON><PERSON>  vs. <PERSON></td>\n", "      <td>W/L</td>\n", "      <td>UFC Lightweight Title Bout</td>\n", "      <td>Decision - Unanimous</td>\n", "      <td>5</td>\n", "      <td>5:00</td>\n", "      <td>5 Rnd (5-5-5-5-5)</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON> 47 - 49.<PERSON> 47 - 48.<PERSON> ...</td>\n", "      <td>http://ufcstats.com/fight-details/970c2beda729...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON>  vs. <PERSON><PERSON><PERSON></td>\n", "      <td>W/L</td>\n", "      <td>Light Heavyweight Bout</td>\n", "      <td>Decision - Unanimous</td>\n", "      <td>3</td>\n", "      <td>5:00</td>\n", "      <td>3 Rnd (5-5-5)</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON> 27 - 29.<PERSON> 28 - 29.<PERSON>...</td>\n", "      <td>http://ufcstats.com/fight-details/a6a4be467d12...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON>  vs. <PERSON></td>\n", "      <td>W/L</td>\n", "      <td>Welterweight Bout</td>\n", "      <td>Decision - Majority</td>\n", "      <td>3</td>\n", "      <td>5:00</td>\n", "      <td>3 Rnd (5-5-5)</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON> 29 - 29.<PERSON> 27 - 30.<PERSON> ...</td>\n", "      <td>http://ufcstats.com/fight-details/3b63f4fb2469...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON>  vs. <PERSON></td>\n", "      <td>W/L</td>\n", "      <td>UFC Middleweight Title Bout</td>\n", "      <td>Decision - Unanimous</td>\n", "      <td>5</td>\n", "      <td>5:00</td>\n", "      <td>5 Rnd (5-5-5-5-5)</td>\n", "      <td><PERSON></td>\n", "      <td>Point Deducted: <PERSON><PERSON><PERSON> by MenneTony Week...</td>\n", "      <td>http://ufcstats.com/fight-details/37cb7ce0f0b7...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 2: No Way Out</td>\n", "      <td><PERSON>  vs. <PERSON></td>\n", "      <td>W/L</td>\n", "      <td>Open Weight Bout</td>\n", "      <td>KO/TKO</td>\n", "      <td>1</td>\n", "      <td>2:50</td>\n", "      <td>No Time Limit</td>\n", "      <td><PERSON></td>\n", "      <td>toCorner Stoppage</td>\n", "      <td>http://ufcstats.com/fight-details/3b020d4914b4...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 2: No Way Out</td>\n", "      <td><PERSON>  vs. <PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>W/L</td>\n", "      <td>Open Weight Bout</td>\n", "      <td>Submission</td>\n", "      <td>1</td>\n", "      <td>4:52</td>\n", "      <td>No Time Limit</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON> From Half Guard</td>\n", "      <td>http://ufcstats.com/fight-details/d917c8c7461b...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 2: No Way Out</td>\n", "      <td><PERSON>  vs. <PERSON></td>\n", "      <td>W/L</td>\n", "      <td>Open Weight Bout</td>\n", "      <td>KO/TKO</td>\n", "      <td>1</td>\n", "      <td>12:13</td>\n", "      <td>No Time Limit</td>\n", "      <td><PERSON></td>\n", "      <td>Punches to Head From GuardSubmission to Strikes</td>\n", "      <td>http://ufcstats.com/fight-details/ccee020be2e8...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 2: No Way Out</td>\n", "      <td><PERSON>  vs. <PERSON></td>\n", "      <td>W/L</td>\n", "      <td>Open Weight Bout</td>\n", "      <td>Submission</td>\n", "      <td>1</td>\n", "      <td>0:58</td>\n", "      <td>No Time Limit</td>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/4b9ae533ccb3...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 2: No Way Out</td>\n", "      <td><PERSON>  vs. <PERSON></td>\n", "      <td>W/L</td>\n", "      <td>Open Weight Bout</td>\n", "      <td>Submission</td>\n", "      <td>1</td>\n", "      <td>0:20</td>\n", "      <td>No Time Limit</td>\n", "      <td><PERSON></td>\n", "      <td>Guillotine Choke From Mount</td>\n", "      <td>http://ufcstats.com/fight-details/4acab67848e7...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>299 rows × 11 columns</p>\n", "</div>"], "text/plain": ["                        EVENT                                   BOUT OUTCOME  \\\n", "0   UFC 33: Victory in Vegas   <PERSON>  vs. <PERSON>      W/L   \n", "0   UFC 33: Victory in Vegas        Jens <PERSON>  vs. <PERSON>      W/L   \n", "0   UFC 33: Victory in Vegas   <PERSON>  vs. <PERSON><PERSON><PERSON>      W/L   \n", "0   UFC 33: Victory in Vegas           <PERSON>  vs. <PERSON>      W/L   \n", "0   UFC 33: Victory in Vegas           <PERSON>  vs. <PERSON>      W/L   \n", "..                        ...                                    ...     ...   \n", "0          UFC 2: No Way Out     <PERSON>  vs. <PERSON>      W/L   \n", "0          UFC 2: No Way Out     <PERSON>  vs. <PERSON><PERSON><PERSON><PERSON> Luster      W/L   \n", "0          UFC 2: <PERSON> Way Out       <PERSON>  vs. <PERSON>      W/L   \n", "0          UFC 2: <PERSON> Way Out          <PERSON>  vs. <PERSON>      W/L   \n", "0          UFC 2: <PERSON> Way Out       <PERSON>  vs. <PERSON>      W/L   \n", "\n", "                         WEIGHTCLASS                 METHOD ROUND   TIME  \\\n", "0   UFC Light Heavyweight Title <PERSON><PERSON>  Decision - Unanimous      5   5:00   \n", "0         UFC Lightweight Title <PERSON><PERSON>  Decision - Unanimous      5   5:00   \n", "0             Light Heavyweight Bout  Decision - Unanimous      3   5:00   \n", "0                  We<PERSON> <PERSON><PERSON>   Decision - Majority      3   5:00   \n", "0        UFC Middleweight Title <PERSON><PERSON>  Decision - Unanimous      5   5:00   \n", "..                               ...                    ...   ...    ...   \n", "0                   Open Weight Bout                KO/TKO      1   2:50   \n", "0                   Open Weight Bout            Submission      1   4:52   \n", "0                   Open Weight Bout                KO/TKO      1  12:13   \n", "0                   Open Weight Bout            Submission      1   0:58   \n", "0                   Open Weight Bout            Submission      1   0:20   \n", "\n", "          TIME FORMAT         REFEREE  \\\n", "0   5 Rnd (5-5-5-5-5)   <PERSON>   \n", "0   5 Rnd (5-5-5-5-5)  <PERSON>   \n", "0       3 <PERSON><PERSON> (5-5-5)   <PERSON>   \n", "0       3 <PERSON><PERSON> (5-5-5)  <PERSON>   \n", "0   5 Rnd (5-5-5-5-5)   <PERSON>   \n", "..                ...             ...   \n", "0       No Time Limit   <PERSON>   \n", "0       No Time Limit   <PERSON>   \n", "0       No Time Limit   <PERSON>   \n", "0       No Time Limit   <PERSON>   \n", "0       No Time Limit   <PERSON>   \n", "\n", "                                              DETAILS  \\\n", "0   <PERSON> 43 - 50.<PERSON> 44 - 50.<PERSON>...   \n", "0   <PERSON> 47 - 49.<PERSON> 47 - 48.<PERSON> ...   \n", "0   <PERSON> 27 - 29.<PERSON> 28 - 29.<PERSON>..   \n", "0   <PERSON> 29 - 29.<PERSON> 27 - 30.<PERSON> ...   \n", "0   Point Deducted: Illegal <PERSON> by MenneTony Week...   \n", "..                                                ...   \n", "0                                   toCorner Stoppage   \n", "0                            <PERSON><PERSON> From Half Guard    \n", "0     Punches to Head From GuardSubmission to Strikes   \n", "0                          Guillotine Choke Standing    \n", "0                        Guillotine Choke From Mount    \n", "\n", "                                                  URL  \n", "0   http://ufcstats.com/fight-details/ea64af61ed03...  \n", "0   http://ufcstats.com/fight-details/970c2beda729...  \n", "0   http://ufcstats.com/fight-details/a6a4be467d12...  \n", "0   http://ufcstats.com/fight-details/3b63f4fb2469...  \n", "0   http://ufcstats.com/fight-details/37cb7ce0f0b7...  \n", "..                                                ...  \n", "0   http://ufcstats.com/fight-details/3b020d4914b4...  \n", "0   http://ufcstats.com/fight-details/d917c8c7461b...  \n", "0   http://ufcstats.com/fight-details/ccee020be2e8...  \n", "0   http://ufcstats.com/fight-details/4b9ae533ccb3...  \n", "0   http://ufcstats.com/fight-details/4acab67848e7...  \n", "\n", "[299 rows x 11 columns]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EVENT</th>\n", "      <th>BOUT</th>\n", "      <th>ROUND</th>\n", "      <th>FIGHTER</th>\n", "      <th>KD</th>\n", "      <th>SIG.STR.</th>\n", "      <th>SIG.STR. %</th>\n", "      <th>TOTAL STR.</th>\n", "      <th>TD</th>\n", "      <th>TD %</th>\n", "      <th>SUB.ATT</th>\n", "      <th>REV.</th>\n", "      <th>CTRL</th>\n", "      <th>HEAD</th>\n", "      <th>BODY</th>\n", "      <th>LEG</th>\n", "      <th>DISTANCE</th>\n", "      <th>CLINCH</th>\n", "      <th>GROUND</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 1</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>13 of 15</td>\n", "      <td>86%</td>\n", "      <td>27 of 29</td>\n", "      <td>1 of 1</td>\n", "      <td>100%</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4:29</td>\n", "      <td>2 of 4</td>\n", "      <td>3 of 3</td>\n", "      <td>8 of 8</td>\n", "      <td>0 of 0</td>\n", "      <td>13 of 14</td>\n", "      <td>0 of 1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 2</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>32 of 34</td>\n", "      <td>94%</td>\n", "      <td>32 of 34</td>\n", "      <td>1 of 4</td>\n", "      <td>25%</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4:15</td>\n", "      <td>0 of 2</td>\n", "      <td>2 of 2</td>\n", "      <td>30 of 30</td>\n", "      <td>1 of 2</td>\n", "      <td>31 of 32</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 3</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>7 of 15</td>\n", "      <td>46%</td>\n", "      <td>62 of 71</td>\n", "      <td>1 of 1</td>\n", "      <td>100%</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4:30</td>\n", "      <td>7 of 15</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>1 of 6</td>\n", "      <td>0 of 0</td>\n", "      <td>6 of 9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 4</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>5 of 9</td>\n", "      <td>55%</td>\n", "      <td>54 of 58</td>\n", "      <td>1 of 2</td>\n", "      <td>50%</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>4:37</td>\n", "      <td>3 of 7</td>\n", "      <td>2 of 2</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 1</td>\n", "      <td>2 of 2</td>\n", "      <td>3 of 6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 5</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>7 of 11</td>\n", "      <td>63%</td>\n", "      <td>73 of 77</td>\n", "      <td>1 of 1</td>\n", "      <td>100%</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3:43</td>\n", "      <td>5 of 9</td>\n", "      <td>1 of 1</td>\n", "      <td>1 of 1</td>\n", "      <td>3 of 6</td>\n", "      <td>1 of 1</td>\n", "      <td>3 of 4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 2: No Way Out</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 1</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>4 of 5</td>\n", "      <td>80%</td>\n", "      <td>95 of 102</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>--</td>\n", "      <td>4 of 5</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>1 of 2</td>\n", "      <td>2 of 2</td>\n", "      <td>1 of 1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 2: No Way Out</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 1</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>1 of 1</td>\n", "      <td>100%</td>\n", "      <td>1 of 1</td>\n", "      <td>0 of 1</td>\n", "      <td>0%</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>--</td>\n", "      <td>0 of 0</td>\n", "      <td>1 of 1</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>1 of 1</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 2: No Way Out</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 1</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>1 of 1</td>\n", "      <td>100%</td>\n", "      <td>2 of 2</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>--</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>1 of 1</td>\n", "      <td>1 of 1</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 2: No Way Out</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 1</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>1 of 1</td>\n", "      <td>100%</td>\n", "      <td>2 of 2</td>\n", "      <td>1 of 1</td>\n", "      <td>100%</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>--</td>\n", "      <td>1 of 1</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>1 of 1</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 2: No Way Out</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 1</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>0 of 4</td>\n", "      <td>0%</td>\n", "      <td>1 of 5</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>--</td>\n", "      <td>0 of 2</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 2</td>\n", "      <td>0 of 3</td>\n", "      <td>0 of 1</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>900 rows × 19 columns</p>\n", "</div>"], "text/plain": ["                       EVENT                                 BOUT    ROUND  \\\n", "0   UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 1   \n", "1   UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 2   \n", "2   UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 3   \n", "3   UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 4   \n", "4   UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 5   \n", "..                       ...                                  ...      ...   \n", "0          UFC 2: <PERSON> Way Out      <PERSON> vs. <PERSON>  Round 1   \n", "0          UFC 2: <PERSON> Way Out         <PERSON> vs. <PERSON>  Round 1   \n", "0          UFC 2: <PERSON> Way Out         <PERSON> vs. <PERSON>  Round 1   \n", "0          UFC 2: <PERSON> Way Out      <PERSON> vs. <PERSON>  Round 1   \n", "0          UFC 2: <PERSON> Way Out      <PERSON> vs. <PERSON>  Round 1   \n", "\n", "           FIGHTER KD  SIG.STR. SIG.STR. % TOTAL STR.      TD  TD % SUB.ATT  \\\n", "0       <PERSON>  0  13 of 15        86%   27 of 29  1 of 1  100%       0   \n", "1       <PERSON>  0  32 of 34        94%   32 of 34  1 of 4   25%       0   \n", "2       <PERSON>  0   7 of 15        46%   62 of 71  1 of 1  100%       0   \n", "3       <PERSON>  0    5 of 9        55%   54 of 58  1 of 2   50%       0   \n", "4       <PERSON>  0   7 of 11        63%   73 of 77  1 of 1  100%       0   \n", "..             ... ..       ...        ...        ...     ...   ...     ...   \n", "0    <PERSON>  0    4 of 5        80%  95 of 102  0 of 0   ---       0   \n", "0    <PERSON>  0    1 of 1       100%     1 of 1  0 of 1    0%       1   \n", "0       <PERSON>  0    1 of 1       100%     2 of 2  0 of 0   ---       0   \n", "0     <PERSON>  0    1 of 1       100%     2 of 2  1 of 1  100%       1   \n", "0   <PERSON>  0    0 of 4         0%     1 of 5  0 of 0   ---       0   \n", "\n", "   REV.  CTRL     HEAD    BODY       LEG DISTANCE    CLINCH  GROUND  \n", "0     0  4:29   2 of 4  3 of 3    8 of 8   0 of 0  13 of 14  0 of 1  \n", "1     0  4:15   0 of 2  2 of 2  30 of 30   1 of 2  31 of 32  0 of 0  \n", "2     0  4:30  7 of 15  0 of 0    0 of 0   1 of 6    0 of 0  6 of 9  \n", "3     0  4:37   3 of 7  2 of 2    0 of 0   0 of 1    2 of 2  3 of 6  \n", "4     0  3:43   5 of 9  1 of 1    1 of 1   3 of 6    1 of 1  3 of 4  \n", "..  ...   ...      ...     ...       ...      ...       ...     ...  \n", "0     0    --   4 of 5  0 of 0    0 of 0   1 of 2    2 of 2  1 of 1  \n", "0     0    --   0 of 0  1 of 1    0 of 0   0 of 0    1 of 1  0 of 0  \n", "0     0    --   0 of 0  0 of 0    1 of 1   1 of 1    0 of 0  0 of 0  \n", "0     0    --   1 of 1  0 of 0    0 of 0   0 of 0    1 of 1  0 of 0  \n", "0     0    --   0 of 2  0 of 0    0 of 2   0 of 3    0 of 1  0 of 0  \n", "\n", "[900 rows x 19 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# create empty df to store fight results\n", "all_fight_results_df = pd.DataFrame(columns=config['fight_results_column_names'])\n", "# create empty df to store fight stats\n", "all_fight_stats_df = pd.DataFrame(columns=config['fight_stats_column_names'])\n", "\n", "# loop through each fight and parse fight results and stats\n", "for url in tqdm_notebook(list_of_fight_details_urls):\n", "\n", "    # get soup\n", "    soup = LIB.get_soup(url)\n", "\n", "    # parse fight results and fight stats\n", "    fight_results_df, fight_stats_df = LIB.parse_organise_fight_results_and_stats(\n", "        soup,\n", "        url,\n", "        config['fight_results_column_names'],\n", "        config['totals_column_names'],\n", "        config['significant_strikes_column_names']\n", "        )\n", "\n", "    # concat fight results\n", "    all_fight_results_df = pd.concat([all_fight_results_df, fight_results_df])\n", "    # concat fight stats\n", "    all_fight_stats_df = pd.concat([all_fight_stats_df, fight_stats_df])\n", "\n", "# show all fight results\n", "display(all_fight_results_df)\n", "# show all fight stats\n", "display(all_fight_stats_df)\n", "\n", "# write to file\n", "all_fight_results_df.to_csv(config['fight_results_file_name'], index=False)\n", "# write to file\n", "all_fight_stats_df.to_csv(config['fight_stats_file_name'], index=False)"]}], "metadata": {"interpreter": {"hash": "15747ae626c9cfad767687a667802efd987d29cfb104f39ef23d4cc739083892"}, "kernelspec": {"display_name": "Python 3.8.5 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}