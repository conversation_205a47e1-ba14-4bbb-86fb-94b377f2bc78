{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["'''\n", "Overview\n", "this notebook parses all fighters' details and tale of the tape\n", "\n", "scrape ufc fighters' details\n", "includes first, last, nickname, url\n", "from url scrape scrape fighter's tale of the tape, \n", "includes fighter, height, weight, reach, stance, dob\n", "'''"]}, {"cell_type": "code", "execution_count": 90, "metadata": {}, "outputs": [], "source": ["# imports\n", "import pandas as pd\n", "import numpy as np\n", "from tqdm.notebook import tqdm_notebook\n", "\n", "# import library\n", "import scrape_ufc_stats_library as LIB\n", "import importlib\n", "importlib.reload(LIB)\n", "\n", "# import configs\n", "import yaml\n", "config = yaml.safe_load(open('scrape_ufc_stats_config.yaml'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse Fighter Details\n", "Includes:\n", "<br>\n", "First\n", "<br>\n", "Last\n", "<br>\n", "Nickname\n", "<br>\n", "URL"]}, {"cell_type": "code", "execution_count": 91, "metadata": {}, "outputs": [], "source": ["# generate list of urls for fighter details\n", "list_of_alphabetical_urls = LIB.generate_alphabetical_urls()"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "45062510ee91416dbf377cfa4f831411", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/26 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FIRST</th>\n", "      <th>LAST</th>\n", "      <th>NICKNAME</th>\n", "      <th>URL</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Tom</td>\n", "      <td><PERSON></td>\n", "      <td></td>\n", "      <td>http://ufcstats.com/fighter-details/93fe7332d1...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>The Assassin</td>\n", "      <td>http://ufcstats.com/fighter-details/15df64c02b...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>David</td>\n", "      <td><PERSON></td>\n", "      <td>Tank</td>\n", "      <td>http://ufcstats.com/fighter-details/b361180739...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/fighter-details/2f5cbecbbe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Abe</td>\n", "      <td><PERSON></td>\n", "      <td>http://ufcstats.com/fighter-details/c0ed7b2081...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>Dave</td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td></td>\n", "      <td>http://ufcstats.com/fighter-details/be124bdd60...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td><PERSON></td>\n", "      <td>Zuniga</td>\n", "      <td></td>\n", "      <td>http://ufcstats.com/fighter-details/02d808afb9...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td><PERSON></td>\n", "      <td>Zuniga</td>\n", "      <td></td>\n", "      <td>http://ufcstats.com/fighter-details/1291dd6b8a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td><PERSON></td>\n", "      <td>Zuniga</td>\n", "      <td>Tigre</td>\n", "      <td>http://ufcstats.com/fighter-details/523af801b3...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>RezDog</td>\n", "      <td>http://ufcstats.com/fighter-details/0c277f3ff6...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3737 rows × 4 columns</p>\n", "</div>"], "text/plain": ["       FIRST          LAST      NICKNAME  \\\n", "0        <PERSON>                 \n", "1      <PERSON> Assassin   \n", "2      <PERSON>   \n", "3     <PERSON><PERSON><PERSON>k   \n", "4   <PERSON><PERSON><PERSON>   \n", "..       ...           ...           ...   \n", "27      <PERSON>                 \n", "28      <PERSON>                 \n", "29    <PERSON>                 \n", "30     <PERSON>   \n", "31    <PERSON>   \n", "\n", "                                                  URL  \n", "0   http://ufcstats.com/fighter-details/93fe7332d1...  \n", "1   http://ufcstats.com/fighter-details/15df64c02b...  \n", "2   http://ufcstats.com/fighter-details/b361180739...  \n", "3   http://ufcstats.com/fighter-details/2f5cbecbbe...  \n", "4   http://ufcstats.com/fighter-details/c0ed7b2081...  \n", "..                                                ...  \n", "27  http://ufcstats.com/fighter-details/be124bdd60...  \n", "28  http://ufcstats.com/fighter-details/02d808afb9...  \n", "29  http://ufcstats.com/fighter-details/1291dd6b8a...  \n", "30  http://ufcstats.com/fighter-details/523af801b3...  \n", "31  http://ufcstats.com/fighter-details/0c277f3ff6...  \n", "\n", "[3737 rows x 4 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# create empty dataframe to store all fighter details\n", "all_fighter_details_df = pd.DataFrame()\n", "\n", "# loop through list of alphabetical urls\n", "for url in tqdm_notebook(list_of_alphabetical_urls):\n", "    # get soup\n", "    soup = LIB.get_soup(url)\n", "    # parse fighter details\n", "    fighter_details_df = LIB.parse_fighter_details(soup, config['fighter_details_column_names'])\n", "    # concat fighter_details_df to all_fighter_details_df\n", "    all_fighter_details_df = pd.concat([all_fighter_details_df, fighter_details_df])\n", "\n", "# show all fighter details\n", "display(all_fighter_details_df)\n", "\n", "# write to file\n", "all_fighter_details_df.to_csv(config['fighter_details_file_name'], index=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse Fighter Tale of the Tape\n", "\n", "Includes:\n", "<br>\n", "Fighter\n", "<br>\n", "Height\n", "<br>\n", "Weight\n", "<br>\n", "Reach\n", "<br>\n", "<PERSON><PERSON>\n", "<br>\n", "DOB\n", "<br>\n", "URL"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [], "source": ["# define list of urls of fighters to parse\n", "list_of_fighter_urls = list(all_fighter_details_df['URL'])"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "09f3ef2507dd4144b9bcaba32710fcf8", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3737 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FIGHTER</th>\n", "      <th>HEIGHT</th>\n", "      <th>WEIGHT</th>\n", "      <th>R<PERSON><PERSON></th>\n", "      <th>STANCE</th>\n", "      <th>DOB</th>\n", "      <th>URL</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>--</td>\n", "      <td>155 lbs.</td>\n", "      <td>--</td>\n", "      <td></td>\n", "      <td>Jul 13, 1978</td>\n", "      <td>http://ufcstats.com/fighter-details/93fe7332d1...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>5' 11\"</td>\n", "      <td>155 lbs.</td>\n", "      <td>--</td>\n", "      <td>Orthodox</td>\n", "      <td>Jul 03, 1983</td>\n", "      <td>http://ufcstats.com/fighter-details/15df64c02b...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>6' 0\"</td>\n", "      <td>265 lbs.</td>\n", "      <td>--</td>\n", "      <td>Switch</td>\n", "      <td>--</td>\n", "      <td>http://ufcstats.com/fighter-details/b361180739...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>6' 3\"</td>\n", "      <td>235 lbs.</td>\n", "      <td>76\"</td>\n", "      <td>Orthodox</td>\n", "      <td>Sep 02, 1981</td>\n", "      <td>http://ufcstats.com/fighter-details/2f5cbecbbe...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>5' 6\"</td>\n", "      <td>145 lbs.</td>\n", "      <td>--</td>\n", "      <td>Orthodox</td>\n", "      <td>--</td>\n", "      <td>http://ufcstats.com/fighter-details/c0ed7b2081...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>--</td>\n", "      <td>170 lbs.</td>\n", "      <td>--</td>\n", "      <td></td>\n", "      <td>Mar 05, 1980</td>\n", "      <td>http://ufcstats.com/fighter-details/be124bdd60...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>--</td>\n", "      <td>145 lbs.</td>\n", "      <td>--</td>\n", "      <td></td>\n", "      <td>--</td>\n", "      <td>http://ufcstats.com/fighter-details/02d808afb9...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>5' 9\"</td>\n", "      <td>185 lbs.</td>\n", "      <td>--</td>\n", "      <td></td>\n", "      <td>--</td>\n", "      <td>http://ufcstats.com/fighter-details/1291dd6b8a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>5' 7\"</td>\n", "      <td>155 lbs.</td>\n", "      <td>70\"</td>\n", "      <td>Orthodox</td>\n", "      <td>Apr 04, 1992</td>\n", "      <td>http://ufcstats.com/fighter-details/523af801b3...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>6' 2\"</td>\n", "      <td>205 lbs.</td>\n", "      <td>74\"</td>\n", "      <td></td>\n", "      <td>Jun 26, 1982</td>\n", "      <td>http://ufcstats.com/fighter-details/0c277f3ff6...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>3737 rows × 7 columns</p>\n", "</div>"], "text/plain": ["                FIGHTER  HEIGHT    WEIGHT REACH    STANCE           DOB  \\\n", "0             <PERSON>      --  155 lbs.    --            Jul 13, 1978   \n", "0          <PERSON>  5' 11\"  155 lbs.    --  Orthodox  Jul 03, 1983   \n", "0          <PERSON>   6' 0\"  265 lbs.    --    Switch            --   \n", "0   <PERSON><PERSON><PERSON>   6' 3\"  235 lbs.   76\"  Orthodox  Sep 02, 1981   \n", "0          <PERSON><PERSON><PERSON>   5' 6\"  145 lbs.    --  Orthodox            --   \n", "..                  ...     ...       ...   ...       ...           ...   \n", "0         <PERSON>      --  170 lbs.    --            Mar 05, 1980   \n", "0           <PERSON>      --  145 lbs.    --                      --   \n", "0         <PERSON>   5' 9\"  185 lbs.    --                      --   \n", "0          <PERSON>   5' 7\"  155 lbs.   70\"  Orthodox  Apr 04, 1992   \n", "0        <PERSON>   6' 2\"  205 lbs.   74\"            Jun 26, 1982   \n", "\n", "                                                  URL  \n", "0   http://ufcstats.com/fighter-details/93fe7332d1...  \n", "0   http://ufcstats.com/fighter-details/15df64c02b...  \n", "0   http://ufcstats.com/fighter-details/b361180739...  \n", "0   http://ufcstats.com/fighter-details/2f5cbecbbe...  \n", "0   http://ufcstats.com/fighter-details/c0ed7b2081...  \n", "..                                                ...  \n", "0   http://ufcstats.com/fighter-details/be124bdd60...  \n", "0   http://ufcstats.com/fighter-details/02d808afb9...  \n", "0   http://ufcstats.com/fighter-details/1291dd6b8a...  \n", "0   http://ufcstats.com/fighter-details/523af801b3...  \n", "0   http://ufcstats.com/fighter-details/0c277f3ff6...  \n", "\n", "[3737 rows x 7 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# create empty df to store fighters' tale of the tape\n", "all_fighter_tott_df = pd.DataFrame(columns=config['fighter_tott_column_names'])\n", "\n", "# loop through list_of_fighter_urls\n", "for url in tqdm_notebook(list_of_fighter_urls):\n", "    # get soup\n", "    soup = LIB.get_soup(url)\n", "    # parse fighter tale of the tape\n", "    fighter_tott = LIB.parse_fighter_tott(soup)\n", "    # organise fighter tale of the tape\n", "    fighter_tott_df = LIB.organise_fighter_tott(fighter_tott, config['fighter_tott_column_names'], url)\n", "    # concat fighter\n", "    all_fighter_tott_df = pd.concat([all_fighter_tott_df, fighter_tott_df])\n", "\n", "# show all fighters' tale of the tape\n", "display(all_fighter_tott_df)\n", "\n", "# write to file\n", "all_fighter_tott_df.to_csv(config['fighter_tott_file_name'], index=False)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "9aac8944d03ded7dbafcde28cbfe34d23c9079c2df9125865de5144d9ea8ac9a"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}