{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\nOverview\\nthis notebook is a working example of scraping ufc fight stats one at a time\\nthe code is broken down into small steps that can be run in parts to view and verify results at each step\\nthis is useful for testing and debugging the code as the ufc stats website may be update, breaking the code\\n\\n\\nscrape ufc fight stats\\nget all event details, name, url, date, location for all ufc events\\nfor each event, get fight details all fights on card\\nparse each fight to get fight stats of both fighters\\n'"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["'''\n", "Overview\n", "this notebook is a working example of scraping ufc fight stats one at a time\n", "the code is broken down into small steps that can be run in parts to view and verify results at each step\n", "this is useful for testing and debugging the code as the ufc stats website may be update, breaking the code\n", "\n", "\n", "scrape ufc fight stats\n", "get all event details, name, url, date, location for all ufc events\n", "for each event, get fight details all fights on card\n", "parse each fight to get fight stats of both fighters\n", "\n", "additional scraping of fighter's details, name, nickname, url\n", "for each fighter, get their tale of the tape\n", "'''"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["# imports\n", "import pandas as pd\n", "import numpy as np\n", "import os\n", "import re\n", "import requests\n", "from bs4 import BeautifulSoup\n", "import itertools\n", "\n", "# import library\n", "import scrape_ufc_stats_library as LIB\n", "import importlib\n", "importlib.reload(LIB)\n", "\n", "# import configs\n", "import yaml\n", "config = yaml.safe_load(open('scrape_ufc_stats_config.yaml'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse Event Details\n", "Includes:\n", "<br>\n", "Event\n", "<br>\n", "URL\n", "<br>\n", "Date\n", "<br>\n", "Location\n", "<br>"]}, {"cell_type": "code", "execution_count": 146, "metadata": {}, "outputs": [], "source": ["# define url to parse\n", "url = 'http://ufcstats.com/statistics/events/completed' # first page\n", "# url = 'http://ufcstats.com/statistics/events/completed?page=all' # all pages\n", "\n", "# get soup\n", "soup = LIB.get_soup(url)"]}, {"cell_type": "code", "execution_count": 147, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EVENT</th>\n", "      <th>URL</th>\n", "      <th>DATE</th>\n", "      <th>LOCATION</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/f5585e675af7...</td>\n", "      <td>January 15, 2022</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>UFC Fight Night: <PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/2a470ad41c22...</td>\n", "      <td>December 18, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>UFC 269: <PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/ef927e4fe211...</td>\n", "      <td>December 11, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/509697e08673...</td>\n", "      <td>December 04, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/event-details/c95fbc085e17...</td>\n", "      <td>November 20, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>UFC Fight Night: <PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/event-details/b5abaa65f879...</td>\n", "      <td>November 13, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>UFC 268: <PERSON><PERSON> vs. <PERSON><PERSON> 2</td>\n", "      <td>http://ufcstats.com/event-details/48e093ea1f43...</td>\n", "      <td>November 06, 2021</td>\n", "      <td>New York City, New York, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>UFC 267: <PERSON><PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/3974fa35c917...</td>\n", "      <td>October 30, 2021</td>\n", "      <td>Abu Dhabi, Abu Dhabi, United Arab Emirates</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>UFC Fight Night: <PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/8a9c6c4301f6...</td>\n", "      <td>October 23, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/8f4616698508...</td>\n", "      <td>October 16, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/event-details/d247691a6c0e...</td>\n", "      <td>October 09, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>UFC Fight Night: <PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/event-details/e15d0a2519d6...</td>\n", "      <td>October 02, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>UFC 266: <PERSON><PERSON><PERSON><PERSON> vs. Ortega</td>\n", "      <td>http://ufcstats.com/event-details/0eec866a0778...</td>\n", "      <td>September 25, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>UFC Fight Night: <PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/4c27ca8c8481...</td>\n", "      <td>September 18, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/event-details/b9532d815060...</td>\n", "      <td>September 04, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/0db9d2486d56...</td>\n", "      <td>August 28, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/2f13e4020cea...</td>\n", "      <td>August 21, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>UFC 265: <PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/972197a8c61a...</td>\n", "      <td>August 07, 2021</td>\n", "      <td>Houston, Texas, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>UFC Fight Night: <PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/320edfb0332b...</td>\n", "      <td>July 31, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/e57f0b06ea39...</td>\n", "      <td>July 24, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/40389d39a92f...</td>\n", "      <td>July 17, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>UFC 264: <PERSON><PERSON><PERSON> vs. <PERSON> 3</td>\n", "      <td>http://ufcstats.com/event-details/8a0be41c0380...</td>\n", "      <td>July 10, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/a1d6b308781a...</td>\n", "      <td>June 26, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>UFC Fight Night: <PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/event-details/9ddfb3369a3b...</td>\n", "      <td>June 19, 2021</td>\n", "      <td>Las Vegas, Nevada, USA</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                       EVENT  \\\n", "0       UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON>   \n", "1         UFC Fight Night: <PERSON> vs. <PERSON><PERSON><PERSON>   \n", "2              UFC 269: <PERSON> vs. <PERSON><PERSON><PERSON>   \n", "3             UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON>   \n", "4           UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON>   \n", "5    UFC Fight Night: <PERSON> vs. <PERSON>   \n", "6             UFC 268: <PERSON><PERSON> vs. <PERSON><PERSON> 2   \n", "7           UFC 267: <PERSON><PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON>   \n", "8         UFC Fight Night: <PERSON> vs. <PERSON><PERSON><PERSON>   \n", "9           UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON>   \n", "10       UFC Fight Night: <PERSON><PERSON> vs. <PERSON>   \n", "11        UFC Fight Night: <PERSON> vs. <PERSON>   \n", "12           UFC 266: <PERSON><PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON>   \n", "13          UFC Fight Night: <PERSON> vs. <PERSON><PERSON>   \n", "14         UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON>   \n", "15     UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON>   \n", "16   UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON><PERSON>   \n", "17                   UFC 265: <PERSON> vs. <PERSON><PERSON>   \n", "18      UFC Fight Night: <PERSON> vs. <PERSON><PERSON><PERSON>   \n", "19  UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON>   \n", "20     UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON>   \n", "21           UFC 264: <PERSON><PERSON><PERSON> vs. <PERSON> 3   \n", "22          UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON>   \n", "23             UFC Fight Night: <PERSON> vs. <PERSON><PERSON>   \n", "\n", "                                                  URL                DATE  \\\n", "0   http://ufcstats.com/event-details/f5585e675af7...    January 15, 2022   \n", "1   http://ufcstats.com/event-details/2a470ad41c22...   December 18, 2021   \n", "2   http://ufcstats.com/event-details/ef927e4fe211...   December 11, 2021   \n", "3   http://ufcstats.com/event-details/509697e08673...   December 04, 2021   \n", "4   http://ufcstats.com/event-details/c95fbc085e17...   November 20, 2021   \n", "5   http://ufcstats.com/event-details/b5abaa65f879...   November 13, 2021   \n", "6   http://ufcstats.com/event-details/48e093ea1f43...   November 06, 2021   \n", "7   http://ufcstats.com/event-details/3974fa35c917...    October 30, 2021   \n", "8   http://ufcstats.com/event-details/8a9c6c4301f6...    October 23, 2021   \n", "9   http://ufcstats.com/event-details/8f4616698508...    October 16, 2021   \n", "10  http://ufcstats.com/event-details/d247691a6c0e...    October 09, 2021   \n", "11  http://ufcstats.com/event-details/e15d0a2519d6...    October 02, 2021   \n", "12  http://ufcstats.com/event-details/0eec866a0778...  September 25, 2021   \n", "13  http://ufcstats.com/event-details/4c27ca8c8481...  September 18, 2021   \n", "14  http://ufcstats.com/event-details/b9532d815060...  September 04, 2021   \n", "15  http://ufcstats.com/event-details/0db9d2486d56...     August 28, 2021   \n", "16  http://ufcstats.com/event-details/2f13e4020cea...     August 21, 2021   \n", "17  http://ufcstats.com/event-details/972197a8c61a...     August 07, 2021   \n", "18  http://ufcstats.com/event-details/320edfb0332b...       July 31, 2021   \n", "19  http://ufcstats.com/event-details/e57f0b06ea39...       July 24, 2021   \n", "20  http://ufcstats.com/event-details/40389d39a92f...       July 17, 2021   \n", "21  http://ufcstats.com/event-details/8a0be41c0380...       July 10, 2021   \n", "22  http://ufcstats.com/event-details/a1d6b308781a...       June 26, 2021   \n", "23  http://ufcstats.com/event-details/9ddfb3369a3b...       June 19, 2021   \n", "\n", "                                      LOCATION  \n", "0                       Las Vegas, Nevada, USA  \n", "1                       Las Vegas, Nevada, USA  \n", "2                       Las Vegas, Nevada, USA  \n", "3                       Las Vegas, Nevada, USA  \n", "4                       Las Vegas, Nevada, USA  \n", "5                       Las Vegas, Nevada, USA  \n", "6                 New York City, New York, USA  \n", "7   Abu Dhabi, Abu Dhabi, United Arab Emirates  \n", "8                       Las Vegas, Nevada, USA  \n", "9                       Las Vegas, Nevada, USA  \n", "10                      Las Vegas, Nevada, USA  \n", "11                      Las Vegas, Nevada, USA  \n", "12                      Las Vegas, Nevada, USA  \n", "13                      Las Vegas, Nevada, USA  \n", "14                      Las Vegas, Nevada, USA  \n", "15                      Las Vegas, Nevada, USA  \n", "16                      Las Vegas, Nevada, USA  \n", "17                         Houston, Texas, USA  \n", "18                      Las Vegas, Nevada, USA  \n", "19                      Las Vegas, Nevada, USA  \n", "20                      Las Vegas, Nevada, USA  \n", "21                      Las Vegas, Nevada, USA  \n", "22                      Las Vegas, Nevada, USA  \n", "23                      Las Vegas, Nevada, USA  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# parse event details\n", "event_details_df = LIB.parse_event_details(soup)\n", "\n", "# show event details\n", "display(event_details_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse Fight Details\n", "Includes:\n", "<br>\n", "Event\n", "<br>\n", "Bout\n", "<br>\n", "URL"]}, {"cell_type": "code", "execution_count": 148, "metadata": {}, "outputs": [], "source": ["# parse one event for fight details\n", "\n", "# define url to parse\n", "url = 'http://ufcstats.com/event-details/509697e08673d2e5'\n", "# get soup\n", "soup = LIB.get_soup(url)"]}, {"cell_type": "code", "execution_count": 149, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EVENT</th>\n", "      <th>BOUT</th>\n", "      <th>URL</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/3109d1151f14...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/a38648a1c190...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/b3847772199e...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/5d64044b9850...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/9e5bbbacf1d9...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/657b7da9c893...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/723a8a8c82ca...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON><PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/376dddfa82de...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON> vs. <PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/405bded9cc7a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/3fc8afbb23b9...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON> vs. <PERSON><PERSON><PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/42b28adbed69...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/c74b03337a84...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON></td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>http://ufcstats.com/fight-details/1ce1a43ef68f...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                             EVENT                                     BOUT  \\\n", "0   UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON>   \n", "1   UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON>   \n", "2   UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON>   \n", "3   UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON>   \n", "4   UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON>   \n", "5   UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON>   \n", "6   UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON><PERSON>   \n", "7   UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON><PERSON><PERSON>   \n", "8   UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON><PERSON>   \n", "9   UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON>   \n", "10  UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON><PERSON><PERSON>   \n", "11  UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON>   \n", "12  UFC Fight Night: <PERSON><PERSON> vs. <PERSON><PERSON> vs. <PERSON>   \n", "\n", "                                                  URL  \n", "0   http://ufcstats.com/fight-details/3109d1151f14...  \n", "1   http://ufcstats.com/fight-details/a38648a1c190...  \n", "2   http://ufcstats.com/fight-details/b3847772199e...  \n", "3   http://ufcstats.com/fight-details/5d64044b9850...  \n", "4   http://ufcstats.com/fight-details/9e5bbbacf1d9...  \n", "5   http://ufcstats.com/fight-details/657b7da9c893...  \n", "6   http://ufcstats.com/fight-details/723a8a8c82ca...  \n", "7   http://ufcstats.com/fight-details/376dddfa82de...  \n", "8   http://ufcstats.com/fight-details/405bded9cc7a...  \n", "9   http://ufcstats.com/fight-details/3fc8afbb23b9...  \n", "10  http://ufcstats.com/fight-details/42b28adbed69...  \n", "11  http://ufcstats.com/fight-details/c74b03337a84...  \n", "12  http://ufcstats.com/fight-details/1ce1a43ef68f...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# parse fight links\n", "fight_details_df = LIB.parse_fight_details(soup)\n", "\n", "# show fight links\n", "display(fight_details_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse Fight Results and Stats"]}, {"cell_type": "code", "execution_count": 150, "metadata": {}, "outputs": [], "source": ["# define url to parse\n", "# various types of fights\n", "# url = 'http://ufcstats.com/fight-details/4b7ec02b39fc6f70' # one round finish\n", "# url = 'http://ufcstats.com/fight-details/8b3b38167060b1d7' # three rounds decision\n", "# url = 'http://ufcstats.com/fight-details/b22eab3aa1522f40' # three rounds finish\n", "# url = 'http://ufcstats.com/fight-details/3109d1151f149aaf' # five rounds decision\n", "# url = 'http://ufcstats.com/fight-details/d93c8c77e1091a16' # no stats\n", "# url = 'http://ufcstats.com/fight-details/c63edd25d2201a46' # draw\n", "url = 'http://ufcstats.com/fight-details/37cb7ce0f0b70640' # point deduction\n", "\n", "# get soup\n", "soup = LIB.get_soup(url)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Parse Fight Results\n", "\n", "Includes:\n", "<br>\n", "Event\n", "<br>\n", "Bout\n", "<br>\n", "Weightclass\n", "<br>\n", "Method\n", "<br>\n", "Round\n", "<br>\n", "Time\n", "<br>\n", "Time Format\n", "<br>\n", "Referee\n", "<br>\n", "Details\n", "<br>"]}, {"cell_type": "code", "execution_count": 151, "metadata": {}, "outputs": [{"data": {"text/plain": ["['UFC 33: Victory in Vegas ',\n", " '<PERSON> ',\n", " '<PERSON> ',\n", " 'W',\n", " 'L',\n", " 'UFC Middleweight Title Bout',\n", " 'Method: Decision - Unanimous ',\n", " 'Round:5',\n", " 'Time:5:00',\n", " 'Time format:5 Rnd (5-5-5-5-5)',\n", " 'Referee:<PERSON>',\n", " 'Details:Point Deducted: <PERSON><PERSON><PERSON> by <PERSON><PERSON><PERSON><PERSON> 45 - 49.<PERSON> 42 - 49.<PERSON> 44 - 49.',\n", " 'URL:http://ufcstats.com/fight-details/37cb7ce0f0b70640']"]}, "execution_count": 151, "metadata": {}, "output_type": "execute_result"}], "source": ["# parse fight results from soup\n", "fight_results = LIB.parse_fight_results(soup)\n", "# append fight url\n", "fight_results.append('URL:'+url)\n", "\n", "# show fight results\n", "fight_results"]}, {"cell_type": "code", "execution_count": 152, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EVENT</th>\n", "      <th>BOUT</th>\n", "      <th>OUTCOME</th>\n", "      <th>WEIGHTCLASS</th>\n", "      <th>METHOD</th>\n", "      <th>ROUND</th>\n", "      <th>TIME</th>\n", "      <th>TIME FORMAT</th>\n", "      <th>REFEREE</th>\n", "      <th>DETAILS</th>\n", "      <th>URL</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON>  vs. <PERSON></td>\n", "      <td>W/L</td>\n", "      <td>UFC Middleweight Title Bout</td>\n", "      <td>Decision - Unanimous</td>\n", "      <td>5</td>\n", "      <td>5:00</td>\n", "      <td>5 Rnd (5-5-5-5-5)</td>\n", "      <td><PERSON></td>\n", "      <td>Point Deducted: <PERSON><PERSON><PERSON> by MenneTony Week...</td>\n", "      <td>http://ufcstats.com/fight-details/37cb7ce0f0b7...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       EVENT                           BOUT OUTCOME  \\\n", "0  UFC 33: Victory in Vegas   <PERSON>  vs. <PERSON>      W/L   \n", "\n", "                   WEIGHTCLASS                 METHOD ROUND  TIME  \\\n", "0  UFC Middleweight Title <PERSON><PERSON>  Decision - Unanimous      5  5:00   \n", "\n", "         TIME FORMAT        REFEREE  \\\n", "0  5 Rnd (5-5-5-5-5)  <PERSON>   \n", "\n", "                                             DETAILS  \\\n", "0  Point Deducted: Illegal <PERSON> by MenneTony Week...   \n", "\n", "                                                 URL  \n", "0  http://ufcstats.com/fight-details/37cb7ce0f0b7...  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# organise fight results\n", "fight_results_df = LIB.organise_fight_results(fight_results, config['fight_results_column_names'])\n", "\n", "# show fight results\n", "display(fight_results_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Parse Fight Stats\n", "Includes:\n", "Event\n", "<br>\n", "Bout\n", "<br>\n", "Round\n", "<br>\n", "Fighter\n", "<br>\n", "Kd\n", "<br>\n", "Sig.Str.\n", "<br>\n", "Sig.Str. %\n", "<br>\n", "Total Str.\n", "<br>\n", "Td\n", "<br>\n", "Td %\n", "<br>\n", "Sub.Att\n", "<br>\n", "Rev.\n", "<br>\n", "Ctrl\n", "<br>\n", "Head\n", "<br>\n", "Body\n", "<br>\n", "Leg\n", "<br>\n", "Distance\n", "<br>\n", "Clinch\n", "<br>\n", "Ground\n", "<br>"]}, {"cell_type": "code", "execution_count": 153, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['<PERSON>', '0', '70 of 125', '56%', '180 of 241', '1 of 1', '100%', '4', '2', '7:55', '<PERSON>', '0', '15 of 22', '68%', '45 of 52', '1 of 1', '100%', '1', '0', '2:02']\n"]}], "source": ["# parse full fight stats for both fighters\n", "fighter_a_stats, fighter_b_stats = LIB.parse_fight_stats(soup)\n", "\n", "# show fighter stats\n", "print(fighter_a_stats[:20])"]}, {"cell_type": "code", "execution_count": 154, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[['<PERSON>', '0', '70 of 125', '56%', '180 of 241', '1 of 1', '100%', '4', '2', '7:55'], ['<PERSON>', '0', '15 of 22', '68%', '45 of 52', '1 of 1', '100%', '1', '0', '2:02']]\n"]}], "source": ["# organise stats extracted from soup\n", "fighter_a_stats_clean = LIB.organise_fight_stats(fighter_a_stats)\n", "fighter_b_stats_clean = LIB.organise_fight_stats(fighter_b_stats)\n", "\n", "# show organised stats\n", "print(fighter_a_stats_clean[:2])"]}, {"cell_type": "code", "execution_count": 155, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ROUND</th>\n", "      <th>FIGHTER</th>\n", "      <th>KD</th>\n", "      <th>SIG.STR.</th>\n", "      <th>SIG.STR. %</th>\n", "      <th>TOTAL STR.</th>\n", "      <th>TD</th>\n", "      <th>TD %</th>\n", "      <th>SUB.ATT</th>\n", "      <th>REV.</th>\n", "      <th>CTRL</th>\n", "      <th>HEAD</th>\n", "      <th>BODY</th>\n", "      <th>LEG</th>\n", "      <th>DISTANCE</th>\n", "      <th>CLINCH</th>\n", "      <th>GROUND</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Round 1</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>15 of 22</td>\n", "      <td>68%</td>\n", "      <td>45 of 52</td>\n", "      <td>1 of 1</td>\n", "      <td>100%</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2:02</td>\n", "      <td>5 of 9</td>\n", "      <td>8 of 10</td>\n", "      <td>2 of 3</td>\n", "      <td>2 of 4</td>\n", "      <td>8 of 10</td>\n", "      <td>5 of 8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Round 2</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>6 of 15</td>\n", "      <td>40%</td>\n", "      <td>34 of 45</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3:07</td>\n", "      <td>3 of 12</td>\n", "      <td>2 of 2</td>\n", "      <td>1 of 1</td>\n", "      <td>2 of 5</td>\n", "      <td>1 of 2</td>\n", "      <td>3 of 8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Round 3</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>14 of 29</td>\n", "      <td>48%</td>\n", "      <td>29 of 47</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0:14</td>\n", "      <td>7 of 21</td>\n", "      <td>3 of 3</td>\n", "      <td>4 of 5</td>\n", "      <td>10 of 24</td>\n", "      <td>4 of 5</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Round 4</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>21 of 41</td>\n", "      <td>51%</td>\n", "      <td>24 of 44</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0:07</td>\n", "      <td>13 of 31</td>\n", "      <td>3 of 3</td>\n", "      <td>5 of 7</td>\n", "      <td>7 of 21</td>\n", "      <td>13 of 19</td>\n", "      <td>1 of 1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Round 5</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>14 of 18</td>\n", "      <td>77%</td>\n", "      <td>48 of 53</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2:25</td>\n", "      <td>10 of 13</td>\n", "      <td>0 of 0</td>\n", "      <td>4 of 5</td>\n", "      <td>3 of 5</td>\n", "      <td>4 of 6</td>\n", "      <td>7 of 7</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     ROUND     FIGHTER KD  SIG.STR. SIG.STR. % TOTAL STR.      TD  TD %  \\\n", "0  Round 1  <PERSON>  0  15 of 22        68%   45 of 52  1 of 1  100%   \n", "1  Round 2  <PERSON>  0   6 of 15        40%   34 of 45  0 of 0   ---   \n", "2  Round 3  <PERSON>  0  14 of 29        48%   29 of 47  0 of 0   ---   \n", "3  Round 4  <PERSON>  0  21 of 41        51%   24 of 44  0 of 0   ---   \n", "4  Round 5  <PERSON>  0  14 of 18        77%   48 of 53  0 of 0   ---   \n", "\n", "  SUB.ATT REV.  CTRL      HEAD     BODY     LEG  DISTANCE    CLINCH  GROUND  \n", "0       1    0  2:02    5 of 9  8 of 10  2 of 3    2 of 4   8 of 10  5 of 8  \n", "1       2    1  3:07   3 of 12   2 of 2  1 of 1    2 of 5    1 of 2  3 of 8  \n", "2       0    1  0:14   7 of 21   3 of 3  4 of 5  10 of 24    4 of 5  0 of 0  \n", "3       1    0  0:07  13 of 31   3 of 3  5 of 7   7 of 21  13 of 19  1 of 1  \n", "4       0    0  2:25  10 of 13   0 of 0  4 of 5    3 of 5    4 of 6  7 of 7  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# convert list of fighter stats into a structured dataframe\n", "fighter_a_stats_df = LIB.convert_fight_stats_to_df(fighter_a_stats_clean, config['totals_column_names'], config['significant_strikes_column_names'])\n", "fighter_b_stats_df = LIB.convert_fight_stats_to_df(fighter_b_stats_clean, config['totals_column_names'], config['significant_strikes_column_names'])\n", "\n", "# show stats df\n", "display(fighter_a_stats_df)"]}, {"cell_type": "code", "execution_count": 156, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>EVENT</th>\n", "      <th>BOUT</th>\n", "      <th>ROUND</th>\n", "      <th>FIGHTER</th>\n", "      <th>KD</th>\n", "      <th>SIG.STR.</th>\n", "      <th>SIG.STR. %</th>\n", "      <th>TOTAL STR.</th>\n", "      <th>TD</th>\n", "      <th>TD %</th>\n", "      <th>SUB.ATT</th>\n", "      <th>REV.</th>\n", "      <th>CTRL</th>\n", "      <th>HEAD</th>\n", "      <th>BODY</th>\n", "      <th>LEG</th>\n", "      <th>DISTANCE</th>\n", "      <th>CLINCH</th>\n", "      <th>GROUND</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 1</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>15 of 22</td>\n", "      <td>68%</td>\n", "      <td>45 of 52</td>\n", "      <td>1 of 1</td>\n", "      <td>100%</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2:02</td>\n", "      <td>5 of 9</td>\n", "      <td>8 of 10</td>\n", "      <td>2 of 3</td>\n", "      <td>2 of 4</td>\n", "      <td>8 of 10</td>\n", "      <td>5 of 8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 2</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>6 of 15</td>\n", "      <td>40%</td>\n", "      <td>34 of 45</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>3:07</td>\n", "      <td>3 of 12</td>\n", "      <td>2 of 2</td>\n", "      <td>1 of 1</td>\n", "      <td>2 of 5</td>\n", "      <td>1 of 2</td>\n", "      <td>3 of 8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 3</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>14 of 29</td>\n", "      <td>48%</td>\n", "      <td>29 of 47</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0:14</td>\n", "      <td>7 of 21</td>\n", "      <td>3 of 3</td>\n", "      <td>4 of 5</td>\n", "      <td>10 of 24</td>\n", "      <td>4 of 5</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 4</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>21 of 41</td>\n", "      <td>51%</td>\n", "      <td>24 of 44</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0:07</td>\n", "      <td>13 of 31</td>\n", "      <td>3 of 3</td>\n", "      <td>5 of 7</td>\n", "      <td>7 of 21</td>\n", "      <td>13 of 19</td>\n", "      <td>1 of 1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 5</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>14 of 18</td>\n", "      <td>77%</td>\n", "      <td>48 of 53</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2:25</td>\n", "      <td>10 of 13</td>\n", "      <td>0 of 0</td>\n", "      <td>4 of 5</td>\n", "      <td>3 of 5</td>\n", "      <td>4 of 6</td>\n", "      <td>7 of 7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 1</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>22 of 22</td>\n", "      <td>1 of 4</td>\n", "      <td>25%</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2:27</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 2</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>0 of 0</td>\n", "      <td>---</td>\n", "      <td>15 of 15</td>\n", "      <td>3 of 5</td>\n", "      <td>60%</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1:05</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 3</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>4 of 11</td>\n", "      <td>36%</td>\n", "      <td>38 of 45</td>\n", "      <td>1 of 3</td>\n", "      <td>33%</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2:47</td>\n", "      <td>2 of 9</td>\n", "      <td>2 of 2</td>\n", "      <td>0 of 0</td>\n", "      <td>4 of 11</td>\n", "      <td>0 of 0</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 4</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>4 of 12</td>\n", "      <td>33%</td>\n", "      <td>21 of 29</td>\n", "      <td>1 of 2</td>\n", "      <td>50%</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>3:11</td>\n", "      <td>3 of 10</td>\n", "      <td>1 of 2</td>\n", "      <td>0 of 0</td>\n", "      <td>3 of 11</td>\n", "      <td>1 of 1</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>UFC 33: Victory in Vegas</td>\n", "      <td><PERSON> vs. <PERSON></td>\n", "      <td>Round 5</td>\n", "      <td><PERSON></td>\n", "      <td>0</td>\n", "      <td>1 of 2</td>\n", "      <td>50%</td>\n", "      <td>14 of 15</td>\n", "      <td>1 of 5</td>\n", "      <td>20%</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>2:06</td>\n", "      <td>0 of 1</td>\n", "      <td>0 of 0</td>\n", "      <td>1 of 1</td>\n", "      <td>0 of 1</td>\n", "      <td>1 of 1</td>\n", "      <td>0 of 0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                      EVENT                         BOUT    ROUND  \\\n", "0  UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 1   \n", "1  UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 2   \n", "2  UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 3   \n", "3  UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 4   \n", "4  UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 5   \n", "0  UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 1   \n", "1  UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 2   \n", "2  UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 3   \n", "3  UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 4   \n", "4  UFC 33: Victory in Vegas  <PERSON> vs. <PERSON>  Round 5   \n", "\n", "        FIGHTER KD  SIG.STR. SIG.STR. % TOTAL STR.      TD  TD % SUB.ATT REV.  \\\n", "0    <PERSON>  0  15 of 22        68%   45 of 52  1 of 1  100%       1    0   \n", "1    <PERSON>  0   6 of 15        40%   34 of 45  0 of 0   ---       2    1   \n", "2    <PERSON>  0  14 of 29        48%   29 of 47  0 of 0   ---       0    1   \n", "3    <PERSON>  0  21 of 41        51%   24 of 44  0 of 0   ---       1    0   \n", "4    <PERSON>  0  14 of 18        77%   48 of 53  0 of 0   ---       0    0   \n", "0  <PERSON>  0    0 of 0        ---   22 of 22  1 of 4   25%       0    0   \n", "1  <PERSON>  0    0 of 0        ---   15 of 15  3 of 5   60%       0    0   \n", "2  <PERSON>  0   4 of 11        36%   38 of 45  1 of 3   33%       0    0   \n", "3  <PERSON>  0   4 of 12        33%   21 of 29  1 of 2   50%       0    0   \n", "4  <PERSON>  0    1 of 2        50%   14 of 15  1 of 5   20%       0    0   \n", "\n", "   CTRL      HEAD     BODY     LEG  DISTANCE    CLINCH  GROUND  \n", "0  2:02    5 of 9  8 of 10  2 of 3    2 of 4   8 of 10  5 of 8  \n", "1  3:07   3 of 12   2 of 2  1 of 1    2 of 5    1 of 2  3 of 8  \n", "2  0:14   7 of 21   3 of 3  4 of 5  10 of 24    4 of 5  0 of 0  \n", "3  0:07  13 of 31   3 of 3  5 of 7   7 of 21  13 of 19  1 of 1  \n", "4  2:25  10 of 13   0 of 0  4 of 5    3 of 5    4 of 6  7 of 7  \n", "0  2:27    0 of 0   0 of 0  0 of 0    0 of 0    0 of 0  0 of 0  \n", "1  1:05    0 of 0   0 of 0  0 of 0    0 of 0    0 of 0  0 of 0  \n", "2  2:47    2 of 9   2 of 2  0 of 0   4 of 11    0 of 0  0 of 0  \n", "3  3:11   3 of 10   1 of 2  0 of 0   3 of 11    1 of 1  0 of 0  \n", "4  2:06    0 of 1   0 of 0  1 of 1    0 of 1    1 of 1  0 of 0  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# combine fighter stats into one\n", "fight_stats = LIB.combine_fighter_stats_dfs(fighter_a_stats_df, fighter_b_stats_df, soup)\n", "\n", "# show fight stats\n", "display(fight_stats)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse Fighter Details"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [], "source": ["# define url to parse\n", "url = 'http://ufcstats.com/statistics/fighters?char=b&page=all' # 'a' last names\n", "url = 'http://ufcstats.com/statistics/fighters?char=b&page=all' # 'b' last names"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["# get soup\n", "soup = LIB.get_soup(url)"]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FIRST</th>\n", "      <th>LAST</th>\n", "      <th>NICKNAME</th>\n", "      <th>URL</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td></td>\n", "      <td>http://ufcstats.com/fighter-details/39cc64bf0a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>The Polish Pistola</td>\n", "      <td>http://ufcstats.com/fighter-details/fd5b6598a3...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Darth</td>\n", "      <td>http://ufcstats.com/fighter-details/c0ab242c40...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Badurek</td>\n", "      <td></td>\n", "      <td>http://ufcstats.com/fighter-details/4fcf6e0c4e...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td>Caramel Thunder</td>\n", "      <td>http://ufcstats.com/fighter-details/82d4c95764...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>267</th>\n", "      <td>JP</td>\n", "      <td>Buys</td>\n", "      <td><PERSON></td>\n", "      <td>http://ufcstats.com/fighter-details/0b36224780...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>268</th>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON>uk<PERSON></td>\n", "      <td>The Great</td>\n", "      <td>http://ufcstats.com/fighter-details/c4d039123e...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>269</th>\n", "      <td><PERSON></td>\n", "      <td><PERSON></td>\n", "      <td>Kid Dynamite</td>\n", "      <td>http://ufcstats.com/fighter-details/76ee3d666c...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>270</th>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>The Sergeant</td>\n", "      <td>http://ufcstats.com/fighter-details/4361245697...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>271</th>\n", "      <td><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "      <td></td>\n", "      <td>http://ufcstats.com/fighter-details/87f0b37f15...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>272 rows × 4 columns</p>\n", "</div>"], "text/plain": ["       FIRST       LAST            NICKNAME  \\\n", "0     <PERSON><PERSON>                       \n", "1       <PERSON>  The Polish Pistola   \n", "2       <PERSON>   \n", "3    Izabela    Badurek                       \n", "4     <PERSON>      Baeza     <PERSON>   \n", "..       ...        ...                 ...   \n", "267       JP       Buys        <PERSON> Savage   \n", "268   <PERSON> Great   \n", "269  <PERSON>   \n", "270    <PERSON>        The Sergeant   \n", "271  <PERSON>                       \n", "\n", "                                                   URL  \n", "0    http://ufcstats.com/fighter-details/39cc64bf0a...  \n", "1    http://ufcstats.com/fighter-details/fd5b6598a3...  \n", "2    http://ufcstats.com/fighter-details/c0ab242c40...  \n", "3    http://ufcstats.com/fighter-details/4fcf6e0c4e...  \n", "4    http://ufcstats.com/fighter-details/82d4c95764...  \n", "..                                                 ...  \n", "267  http://ufcstats.com/fighter-details/0b36224780...  \n", "268  http://ufcstats.com/fighter-details/c4d039123e...  \n", "269  http://ufcstats.com/fighter-details/76ee3d666c...  \n", "270  http://ufcstats.com/fighter-details/4361245697...  \n", "271  http://ufcstats.com/fighter-details/87f0b37f15...  \n", "\n", "[272 rows x 4 columns]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# parse fighter details\n", "fighter_details_df = LIB.parse_fighter_details(soup, config['fighter_details_column_names'])\n", "\n", "# show fighter details\n", "display(fighter_details_df)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Parse Fighter Tale Of The Tape"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["# define url to parse\n", "url = 'http://ufcstats.com/fighter-details/93fe7332d16c6ad9' # '--' and '' present\n", "url = 'http://ufcstats.com/fighter-details/b361180739bed4b0' # '--' present\n", "url = 'http://ufcstats.com/fighter-details/1897b7b913736a7c' # no first name\n", "url = 'http://ufcstats.com/fighter-details/d0f3959b4a9747e6' # all tott present"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["# get soup\n", "soup = LIB.get_soup(url)"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["['Fighter:<PERSON>',\n", " 'Height:5\\' 7\"',\n", " 'Weight:135 lbs.',\n", " 'Reach:70\"',\n", " 'STANCE:Orthodox',\n", " 'DOB:Sep 09, 1986']"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["# parse fighter tale of the tape\n", "fighter_tott = LIB.parse_fighter_tott(soup)\n", "\n", "# show tale of the tape\n", "fighter_tott"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>FIGHTER</th>\n", "      <th>HEIGHT</th>\n", "      <th>WEIGHT</th>\n", "      <th>R<PERSON><PERSON></th>\n", "      <th>STANCE</th>\n", "      <th>DOB</th>\n", "      <th>URL</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON></td>\n", "      <td>5' 7\"</td>\n", "      <td>135 lbs.</td>\n", "      <td>70\"</td>\n", "      <td>Orthodox</td>\n", "      <td>Sep 09, 1986</td>\n", "      <td>http://ufcstats.com/fighter-details/d0f3959b4a...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     FIGHTER HEIGHT    WEIGHT REACH    STANCE           DOB  \\\n", "0  <PERSON>  5' 7\"  135 lbs.   70\"  Orthodox  Sep 09, 1986   \n", "\n", "                                                 URL  \n", "0  http://ufcstats.com/fighter-details/d0f3959b4a...  "]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["# create empty df to store fighters' tale of the tape\n", "all_fighter_tott_df = pd.DataFrame(columns=config['fighter_tott_column_names'])\n", "\n", "# organise fighter tale of the tape\n", "fighter_tott_df = LIB.organise_fighter_tott(fighter_tott, config['fighter_tott_column_names'], url)\n", "\n", "# show fighter tale of the tape\n", "display(fighter_tott_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"interpreter": {"hash": "9aac8944d03ded7dbafcde28cbfe34d23c9079c2df9125865de5144d9ea8ac9a"}, "kernelspec": {"display_name": "Python 3.8.8 64-bit ('base': conda)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}