const Database = require('better-sqlite3');
const path = require('path');

const scrapePath = path.join(__dirname, '../data/ufc_data.db');
const calculatorPath = path.join(__dirname, '../../ufc-ranking-calculator/data/ufc_data.db');

console.log('Comparing databases:');
console.log('Scrape DB:', scrapePath);
console.log('Calculator DB:', calculatorPath);

const scrapeDb = new Database(scrapePath, { readonly: true });
const calcDb = new Database(calculatorPath, { readonly: true });

// Compare event counts
const scrapeEvents = scrapeDb.prepare('SELECT COUNT(*) as count FROM events').get();
const calcEvents = calcDb.prepare('SELECT COUNT(*) as count FROM events').get();

console.log(`\nEvents - Scrape: ${scrapeEvents.count}, Calculator: ${calcEvents.count}`);

// Compare fight counts
const scrapeFights = scrapeDb.prepare('SELECT COUNT(*) as count FROM fights').get();
const calcFights = calcDb.prepare('SELECT COUNT(*) as count FROM fights').get();

console.log(`Fights - Scrape: ${scrapeFights.count}, Calculator: ${calcFights.count}`);

// Check for UFC 308 in both
const scrapeUFC308 = scrapeDb.prepare("SELECT COUNT(*) as count FROM events WHERE event_name LIKE '%UFC 308%'").get();
const calcUFC308 = calcDb.prepare("SELECT COUNT(*) as count FROM events WHERE event_name LIKE '%UFC 308%'").get();

console.log(`UFC 308 - Scrape: ${scrapeUFC308.count}, Calculator: ${calcUFC308.count}`);

// Check for Ilia Topuria in both
const scrapeIlia = scrapeDb.prepare("SELECT id, first_name, last_name FROM fighters WHERE first_name = 'Ilia' AND last_name = 'Topuria'").get();
const calcIlia = calcDb.prepare("SELECT id, first_name, last_name FROM fighters WHERE first_name = 'Ilia' AND last_name = 'Topuria'").get();

console.log(`\nIlia Topuria:`);
console.log(`Scrape: ${scrapeIlia ? `ID ${scrapeIlia.id}` : 'Not found'}`);
console.log(`Calculator: ${calcIlia ? `ID ${calcIlia.id}` : 'Not found'}`);

scrapeDb.close();
calcDb.close();