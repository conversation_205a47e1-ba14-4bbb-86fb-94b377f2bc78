const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '../data/ufc_data.db');
const db = new Database(dbPath, { readonly: true });

// Check fights for both Topuria IDs
const checkFightsForFighter = (fighterId, name) => {
    const fights = db.prepare(`
        SELECT COUNT(*) as count
        FROM fights f
        WHERE f.fighter1_id = ? OR f.fighter2_id = ?
    `).get(fighterId, fighterId);
    
    console.log(`${name} (ID: ${fighterId}): ${fights.count} fights`);
    
    // Get sample fights
    const sampleFights = db.prepare(`
        SELECT f.bout, e.event_name, e.date
        FROM fights f
        JOIN events e ON f.event_id = e.id
        WHERE f.fighter1_id = ? OR f.fighter2_id = ?
        ORDER BY e.date DESC
        LIMIT 3
    `).all(fighterId, fighterId);
    
    sampleFights.forEach(fight => {
        console.log(`  - ${fight.event_name}: ${fight.bout}`);
    });
};

console.log('Checking fights for both Topuria entries:\n');
checkFightsForFighter(3950, 'Ilia Topuria');
console.log('');
checkFightsForFighter(3951, 'Aleksandre Topuria');

// Check fighter stats too
console.log('\nChecking fighter_stats:');
const stats3950 = db.prepare('SELECT COUNT(*) as count FROM fighter_stats WHERE fighter_id = ?').get(3950);
const stats3951 = db.prepare('SELECT COUNT(*) as count FROM fighter_stats WHERE fighter_id = ?').get(3951);
console.log(`Ilia (3950) has ${stats3950.count} fighter_stats records`);
console.log(`Aleksandre (3951) has ${stats3951.count} fighter_stats records`);

db.close();