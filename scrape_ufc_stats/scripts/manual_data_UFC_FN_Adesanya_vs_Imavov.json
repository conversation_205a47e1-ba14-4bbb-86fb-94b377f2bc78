{"event": {"name": "UFC Fight Night: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON>", "date": "2025-02-01", "location": "Riyadh, Saudi Arabia"}, "fights": [{"fighter1_name": "Israel Adesanya", "fighter2_name": "Nassourdine Imavov", "weight_class": "Middleweight", "winner": "Nassourdine Imavov", "method": "KO/TKO", "round": 2, "time": "0:30", "stats": [{"fighter_name": "Israel Adesanya", "round": 1, "kd": 0, "sig_str_landed": 20, "sig_str_attempted": 44, "sig_str_pct": 45, "total_str_landed": 26, "total_str_attempted": 50, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 8, "head_attempted": 27, "body_landed": 3, "body_attempted": 5, "leg_landed": 9, "leg_attempted": 12, "distance_landed": 20, "distance_attempted": 44, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "Nassourdine Imavov", "round": 1, "kd": 0, "sig_str_landed": 8, "sig_str_attempted": 21, "sig_str_pct": 38, "total_str_landed": 10, "total_str_attempted": 23, "td_landed": 0, "td_attempted": 3, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:40", "head_landed": 3, "head_attempted": 13, "body_landed": 0, "body_attempted": 2, "leg_landed": 5, "leg_attempted": 6, "distance_landed": 8, "distance_attempted": 21, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "Israel Adesanya", "round": 2, "kd": 0, "sig_str_landed": 6, "sig_str_attempted": 6, "sig_str_pct": 100, "total_str_landed": 6, "total_str_attempted": 6, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 1, "head_attempted": 1, "body_landed": 3, "body_attempted": 3, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 6, "distance_attempted": 6, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "Nassourdine Imavov", "round": 2, "kd": 1, "sig_str_landed": 7, "sig_str_attempted": 10, "sig_str_pct": 70, "total_str_landed": 8, "total_str_attempted": 11, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:04", "head_landed": 6, "head_attempted": 8, "body_landed": 0, "body_attempted": 1, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 2, "distance_attempted": 4, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 5, "ground_attempted": 6}]}, {"fighter1_name": "<PERSON><PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Middleweight", "winner": "<PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 9, "sig_str_attempted": 25, "sig_str_pct": 36, "total_str_landed": 9, "total_str_attempted": 25, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 1, "head_attempted": 10, "body_landed": 2, "body_attempted": 3, "leg_landed": 6, "leg_attempted": 12, "distance_landed": 9, "distance_attempted": 24, "clinch_landed": 0, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 13, "sig_str_attempted": 21, "sig_str_pct": 61, "total_str_landed": 13, "total_str_attempted": 21, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:16", "head_landed": 7, "head_attempted": 14, "body_landed": 1, "body_attempted": 2, "leg_landed": 5, "leg_attempted": 5, "distance_landed": 13, "distance_attempted": 21, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 7, "sig_str_attempted": 30, "sig_str_pct": 23, "total_str_landed": 9, "total_str_attempted": 32, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 0, "head_attempted": 18, "body_landed": 5, "body_attempted": 7, "leg_landed": 2, "leg_attempted": 5, "distance_landed": 7, "distance_attempted": 30, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 14, "sig_str_attempted": 25, "sig_str_pct": 56, "total_str_landed": 15, "total_str_attempted": 26, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:34", "head_landed": 5, "head_attempted": 16, "body_landed": 6, "body_attempted": 6, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 13, "distance_attempted": 24, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 22, "sig_str_attempted": 32, "sig_str_pct": 68, "total_str_landed": 32, "total_str_attempted": 45, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:40", "head_landed": 7, "head_attempted": 14, "body_landed": 10, "body_attempted": 10, "leg_landed": 5, "leg_attempted": 8, "distance_landed": 18, "distance_attempted": 28, "clinch_landed": 4, "clinch_attempted": 4, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 16, "sig_str_attempted": 29, "sig_str_pct": 55, "total_str_landed": 19, "total_str_attempted": 34, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 11, "head_attempted": 23, "body_landed": 4, "body_attempted": 4, "leg_landed": 1, "leg_attempted": 2, "distance_landed": 12, "distance_attempted": 24, "clinch_landed": 4, "clinch_attempted": 5, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON><PERSON><PERSON><PERSON>", "weight_class": "Heavyweight", "winner": "<PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 23, "sig_str_pct": 47, "total_str_landed": 32, "total_str_attempted": 51, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:09", "head_landed": 9, "head_attempted": 21, "body_landed": 0, "body_attempted": 0, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 11, "distance_attempted": 23, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 7, "sig_str_attempted": 15, "sig_str_pct": 46, "total_str_landed": 9, "total_str_attempted": 18, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 0, "head_attempted": 8, "body_landed": 2, "body_attempted": 2, "leg_landed": 5, "leg_attempted": 5, "distance_landed": 7, "distance_attempted": 15, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 3, "sig_str_attempted": 19, "sig_str_pct": 15, "total_str_landed": 39, "total_str_attempted": 64, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "2:19", "head_landed": 2, "head_attempted": 17, "body_landed": 1, "body_attempted": 2, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 3, "distance_attempted": 19, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 6, "sig_str_attempted": 17, "sig_str_pct": 35, "total_str_landed": 8, "total_str_attempted": 19, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 3, "head_attempted": 13, "body_landed": 1, "body_attempted": 1, "leg_landed": 2, "leg_attempted": 3, "distance_landed": 6, "distance_attempted": 17, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 18, "sig_str_pct": 61, "total_str_landed": 74, "total_str_attempted": 93, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "2:49", "head_landed": 10, "head_attempted": 17, "body_landed": 1, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 11, "distance_attempted": 18, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 4, "sig_str_attempted": 23, "sig_str_pct": 17, "total_str_landed": 6, "total_str_attempted": 26, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 4, "head_attempted": 22, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 1, "distance_landed": 4, "distance_attempted": 23, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON><PERSON><PERSON>", "weight_class": "Bantamweight", "winner": "<PERSON><PERSON><PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 12, "sig_str_attempted": 21, "sig_str_pct": 57, "total_str_landed": 28, "total_str_attempted": 37, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:09", "head_landed": 6, "head_attempted": 14, "body_landed": 2, "body_attempted": 3, "leg_landed": 4, "leg_attempted": 4, "distance_landed": 11, "distance_attempted": 20, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 3, "sig_str_attempted": 14, "sig_str_pct": 21, "total_str_landed": 5, "total_str_attempted": 16, "td_landed": 2, "td_attempted": 4, "td_pct": 50, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "2:44", "head_landed": 2, "head_attempted": 9, "body_landed": 1, "body_attempted": 2, "leg_landed": 0, "leg_attempted": 3, "distance_landed": 3, "distance_attempted": 12, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 2}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 13, "sig_str_attempted": 38, "sig_str_pct": 34, "total_str_landed": 21, "total_str_attempted": 46, "td_landed": 0, "td_attempted": 5, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:49", "head_landed": 3, "head_attempted": 25, "body_landed": 7, "body_attempted": 9, "leg_landed": 3, "leg_attempted": 4, "distance_landed": 13, "distance_attempted": 36, "clinch_landed": 0, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 21, "sig_str_attempted": 45, "sig_str_pct": 46, "total_str_landed": 26, "total_str_attempted": 51, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:54", "head_landed": 8, "head_attempted": 25, "body_landed": 7, "body_attempted": 13, "leg_landed": 6, "leg_attempted": 7, "distance_landed": 19, "distance_attempted": 40, "clinch_landed": 2, "clinch_attempted": 5, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 13, "sig_str_attempted": 20, "sig_str_pct": 65, "total_str_landed": 20, "total_str_attempted": 28, "td_landed": 0, "td_attempted": 3, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 9, "head_attempted": 15, "body_landed": 4, "body_attempted": 5, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 13, "distance_attempted": 20, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 19, "sig_str_attempted": 35, "sig_str_pct": 54, "total_str_landed": 37, "total_str_attempted": 58, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 1, "pass": 0, "rev": 0, "ctrl_time": "3:34", "head_landed": 14, "head_attempted": 29, "body_landed": 3, "body_attempted": 3, "leg_landed": 2, "leg_attempted": 3, "distance_landed": 9, "distance_attempted": 22, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 10, "ground_attempted": 13}]}, {"fighter1_name": "<PERSON><PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Lightweight", "winner": "<PERSON><PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 16, "sig_str_attempted": 24, "sig_str_pct": 66, "total_str_landed": 20, "total_str_attempted": 29, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:23", "head_landed": 12, "head_attempted": 20, "body_landed": 3, "body_attempted": 3, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 14, "distance_attempted": 21, "clinch_landed": 2, "clinch_attempted": 3, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 8, "sig_str_attempted": 15, "sig_str_pct": 53, "total_str_landed": 10, "total_str_attempted": 18, "td_landed": 1, "td_attempted": 5, "td_pct": 20, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:34", "head_landed": 3, "head_attempted": 8, "body_landed": 4, "body_attempted": 5, "leg_landed": 1, "leg_attempted": 2, "distance_landed": 6, "distance_attempted": 13, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 2, "ground_attempted": 2}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 10, "sig_str_attempted": 18, "sig_str_pct": 55, "total_str_landed": 23, "total_str_attempted": 33, "td_landed": 1, "td_attempted": 2, "td_pct": 50, "sub_att": 0, "pass": 0, "rev": 2, "ctrl_time": "2:31", "head_landed": 8, "head_attempted": 16, "body_landed": 2, "body_attempted": 2, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 0, "distance_attempted": 2, "clinch_landed": 3, "clinch_attempted": 3, "ground_landed": 7, "ground_attempted": 13}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 6, "sig_str_attempted": 8, "sig_str_pct": 75, "total_str_landed": 8, "total_str_attempted": 10, "td_landed": 0, "td_attempted": 3, "td_pct": 0, "sub_att": 1, "pass": 2, "rev": 2, "ctrl_time": "1:38", "head_landed": 5, "head_attempted": 7, "body_landed": 1, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 4, "distance_attempted": 6, "clinch_landed": 2, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 9, "sig_str_attempted": 14, "sig_str_pct": 64, "total_str_landed": 15, "total_str_attempted": 21, "td_landed": 2, "td_attempted": 4, "td_pct": 50, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "3:17", "head_landed": 9, "head_attempted": 14, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 6, "distance_attempted": 10, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 3, "ground_attempted": 4}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 6, "sig_str_attempted": 12, "sig_str_pct": 50, "total_str_landed": 12, "total_str_attempted": 21, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 1, "rev": 1, "ctrl_time": "0:47", "head_landed": 5, "head_attempted": 11, "body_landed": 0, "body_attempted": 0, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 3, "distance_attempted": 5, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 3, "ground_attempted": 7}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON><PERSON>", "weight_class": "Featherweight", "winner": "<PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 14, "sig_str_attempted": 34, "sig_str_pct": 41, "total_str_landed": 32, "total_str_attempted": 56, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:55", "head_landed": 7, "head_attempted": 23, "body_landed": 6, "body_attempted": 10, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 10, "distance_attempted": 30, "clinch_landed": 4, "clinch_attempted": 4, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 27, "sig_str_pct": 40, "total_str_landed": 23, "total_str_attempted": 41, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:21", "head_landed": 2, "head_attempted": 12, "body_landed": 7, "body_attempted": 9, "leg_landed": 2, "leg_attempted": 6, "distance_landed": 6, "distance_attempted": 21, "clinch_landed": 5, "clinch_attempted": 6, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 10, "sig_str_attempted": 33, "sig_str_pct": 30, "total_str_landed": 25, "total_str_attempted": 51, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:16", "head_landed": 8, "head_attempted": 31, "body_landed": 2, "body_attempted": 2, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 8, "distance_attempted": 31, "clinch_landed": 2, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 21, "sig_str_pct": 52, "total_str_landed": 19, "total_str_attempted": 31, "td_landed": 0, "td_attempted": 3, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "2:03", "head_landed": 1, "head_attempted": 10, "body_landed": 4, "body_attempted": 5, "leg_landed": 6, "leg_attempted": 6, "distance_landed": 6, "distance_attempted": 16, "clinch_landed": 5, "clinch_attempted": 5, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 8, "sig_str_attempted": 25, "sig_str_pct": 32, "total_str_landed": 22, "total_str_attempted": 69, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "3:25", "head_landed": 8, "head_attempted": 25, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 4, "distance_attempted": 12, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 4, "ground_attempted": 13}, {"fighter_name": "<PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 9, "sig_str_attempted": 18, "sig_str_pct": 50, "total_str_landed": 15, "total_str_attempted": 25, "td_landed": 0, "td_attempted": 2, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:11", "head_landed": 2, "head_attempted": 8, "body_landed": 5, "body_attempted": 7, "leg_landed": 2, "leg_attempted": 3, "distance_landed": 2, "distance_attempted": 7, "clinch_landed": 7, "clinch_attempted": 11, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON><PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Heavyweight", "winner": "<PERSON><PERSON><PERSON>", "method": "KO/TKO", "round": 1, "time": "3:12", "stats": [{"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 1, "sig_str_landed": 14, "sig_str_attempted": 32, "sig_str_pct": 43, "total_str_landed": 14, "total_str_attempted": 32, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 9, "head_attempted": 27, "body_landed": 5, "body_attempted": 5, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 13, "distance_attempted": 30, "clinch_landed": 1, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 19, "sig_str_attempted": 34, "sig_str_pct": 55, "total_str_landed": 19, "total_str_attempted": 35, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:08", "head_landed": 4, "head_attempted": 18, "body_landed": 7, "body_attempted": 8, "leg_landed": 8, "leg_attempted": 8, "distance_landed": 18, "distance_attempted": 31, "clinch_landed": 1, "clinch_attempted": 3, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON>", "fighter2_name": "<PERSON><PERSON>", "weight_class": "Lightweight", "winner": "<PERSON><PERSON>", "method": "KO/TKO", "round": 1, "time": "2:01", "stats": [{"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 54, "sig_str_attempted": 69, "sig_str_pct": 78, "total_str_landed": 69, "total_str_attempted": 90, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 1, "pass": 1, "rev": 1, "ctrl_time": "1:00", "head_landed": 53, "head_attempted": 67, "body_landed": 1, "body_attempted": 2, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 8, "distance_attempted": 18, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 46, "ground_attempted": 51}, {"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 4, "sig_str_attempted": 13, "sig_str_pct": 30, "total_str_landed": 4, "total_str_attempted": 13, "td_landed": 1, "td_attempted": 2, "td_pct": 50, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:08", "head_landed": 3, "head_attempted": 9, "body_landed": 0, "body_attempted": 3, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 4, "distance_attempted": 13, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON><PERSON>", "weight_class": "Women's Flyweight", "winner": "<PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 14, "sig_str_attempted": 25, "sig_str_pct": 56, "total_str_landed": 35, "total_str_attempted": 51, "td_landed": 1, "td_attempted": 2, "td_pct": 50, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "2:48", "head_landed": 5, "head_attempted": 14, "body_landed": 5, "body_attempted": 7, "leg_landed": 4, "leg_attempted": 4, "distance_landed": 13, "distance_attempted": 24, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 10, "sig_str_attempted": 18, "sig_str_pct": 55, "total_str_landed": 10, "total_str_attempted": 18, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 1, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 4, "head_attempted": 10, "body_landed": 2, "body_attempted": 3, "leg_landed": 4, "leg_attempted": 5, "distance_landed": 10, "distance_attempted": 18, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 30, "sig_str_attempted": 54, "sig_str_pct": 55, "total_str_landed": 62, "total_str_attempted": 88, "td_landed": 1, "td_attempted": 2, "td_pct": 50, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:20", "head_landed": 14, "head_attempted": 36, "body_landed": 9, "body_attempted": 11, "leg_landed": 7, "leg_attempted": 7, "distance_landed": 27, "distance_attempted": 49, "clinch_landed": 3, "clinch_attempted": 4, "ground_landed": 0, "ground_attempted": 1}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 21, "sig_str_attempted": 38, "sig_str_pct": 55, "total_str_landed": 24, "total_str_attempted": 41, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 15, "head_attempted": 29, "body_landed": 4, "body_attempted": 7, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 15, "distance_attempted": 32, "clinch_landed": 6, "clinch_attempted": 6, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 12, "sig_str_attempted": 26, "sig_str_pct": 46, "total_str_landed": 67, "total_str_attempted": 90, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "3:03", "head_landed": 4, "head_attempted": 16, "body_landed": 6, "body_attempted": 8, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 8, "distance_attempted": 21, "clinch_landed": 4, "clinch_attempted": 5, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 6, "sig_str_attempted": 16, "sig_str_pct": 37, "total_str_landed": 7, "total_str_attempted": 17, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 4, "head_attempted": 14, "body_landed": 2, "body_attempted": 2, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 5, "distance_attempted": 14, "clinch_landed": 1, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "Bogdan Grad", "fighter2_name": "<PERSON>", "weight_class": "Featherweight", "winner": "Bogdan Grad", "method": "KO/TKO", "round": 2, "time": "4:22", "stats": [{"fighter_name": "Bogdan Grad", "round": 1, "kd": 0, "sig_str_landed": 5, "sig_str_attempted": 9, "sig_str_pct": 55, "total_str_landed": 13, "total_str_attempted": 20, "td_landed": 2, "td_attempted": 7, "td_pct": 28, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:17", "head_landed": 5, "head_attempted": 8, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 1, "distance_landed": 4, "distance_attempted": 8, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 15, "sig_str_attempted": 23, "sig_str_pct": 65, "total_str_landed": 28, "total_str_attempted": 42, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 1, "ctrl_time": "2:13", "head_landed": 10, "head_attempted": 18, "body_landed": 1, "body_attempted": 1, "leg_landed": 4, "leg_attempted": 4, "distance_landed": 11, "distance_attempted": 17, "clinch_landed": 2, "clinch_attempted": 3, "ground_landed": 2, "ground_attempted": 3}, {"fighter_name": "Bogdan Grad", "round": 2, "kd": 0, "sig_str_landed": 41, "sig_str_attempted": 57, "sig_str_pct": 71, "total_str_landed": 46, "total_str_attempted": 66, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "3:01", "head_landed": 37, "head_attempted": 52, "body_landed": 1, "body_attempted": 2, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 5, "distance_attempted": 10, "clinch_landed": 2, "clinch_attempted": 3, "ground_landed": 34, "ground_attempted": 44}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 19, "sig_str_attempted": 29, "sig_str_pct": 65, "total_str_landed": 19, "total_str_attempted": 29, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 9, "head_attempted": 19, "body_landed": 6, "body_attempted": 6, "leg_landed": 4, "leg_attempted": 4, "distance_landed": 18, "distance_attempted": 28, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Heavyweight", "winner": "<PERSON><PERSON>", "method": "Decision - Split", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 23, "sig_str_pct": 47, "total_str_landed": 19, "total_str_attempted": 39, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:57", "head_landed": 8, "head_attempted": 19, "body_landed": 0, "body_attempted": 0, "leg_landed": 3, "leg_attempted": 4, "distance_landed": 7, "distance_attempted": 17, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 4, "ground_attempted": 6}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 22, "sig_str_attempted": 40, "sig_str_pct": 55, "total_str_landed": 22, "total_str_attempted": 40, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 14, "head_attempted": 30, "body_landed": 0, "body_attempted": 0, "leg_landed": 8, "leg_attempted": 10, "distance_landed": 22, "distance_attempted": 40, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 12, "sig_str_attempted": 25, "sig_str_pct": 48, "total_str_landed": 12, "total_str_attempted": 25, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 6, "head_attempted": 18, "body_landed": 0, "body_attempted": 0, "leg_landed": 6, "leg_attempted": 7, "distance_landed": 12, "distance_attempted": 25, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 22, "sig_str_attempted": 44, "sig_str_pct": 50, "total_str_landed": 22, "total_str_attempted": 44, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 12, "head_attempted": 33, "body_landed": 0, "body_attempted": 0, "leg_landed": 10, "leg_attempted": 11, "distance_landed": 22, "distance_attempted": 44, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 21, "sig_str_attempted": 39, "sig_str_pct": 53, "total_str_landed": 21, "total_str_attempted": 39, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 11, "head_attempted": 28, "body_landed": 3, "body_attempted": 4, "leg_landed": 7, "leg_attempted": 7, "distance_landed": 21, "distance_attempted": 39, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 25, "sig_str_attempted": 39, "sig_str_pct": 64, "total_str_landed": 25, "total_str_attempted": 39, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 17, "head_attempted": 31, "body_landed": 0, "body_attempted": 0, "leg_landed": 8, "leg_attempted": 8, "distance_landed": 24, "distance_attempted": 38, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}]}]}