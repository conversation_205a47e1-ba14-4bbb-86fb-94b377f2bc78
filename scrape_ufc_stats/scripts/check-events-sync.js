const Database = require('better-sqlite3');
const path = require('path');

const sourcePath = path.join(__dirname, '../data/ufc_data.db');
const targetPath = path.join(__dirname, '../../ufc-ranking-calculator/data/ufc_data.db');

const sourceDb = new Database(sourcePath, { readonly: true });
const targetDb = new Database(targetPath, { readonly: true });

// Check if events exist in target
const eventNames = [
    'UFC 308: Topuria vs. Holloway',
    'UFC 294: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON> 2', 
    'UFC Fight Night: Adesanya vs. Imavov'
];

console.log('Checking events in both databases:\n');

for (const eventName of eventNames) {
    const sourceEvent = sourceDb.prepare('SELECT * FROM events WHERE event_name = ?').get(eventName);
    const targetEvent = targetDb.prepare('SELECT * FROM events WHERE event_name = ?').get(eventName);
    
    console.log(`${eventName}:`);
    console.log(`  Source: ${sourceEvent ? `ID ${sourceEvent.id}` : 'NOT FOUND'}`);
    console.log(`  Target: ${targetEvent ? `ID ${targetEvent.id}` : 'NOT FOUND'}`);
    
    if (sourceEvent && targetEvent) {
        // Check fights count
        const sourceFights = sourceDb.prepare('SELECT COUNT(*) as count FROM fights WHERE event_id = ?').get(sourceEvent.id);
        const targetFights = targetDb.prepare('SELECT COUNT(*) as count FROM fights WHERE event_id = ?').get(targetEvent.id);
        
        console.log(`  Source fights: ${sourceFights.count}`);
        console.log(`  Target fights: ${targetFights.count}`);
    }
    console.log('');
}

sourceDb.close();
targetDb.close();