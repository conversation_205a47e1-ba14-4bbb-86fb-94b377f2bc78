const Database = require('better-sqlite3');
const path = require('path');

const sourcePath = path.join(__dirname, '../data/ufc_data.db');
const targetPath = path.join(__dirname, '../../ufc-ranking-calculator/data/ufc_data.db');

console.log('Syncing newly imported data...');
console.log('Source (scrape):', sourcePath);
console.log('Target (calculator):', targetPath);

const sourceDb = new Database(sourcePath, { readonly: true });
const targetDb = new Database(targetPath);

// Get the event IDs for the newly imported events
const newEvents = sourceDb.prepare(`
    SELECT id, event_name FROM events 
    WHERE event_name IN (
        'UFC 308: Topuria vs. Holloway',
        'UFC 294: <PERSON><PERSON>chev vs. <PERSON><PERSON><PERSON>ski 2',
        'UFC Fight Night: Adesanya vs. Imavov'
    )
`).all();

console.log(`\nFound ${newEvents.length} events to sync:`);
newEvents.forEach(e => console.log(`- ${e.event_name} (ID: ${e.id})`));

try {
    targetDb.exec('BEGIN TRANSACTION');
    
    // For each new event, copy fights and fight_stats
    for (const event of newEvents) {
        console.log(`\nSyncing ${event.event_name}...`);
        
        // Get fights for this event from source
        const fights = sourceDb.prepare(`
            SELECT * FROM fights WHERE event_id = ?
        `).all(event.id);
        
        console.log(`  Found ${fights.length} fights to copy`);
        
        // Prepare insert statements
        const insertFight = targetDb.prepare(`
            INSERT OR REPLACE INTO fights (
                id, event_id, bout, url, fighter1_id, fighter2_id, winner_id,
                result_method, result_round, result_time, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        const insertFightStats = targetDb.prepare(`
            INSERT OR REPLACE INTO fight_stats (
                id, fight_id, fighter_id, round_number, knockdowns, sig_strikes_landed,
                sig_strikes_attempted, sig_strikes_pct, total_strikes_landed,
                total_strikes_attempted, takedowns_landed, takedowns_attempted,
                takedowns_pct, submission_attempts, reversals, control_time,
                head_strikes, body_strikes, leg_strikes, distance_strikes,
                clinch_strikes, ground_strikes
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        // Copy each fight
        for (const fight of fights) {
            insertFight.run(
                fight.id, fight.event_id, fight.bout, fight.url,
                fight.fighter1_id, fight.fighter2_id, fight.winner_id,
                fight.result_method, fight.result_round, fight.result_time,
                fight.created_at
            );
            
            // Get fight stats for this fight
            const fightStats = sourceDb.prepare(`
                SELECT * FROM fight_stats WHERE fight_id = ?
            `).all(fight.id);
            
            console.log(`    Fight ${fight.id}: ${fightStats.length} stat records`);
            
            // Copy fight stats
            for (const stat of fightStats) {
                insertFightStats.run(
                    stat.id, stat.fight_id, stat.fighter_id, stat.round_number,
                    stat.knockdowns, stat.sig_strikes_landed, stat.sig_strikes_attempted,
                    stat.sig_strikes_pct, stat.total_strikes_landed, stat.total_strikes_attempted,
                    stat.takedowns_landed, stat.takedowns_attempted, stat.takedowns_pct,
                    stat.submission_attempts, stat.reversals, stat.control_time,
                    stat.head_strikes, stat.body_strikes, stat.leg_strikes,
                    stat.distance_strikes, stat.clinch_strikes, stat.ground_strikes
                );
            }
        }
    }
    
    targetDb.exec('COMMIT');
    console.log('\n✅ Database sync completed successfully!');
    
    // Verify the sync
    const targetFightCount = targetDb.prepare('SELECT COUNT(*) as count FROM fights').get();
    console.log(`Target database now has ${targetFightCount.count} fights`);
    
} catch (error) {
    targetDb.exec('ROLLBACK');
    console.error('❌ Error during sync:', error);
} finally {
    sourceDb.close();
    targetDb.close();
}