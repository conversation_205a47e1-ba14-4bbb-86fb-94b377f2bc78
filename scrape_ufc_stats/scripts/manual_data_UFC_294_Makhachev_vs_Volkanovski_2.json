{"event": {"name": "UFC 294: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON> 2", "date": "2023-10-21", "location": "Abu Dhabi, United Arab Emirates"}, "fights": [{"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Lightweight", "winner": "<PERSON>", "method": "KO/TKO", "round": 1, "time": "3:06", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 1, "sig_str_landed": 24, "sig_str_attempted": 29, "sig_str_pct": 82, "total_str_landed": 39, "total_str_attempted": 46, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:19", "head_landed": 13, "head_attempted": 16, "body_landed": 9, "body_attempted": 11, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 7, "distance_attempted": 11, "clinch_landed": 8, "clinch_attempted": 9, "ground_landed": 9, "ground_attempted": 9}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 4, "sig_str_attempted": 6, "sig_str_pct": 66, "total_str_landed": 11, "total_str_attempted": 18, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:49", "head_landed": 0, "head_attempted": 1, "body_landed": 1, "body_attempted": 1, "leg_landed": 3, "leg_attempted": 4, "distance_landed": 3, "distance_attempted": 5, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON><PERSON>", "fighter2_name": "<PERSON><PERSON><PERSON><PERSON>", "weight_class": "Middleweight", "winner": "<PERSON><PERSON><PERSON><PERSON>", "method": "Decision - Majority", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 3, "sig_str_attempted": 3, "sig_str_pct": 100, "total_str_landed": 5, "total_str_attempted": 7, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:04", "head_landed": 2, "head_attempted": 2, "body_landed": 1, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 1, "distance_attempted": 1, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 1, "ground_attempted": 1}, {"fighter_name": "<PERSON><PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 16, "sig_str_attempted": 17, "sig_str_pct": 94, "total_str_landed": 61, "total_str_attempted": 66, "td_landed": 2, "td_attempted": 7, "td_pct": 28, "sub_att": 1, "pass": 0, "rev": 0, "ctrl_time": "4:35", "head_landed": 15, "head_attempted": 16, "body_landed": 1, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 1, "distance_attempted": 1, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 15, "ground_attempted": 16}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 18, "sig_str_pct": 61, "total_str_landed": 12, "total_str_attempted": 19, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 5, "head_attempted": 9, "body_landed": 3, "body_attempted": 5, "leg_landed": 3, "leg_attempted": 4, "distance_landed": 11, "distance_attempted": 18, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 9, "sig_str_attempted": 20, "sig_str_pct": 45, "total_str_landed": 12, "total_str_attempted": 23, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:41", "head_landed": 3, "head_attempted": 10, "body_landed": 2, "body_attempted": 6, "leg_landed": 4, "leg_attempted": 4, "distance_landed": 9, "distance_attempted": 20, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 22, "sig_str_attempted": 45, "sig_str_pct": 48, "total_str_landed": 41, "total_str_attempted": 66, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 20, "head_attempted": 41, "body_landed": 2, "body_attempted": 4, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 21, "distance_attempted": 44, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 1, "ground_attempted": 1}, {"fighter_name": "<PERSON><PERSON><PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 13, "sig_str_attempted": 33, "sig_str_pct": 39, "total_str_landed": 34, "total_str_attempted": 54, "td_landed": 1, "td_attempted": 4, "td_pct": 25, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "2:00", "head_landed": 9, "head_attempted": 27, "body_landed": 0, "body_attempted": 2, "leg_landed": 4, "leg_attempted": 4, "distance_landed": 12, "distance_attempted": 32, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 1, "ground_attempted": 1}]}, {"fighter1_name": "<PERSON><PERSON><PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Light Heavyweight", "winner": null, "method": "Could Not Continue", "round": 1, "time": "3:13", "stats": [{"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 13, "sig_str_attempted": 19, "sig_str_pct": 68, "total_str_landed": 16, "total_str_attempted": 23, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:06", "head_landed": 7, "head_attempted": 13, "body_landed": 3, "body_attempted": 3, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 7, "distance_attempted": 11, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 5, "ground_attempted": 7}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 14, "sig_str_attempted": 27, "sig_str_pct": 51, "total_str_landed": 17, "total_str_attempted": 30, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 4, "head_attempted": 15, "body_landed": 1, "body_attempted": 2, "leg_landed": 9, "leg_attempted": 10, "distance_landed": 14, "distance_attempted": 27, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON><PERSON>", "fighter2_name": "<PERSON><PERSON><PERSON>", "weight_class": "Middleweight", "winner": "<PERSON><PERSON><PERSON>", "method": "KO/TKO", "round": 1, "time": "2:07", "stats": [{"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 1, "sig_str_landed": 26, "sig_str_attempted": 36, "sig_str_pct": 72, "total_str_landed": 26, "total_str_attempted": 36, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 20, "head_attempted": 29, "body_landed": 6, "body_attempted": 6, "leg_landed": 0, "leg_attempted": 1, "distance_landed": 26, "distance_attempted": 36, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 12, "sig_str_attempted": 16, "sig_str_pct": 75, "total_str_landed": 12, "total_str_attempted": 16, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 3, "head_attempted": 6, "body_landed": 1, "body_attempted": 1, "leg_landed": 8, "leg_attempted": 9, "distance_landed": 12, "distance_attempted": 16, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON><PERSON>", "weight_class": "Bantamweight", "winner": "<PERSON>", "method": "Submission", "round": 1, "time": "1:13", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 5, "sig_str_attempted": 11, "sig_str_pct": 45, "total_str_landed": 5, "total_str_attempted": 11, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 1, "pass": 0, "rev": 0, "ctrl_time": "0:03", "head_landed": 2, "head_attempted": 7, "body_landed": 2, "body_attempted": 2, "leg_landed": 1, "leg_attempted": 2, "distance_landed": 5, "distance_attempted": 11, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 1, "sig_str_attempted": 3, "sig_str_pct": 33, "total_str_landed": 1, "total_str_attempted": 3, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:07", "head_landed": 0, "head_attempted": 2, "body_landed": 0, "body_attempted": 0, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 1, "distance_attempted": 3, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Flyweight", "winner": "<PERSON>", "method": "Submission", "round": 3, "time": "3:03", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 20, "sig_str_attempted": 31, "sig_str_pct": 64, "total_str_landed": 34, "total_str_attempted": 46, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:40", "head_landed": 10, "head_attempted": 19, "body_landed": 9, "body_attempted": 11, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 14, "distance_attempted": 25, "clinch_landed": 4, "clinch_attempted": 4, "ground_landed": 2, "ground_attempted": 2}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 10, "sig_str_attempted": 28, "sig_str_pct": 35, "total_str_landed": 40, "total_str_attempted": 58, "td_landed": 1, "td_attempted": 3, "td_pct": 33, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:36", "head_landed": 4, "head_attempted": 16, "body_landed": 3, "body_attempted": 8, "leg_landed": 3, "leg_attempted": 4, "distance_landed": 9, "distance_attempted": 27, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 6, "sig_str_attempted": 6, "sig_str_pct": 100, "total_str_landed": 33, "total_str_attempted": 35, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 2, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 6, "head_attempted": 6, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 0, "distance_attempted": 0, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 6, "ground_attempted": 6}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 3, "sig_str_attempted": 3, "sig_str_pct": 100, "total_str_landed": 60, "total_str_attempted": 65, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "4:56", "head_landed": 3, "head_attempted": 3, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 0, "distance_attempted": 0, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 3, "ground_attempted": 3}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 4, "sig_str_attempted": 6, "sig_str_pct": 66, "total_str_landed": 4, "total_str_attempted": 6, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:43", "head_landed": 3, "head_attempted": 5, "body_landed": 1, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 0, "distance_attempted": 2, "clinch_landed": 4, "clinch_attempted": 4, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 1, "sig_str_attempted": 2, "sig_str_pct": 50, "total_str_landed": 18, "total_str_attempted": 23, "td_landed": 1, "td_attempted": 2, "td_pct": 50, "sub_att": 1, "pass": 0, "rev": 0, "ctrl_time": "1:52", "head_landed": 1, "head_attempted": 2, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 1, "distance_attempted": 2, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Lightweight", "winner": "<PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 8, "sig_str_attempted": 13, "sig_str_pct": 61, "total_str_landed": 12, "total_str_attempted": 17, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 1, "pass": 0, "rev": 0, "ctrl_time": "0:18", "head_landed": 5, "head_attempted": 10, "body_landed": 2, "body_attempted": 2, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 8, "distance_attempted": 13, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 16, "sig_str_attempted": 25, "sig_str_pct": 64, "total_str_landed": 25, "total_str_attempted": 34, "td_landed": 1, "td_attempted": 3, "td_pct": 33, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "2:13", "head_landed": 6, "head_attempted": 13, "body_landed": 4, "body_attempted": 6, "leg_landed": 6, "leg_attempted": 6, "distance_landed": 13, "distance_attempted": 20, "clinch_landed": 1, "clinch_attempted": 3, "ground_landed": 2, "ground_attempted": 2}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 21, "sig_str_attempted": 46, "sig_str_pct": 45, "total_str_landed": 21, "total_str_attempted": 46, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 16, "head_attempted": 35, "body_landed": 5, "body_attempted": 11, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 20, "distance_attempted": 45, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 23, "sig_str_attempted": 39, "sig_str_pct": 58, "total_str_landed": 23, "total_str_attempted": 39, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:13", "head_landed": 12, "head_attempted": 26, "body_landed": 6, "body_attempted": 8, "leg_landed": 5, "leg_attempted": 5, "distance_landed": 21, "distance_attempted": 36, "clinch_landed": 2, "clinch_attempted": 3, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 14, "sig_str_attempted": 33, "sig_str_pct": 42, "total_str_landed": 14, "total_str_attempted": 33, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:06", "head_landed": 7, "head_attempted": 25, "body_landed": 7, "body_attempted": 8, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 13, "distance_attempted": 32, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 22, "sig_str_attempted": 33, "sig_str_pct": 66, "total_str_landed": 35, "total_str_attempted": 50, "td_landed": 3, "td_attempted": 4, "td_pct": 75, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "2:44", "head_landed": 14, "head_attempted": 22, "body_landed": 6, "body_attempted": 9, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 16, "distance_attempted": 27, "clinch_landed": 3, "clinch_attempted": 3, "ground_landed": 3, "ground_attempted": 3}]}, {"fighter1_name": "<PERSON><PERSON><PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Bantamweight", "winner": null, "method": "Could Not Continue", "round": 2, "time": "0:15", "stats": [{"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 29, "sig_str_attempted": 42, "sig_str_pct": 69, "total_str_landed": 32, "total_str_attempted": 45, "td_landed": 1, "td_attempted": 4, "td_pct": 25, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:29", "head_landed": 8, "head_attempted": 21, "body_landed": 8, "body_attempted": 8, "leg_landed": 13, "leg_attempted": 13, "distance_landed": 25, "distance_attempted": 38, "clinch_landed": 4, "clinch_attempted": 4, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 25, "sig_str_attempted": 36, "sig_str_pct": 69, "total_str_landed": 30, "total_str_attempted": 41, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:14", "head_landed": 3, "head_attempted": 10, "body_landed": 6, "body_attempted": 6, "leg_landed": 16, "leg_attempted": 20, "distance_landed": 23, "distance_attempted": 34, "clinch_landed": 2, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 1, "sig_str_attempted": 1, "sig_str_pct": 100, "total_str_landed": 1, "total_str_attempted": 1, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 0, "head_attempted": 0, "body_landed": 0, "body_attempted": 0, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 1, "distance_attempted": 1, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 0, "sig_str_attempted": 0, "sig_str_pct": 0, "total_str_landed": 0, "total_str_attempted": 0, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 0, "head_attempted": 0, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 0, "distance_attempted": 0, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "Abu <PERSON>", "fighter2_name": "Sedriques <PERSON>", "weight_class": "Middleweight", "winner": "Sedriques <PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "Abu <PERSON>", "round": 1, "kd": 0, "sig_str_landed": 5, "sig_str_attempted": 9, "sig_str_pct": 55, "total_str_landed": 50, "total_str_attempted": 55, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:09", "head_landed": 4, "head_attempted": 8, "body_landed": 0, "body_attempted": 0, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 2, "distance_attempted": 3, "clinch_landed": 0, "clinch_attempted": 2, "ground_landed": 3, "ground_attempted": 4}, {"fighter_name": "Sedriques <PERSON>", "round": 1, "kd": 0, "sig_str_landed": 5, "sig_str_attempted": 7, "sig_str_pct": 71, "total_str_landed": 43, "total_str_attempted": 45, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "4:10", "head_landed": 2, "head_attempted": 4, "body_landed": 1, "body_attempted": 1, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 2, "distance_attempted": 4, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 3, "ground_attempted": 3}, {"fighter_name": "Abu <PERSON>", "round": 2, "kd": 0, "sig_str_landed": 20, "sig_str_attempted": 50, "sig_str_pct": 40, "total_str_landed": 21, "total_str_attempted": 51, "td_landed": 1, "td_attempted": 2, "td_pct": 50, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:23", "head_landed": 13, "head_attempted": 39, "body_landed": 4, "body_attempted": 7, "leg_landed": 3, "leg_attempted": 4, "distance_landed": 14, "distance_attempted": 42, "clinch_landed": 2, "clinch_attempted": 2, "ground_landed": 4, "ground_attempted": 6}, {"fighter_name": "Sedriques <PERSON>", "round": 2, "kd": 0, "sig_str_landed": 14, "sig_str_attempted": 29, "sig_str_pct": 48, "total_str_landed": 14, "total_str_attempted": 29, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 3, "head_attempted": 16, "body_landed": 2, "body_attempted": 2, "leg_landed": 9, "leg_attempted": 11, "distance_landed": 14, "distance_attempted": 29, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "Abu <PERSON>", "round": 3, "kd": 0, "sig_str_landed": 16, "sig_str_attempted": 47, "sig_str_pct": 34, "total_str_landed": 16, "total_str_attempted": 47, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 4, "head_attempted": 31, "body_landed": 9, "body_attempted": 11, "leg_landed": 3, "leg_attempted": 5, "distance_landed": 15, "distance_attempted": 45, "clinch_landed": 1, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "Sedriques <PERSON>", "round": 3, "kd": 0, "sig_str_landed": 15, "sig_str_attempted": 43, "sig_str_pct": 34, "total_str_landed": 15, "total_str_attempted": 43, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 6, "head_attempted": 25, "body_landed": 2, "body_attempted": 8, "leg_landed": 7, "leg_attempted": 10, "distance_landed": 15, "distance_attempted": 43, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON><PERSON><PERSON>", "weight_class": "Lightweight", "winner": "<PERSON>", "method": "KO/TKO", "round": 3, "time": "3:00", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 29, "sig_str_attempted": 51, "sig_str_pct": 56, "total_str_landed": 29, "total_str_attempted": 51, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 21, "head_attempted": 41, "body_landed": 4, "body_attempted": 5, "leg_landed": 4, "leg_attempted": 5, "distance_landed": 28, "distance_attempted": 50, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 44, "sig_str_attempted": 88, "sig_str_pct": 50, "total_str_landed": 44, "total_str_attempted": 88, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 23, "head_attempted": 62, "body_landed": 14, "body_attempted": 19, "leg_landed": 7, "leg_attempted": 7, "distance_landed": 40, "distance_attempted": 81, "clinch_landed": 4, "clinch_attempted": 7, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 29, "sig_str_attempted": 68, "sig_str_pct": 42, "total_str_landed": 31, "total_str_attempted": 72, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:01", "head_landed": 23, "head_attempted": 61, "body_landed": 4, "body_attempted": 5, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 28, "distance_attempted": 67, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 48, "sig_str_attempted": 84, "sig_str_pct": 57, "total_str_landed": 49, "total_str_attempted": 85, "td_landed": 0, "td_attempted": 2, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:05", "head_landed": 33, "head_attempted": 66, "body_landed": 11, "body_attempted": 14, "leg_landed": 4, "leg_attempted": 4, "distance_landed": 46, "distance_attempted": 78, "clinch_landed": 2, "clinch_attempted": 6, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 1, "sig_str_landed": 34, "sig_str_attempted": 58, "sig_str_pct": 58, "total_str_landed": 34, "total_str_attempted": 58, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 31, "head_attempted": 55, "body_landed": 3, "body_attempted": 3, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 33, "distance_attempted": 57, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 1, "ground_attempted": 1}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 22, "sig_str_pct": 50, "total_str_landed": 11, "total_str_attempted": 22, "td_landed": 0, "td_attempted": 3, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 7, "head_attempted": 17, "body_landed": 2, "body_attempted": 3, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 11, "distance_attempted": 22, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Featherweight", "winner": "<PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 10, "sig_str_attempted": 17, "sig_str_pct": 58, "total_str_landed": 17, "total_str_attempted": 26, "td_landed": 1, "td_attempted": 2, "td_pct": 50, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:45", "head_landed": 4, "head_attempted": 10, "body_landed": 1, "body_attempted": 2, "leg_landed": 5, "leg_attempted": 5, "distance_landed": 8, "distance_attempted": 15, "clinch_landed": 2, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 18, "sig_str_attempted": 28, "sig_str_pct": 64, "total_str_landed": 49, "total_str_attempted": 64, "td_landed": 1, "td_attempted": 3, "td_pct": 33, "sub_att": 0, "pass": 0, "rev": 1, "ctrl_time": "2:37", "head_landed": 13, "head_attempted": 21, "body_landed": 3, "body_attempted": 4, "leg_landed": 2, "leg_attempted": 3, "distance_landed": 6, "distance_attempted": 11, "clinch_landed": 5, "clinch_attempted": 5, "ground_landed": 7, "ground_attempted": 12}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 1, "sig_str_attempted": 1, "sig_str_pct": 100, "total_str_landed": 6, "total_str_attempted": 6, "td_landed": 1, "td_attempted": 3, "td_pct": 33, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:32", "head_landed": 1, "head_attempted": 1, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 0, "distance_attempted": 0, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 13, "sig_str_pct": 84, "total_str_landed": 30, "total_str_attempted": 34, "td_landed": 2, "td_attempted": 3, "td_pct": 66, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "2:59", "head_landed": 4, "head_attempted": 6, "body_landed": 4, "body_attempted": 4, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 1, "distance_attempted": 1, "clinch_landed": 4, "clinch_attempted": 5, "ground_landed": 6, "ground_attempted": 7}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 39, "sig_str_attempted": 54, "sig_str_pct": 72, "total_str_landed": 47, "total_str_attempted": 62, "td_landed": 0, "td_attempted": 2, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:27", "head_landed": 32, "head_attempted": 46, "body_landed": 5, "body_attempted": 5, "leg_landed": 2, "leg_attempted": 3, "distance_landed": 28, "distance_attempted": 41, "clinch_landed": 2, "clinch_attempted": 3, "ground_landed": 9, "ground_attempted": 10}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 19, "sig_str_attempted": 40, "sig_str_pct": 47, "total_str_landed": 21, "total_str_attempted": 42, "td_landed": 1, "td_attempted": 3, "td_pct": 33, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "1:06", "head_landed": 14, "head_attempted": 33, "body_landed": 3, "body_attempted": 5, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 17, "distance_attempted": 36, "clinch_landed": 2, "clinch_attempted": 4, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON><PERSON>", "fighter2_name": "<PERSON><PERSON>", "weight_class": "Women's Strawweight", "winner": "<PERSON><PERSON><PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 1, "sig_str_landed": 30, "sig_str_attempted": 53, "sig_str_pct": 56, "total_str_landed": 31, "total_str_attempted": 54, "td_landed": 0, "td_attempted": 3, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:18", "head_landed": 19, "head_attempted": 40, "body_landed": 10, "body_attempted": 12, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 22, "distance_attempted": 44, "clinch_landed": 8, "clinch_attempted": 9, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 16, "sig_str_attempted": 46, "sig_str_pct": 34, "total_str_landed": 16, "total_str_attempted": 46, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 9, "head_attempted": 37, "body_landed": 5, "body_attempted": 7, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 15, "distance_attempted": 44, "clinch_landed": 1, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 23, "sig_str_pct": 47, "total_str_landed": 37, "total_str_attempted": 51, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 6, "head_attempted": 16, "body_landed": 5, "body_attempted": 7, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 8, "distance_attempted": 20, "clinch_landed": 3, "clinch_attempted": 3, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 22, "sig_str_pct": 50, "total_str_landed": 28, "total_str_attempted": 40, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "3:48", "head_landed": 8, "head_attempted": 17, "body_landed": 0, "body_attempted": 2, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 4, "distance_attempted": 14, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 7, "ground_attempted": 8}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 33, "sig_str_attempted": 70, "sig_str_pct": 47, "total_str_landed": 41, "total_str_attempted": 78, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 22, "head_attempted": 59, "body_landed": 11, "body_attempted": 11, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 30, "distance_attempted": 67, "clinch_landed": 3, "clinch_attempted": 3, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 20, "sig_str_attempted": 33, "sig_str_pct": 60, "total_str_landed": 21, "total_str_attempted": 35, "td_landed": 0, "td_attempted": 2, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:52", "head_landed": 14, "head_attempted": 26, "body_landed": 3, "body_attempted": 4, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 18, "distance_attempted": 31, "clinch_landed": 2, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Middleweight", "winner": "<PERSON><PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 46, "sig_str_attempted": 66, "sig_str_pct": 69, "total_str_landed": 46, "total_str_attempted": 66, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 16, "head_attempted": 31, "body_landed": 10, "body_attempted": 12, "leg_landed": 20, "leg_attempted": 23, "distance_landed": 46, "distance_attempted": 66, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 28, "sig_str_attempted": 42, "sig_str_pct": 66, "total_str_landed": 28, "total_str_attempted": 42, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 20, "head_attempted": 33, "body_landed": 3, "body_attempted": 4, "leg_landed": 5, "leg_attempted": 5, "distance_landed": 27, "distance_attempted": 41, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 43, "sig_str_attempted": 58, "sig_str_pct": 74, "total_str_landed": 82, "total_str_attempted": 101, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 21, "head_attempted": 36, "body_landed": 9, "body_attempted": 9, "leg_landed": 13, "leg_attempted": 13, "distance_landed": 24, "distance_attempted": 35, "clinch_landed": 7, "clinch_attempted": 9, "ground_landed": 12, "ground_attempted": 14}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 26, "sig_str_attempted": 36, "sig_str_pct": 72, "total_str_landed": 44, "total_str_attempted": 56, "td_landed": 1, "td_attempted": 2, "td_pct": 50, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "2:25", "head_landed": 20, "head_attempted": 30, "body_landed": 3, "body_attempted": 3, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 8, "distance_attempted": 15, "clinch_landed": 3, "clinch_attempted": 3, "ground_landed": 15, "ground_attempted": 18}, {"fighter_name": "<PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 24, "sig_str_attempted": 30, "sig_str_pct": 80, "total_str_landed": 94, "total_str_attempted": 107, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 19, "head_attempted": 25, "body_landed": 3, "body_attempted": 3, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 4, "distance_attempted": 5, "clinch_landed": 8, "clinch_attempted": 12, "ground_landed": 12, "ground_attempted": 13}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 13, "sig_str_pct": 84, "total_str_landed": 35, "total_str_attempted": 40, "td_landed": 2, "td_attempted": 4, "td_pct": 50, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "4:19", "head_landed": 11, "head_attempted": 13, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 0, "distance_attempted": 0, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 10, "ground_attempted": 12}]}]}