const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '../data/ufc_data.db');
const db = new Database(dbPath, { readonly: true });

// Search for variations of Topuria
console.log('Searching for Topuria fighters:');
const topurias = db.prepare("SELECT * FROM fighters WHERE last_name LIKE '%Topuria%' OR first_name LIKE '%Topuria%'").all();
topurias.forEach(f => {
    console.log(`- ID: ${f.id}, Name: ${f.first_name} ${f.last_name}`);
});

// Search for Ilia
console.log('\nSearching for Ilia:');
const ilias = db.prepare("SELECT * FROM fighters WHERE first_name LIKE '%Ilia%'").all();
ilias.forEach(f => {
    console.log(`- ID: ${f.id}, Name: ${f.first_name} ${f.last_name}`);
});

// Check the fight we imported
console.log('\nChecking UFC 308 main event:');
const ufc308 = db.prepare(`
    SELECT f.*, e.event_name,
           f1.id as f1_id, f1.first_name as f1_first, f1.last_name as f1_last,
           f2.id as f2_id, f2.first_name as f2_first, f2.last_name as f2_last
    FROM fights f
    JOIN events e ON f.event_id = e.id
    JOIN fighters f1 ON f.fighter1_id = f1.id
    JOIN fighters f2 ON f.fighter2_id = f2.id
    WHERE e.event_name LIKE '%UFC 308%' AND f.bout LIKE '%Holloway%'
`).get();

if (ufc308) {
    console.log(`Fighter 1: ${ufc308.f1_first} ${ufc308.f1_last} (ID: ${ufc308.f1_id})`);
    console.log(`Fighter 2: ${ufc308.f2_first} ${ufc308.f2_last} (ID: ${ufc308.f2_id})`);
} else {
    console.log('UFC 308 main event not found!');
}

db.close();