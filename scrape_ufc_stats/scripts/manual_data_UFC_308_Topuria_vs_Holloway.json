{"event": {"name": "UFC 308: <PERSON><PERSON> vs. <PERSON>", "date": "2024-10-26", "location": "Abu Dhabi, United Arab Emirates"}, "fights": [{"fighter1_name": "<PERSON><PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Featherweight", "winner": "<PERSON><PERSON>", "method": "KO/TKO", "round": 3, "time": "1:34", "stats": [{"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 12, "sig_str_attempted": 20, "sig_str_pct": 60, "total_str_landed": 12, "total_str_attempted": 20, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 12, "head_attempted": 20, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 12, "distance_attempted": 20, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 23, "sig_str_pct": 47, "total_str_landed": 11, "total_str_attempted": 23, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 10, "head_attempted": 22, "body_landed": 1, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 11, "distance_attempted": 23, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 22, "sig_str_attempted": 44, "sig_str_pct": 50, "total_str_landed": 22, "total_str_attempted": 44, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 14, "head_attempted": 33, "body_landed": 8, "body_attempted": 11, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 22, "distance_attempted": 44, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 19, "sig_str_attempted": 33, "sig_str_pct": 57, "total_str_landed": 20, "total_str_attempted": 34, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 13, "head_attempted": 26, "body_landed": 6, "body_attempted": 7, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 19, "distance_attempted": 33, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 3, "kd": 1, "sig_str_landed": 12, "sig_str_attempted": 20, "sig_str_pct": 60, "total_str_landed": 12, "total_str_attempted": 20, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 11, "head_attempted": 19, "body_landed": 1, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 12, "distance_attempted": 20, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 3, "sig_str_attempted": 6, "sig_str_pct": 50, "total_str_landed": 3, "total_str_attempted": 6, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 3, "head_attempted": 6, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 3, "distance_attempted": 6, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON><PERSON><PERSON><PERSON>", "weight_class": "Middleweight", "winner": "<PERSON><PERSON><PERSON><PERSON>", "method": "Submission", "round": 1, "time": "3:34", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 5, "sig_str_attempted": 10, "sig_str_pct": 50, "total_str_landed": 5, "total_str_attempted": 10, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 1, "ctrl_time": "3:40", "head_landed": 3, "head_attempted": 7, "body_landed": 2, "body_attempted": 3, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 2, "distance_attempted": 6, "clinch_landed": 1, "clinch_attempted": 2, "ground_landed": 2, "ground_attempted": 2}, {"fighter_name": "<PERSON><PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 10, "sig_str_attempted": 12, "sig_str_pct": 83, "total_str_landed": 10, "total_str_attempted": 12, "td_landed": 1, "td_attempted": 1, "td_pct": 100, "sub_att": 1, "pass": 0, "rev": 0, "ctrl_time": "1:20", "head_landed": 9, "head_attempted": 11, "body_landed": 1, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 2, "distance_attempted": 2, "clinch_landed": 1, "clinch_attempted": 2, "ground_landed": 7, "ground_attempted": 8}]}, {"fighter1_name": "<PERSON><PERSON><PERSON>", "fighter2_name": "Aleksandar <PERSON>", "weight_class": "Light Heavyweight", "winner": "<PERSON><PERSON><PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 13, "sig_str_attempted": 21, "sig_str_pct": 61, "total_str_landed": 13, "total_str_attempted": 21, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 9, "head_attempted": 14, "body_landed": 1, "body_attempted": 3, "leg_landed": 3, "leg_attempted": 4, "distance_landed": 11, "distance_attempted": 18, "clinch_landed": 2, "clinch_attempted": 3, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "Aleksandar <PERSON>", "round": 1, "kd": 0, "sig_str_landed": 8, "sig_str_attempted": 17, "sig_str_pct": 47, "total_str_landed": 8, "total_str_attempted": 17, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 8, "head_attempted": 17, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 8, "distance_attempted": 17, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 14, "sig_str_attempted": 24, "sig_str_pct": 58, "total_str_landed": 14, "total_str_attempted": 24, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 11, "head_attempted": 17, "body_landed": 0, "body_attempted": 3, "leg_landed": 3, "leg_attempted": 4, "distance_landed": 14, "distance_attempted": 24, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "Aleksandar <PERSON>", "round": 2, "kd": 0, "sig_str_landed": 12, "sig_str_attempted": 38, "sig_str_pct": 31, "total_str_landed": 13, "total_str_attempted": 39, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 11, "head_attempted": 35, "body_landed": 1, "body_attempted": 3, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 12, "distance_attempted": 38, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 18, "sig_str_attempted": 24, "sig_str_pct": 75, "total_str_landed": 18, "total_str_attempted": 24, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 12, "head_attempted": 16, "body_landed": 3, "body_attempted": 4, "leg_landed": 3, "leg_attempted": 4, "distance_landed": 18, "distance_attempted": 24, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "Aleksandar <PERSON>", "round": 3, "kd": 0, "sig_str_landed": 9, "sig_str_attempted": 19, "sig_str_pct": 47, "total_str_landed": 9, "total_str_attempted": 19, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 9, "head_attempted": 19, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 9, "distance_attempted": 19, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Featherweight", "winner": "<PERSON><PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 14, "sig_str_attempted": 29, "sig_str_pct": 48, "total_str_landed": 14, "total_str_attempted": 29, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 7, "head_attempted": 17, "body_landed": 1, "body_attempted": 4, "leg_landed": 6, "leg_attempted": 8, "distance_landed": 14, "distance_attempted": 29, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 19, "sig_str_attempted": 26, "sig_str_pct": 73, "total_str_landed": 19, "total_str_attempted": 26, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 10, "head_attempted": 15, "body_landed": 4, "body_attempted": 6, "leg_landed": 5, "leg_attempted": 5, "distance_landed": 19, "distance_attempted": 26, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 18, "sig_str_attempted": 31, "sig_str_pct": 58, "total_str_landed": 18, "total_str_attempted": 31, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 15, "head_attempted": 27, "body_landed": 1, "body_attempted": 2, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 18, "distance_attempted": 31, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 5, "sig_str_attempted": 23, "sig_str_pct": 21, "total_str_landed": 5, "total_str_attempted": 23, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 5, "head_attempted": 23, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 5, "distance_attempted": 23, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 14, "sig_str_attempted": 23, "sig_str_pct": 60, "total_str_landed": 14, "total_str_attempted": 23, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 10, "head_attempted": 16, "body_landed": 1, "body_attempted": 4, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 14, "distance_attempted": 23, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 9, "sig_str_attempted": 29, "sig_str_pct": 31, "total_str_landed": 9, "total_str_attempted": 29, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 6, "head_attempted": 24, "body_landed": 2, "body_attempted": 4, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 9, "distance_attempted": 29, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON>", "fighter2_name": "<PERSON><PERSON>", "weight_class": "Middleweight", "winner": "<PERSON><PERSON>", "method": "KO/TKO", "round": 2, "time": "4:52", "stats": [{"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 14, "sig_str_attempted": 29, "sig_str_pct": 48, "total_str_landed": 15, "total_str_attempted": 30, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 10, "head_attempted": 23, "body_landed": 1, "body_attempted": 3, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 14, "distance_attempted": 29, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 15, "sig_str_attempted": 31, "sig_str_pct": 48, "total_str_landed": 15, "total_str_attempted": 31, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 9, "head_attempted": 24, "body_landed": 3, "body_attempted": 4, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 15, "distance_attempted": 31, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 2, "sig_str_landed": 13, "sig_str_attempted": 29, "sig_str_pct": 44, "total_str_landed": 13, "total_str_attempted": 29, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 12, "head_attempted": 27, "body_landed": 1, "body_attempted": 2, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 13, "distance_attempted": 29, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 2, "sig_str_attempted": 12, "sig_str_pct": 16, "total_str_landed": 2, "total_str_attempted": 12, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 1, "head_attempted": 11, "body_landed": 1, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 2, "distance_attempted": 12, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON>", "fighter2_name": "<PERSON><PERSON><PERSON>", "weight_class": "Light Heavyweight", "winner": "<PERSON><PERSON>", "method": "KO/TKO", "round": 1, "time": "0:51", "stats": [{"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 1, "sig_str_landed": 12, "sig_str_attempted": 23, "sig_str_pct": 52, "total_str_landed": 12, "total_str_attempted": 23, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 10, "head_attempted": 21, "body_landed": 2, "body_attempted": 2, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 12, "distance_attempted": 23, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 3, "sig_str_attempted": 18, "sig_str_pct": 16, "total_str_landed": 3, "total_str_attempted": 18, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 2, "head_attempted": 16, "body_landed": 1, "body_attempted": 2, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 3, "distance_attempted": 18, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Welterweight", "winner": "<PERSON>", "method": "KO/TKO", "round": 1, "time": "1:30", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 23, "sig_str_pct": 47, "total_str_landed": 11, "total_str_attempted": 23, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 10, "head_attempted": 22, "body_landed": 1, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 11, "distance_attempted": 23, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 7, "sig_str_attempted": 17, "sig_str_pct": 41, "total_str_landed": 7, "total_str_attempted": 17, "td_landed": 0, "td_attempted": 3, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 5, "head_attempted": 14, "body_landed": 2, "body_attempted": 3, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 7, "distance_attempted": 17, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 1, "sig_str_landed": 25, "sig_str_attempted": 37, "sig_str_pct": 67, "total_str_landed": 25, "total_str_attempted": 37, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 20, "head_attempted": 30, "body_landed": 5, "body_attempted": 7, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 25, "distance_attempted": 37, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 4, "sig_str_attempted": 9, "sig_str_pct": 44, "total_str_landed": 4, "total_str_attempted": 9, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 4, "head_attempted": 9, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 4, "distance_attempted": 9, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 10, "sig_str_attempted": 25, "sig_str_pct": 40, "total_str_landed": 11, "total_str_attempted": 26, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 9, "head_attempted": 23, "body_landed": 1, "body_attempted": 2, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 10, "distance_attempted": 25, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 14, "sig_str_attempted": 37, "sig_str_pct": 37, "total_str_landed": 14, "total_str_attempted": 37, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 9, "head_attempted": 30, "body_landed": 3, "body_attempted": 5, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 14, "distance_attempted": 37, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON><PERSON>", "fighter2_name": "Myktybek Orolbai", "weight_class": "Catch Weight", "winner": "<PERSON><PERSON><PERSON>", "method": "Decision - Split", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 15, "sig_str_attempted": 36, "sig_str_pct": 41, "total_str_landed": 15, "total_str_attempted": 36, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 13, "head_attempted": 31, "body_landed": 2, "body_attempted": 5, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 14, "distance_attempted": 35, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "Myktybek Orolbai", "round": 1, "kd": 0, "sig_str_landed": 8, "sig_str_attempted": 16, "sig_str_pct": 50, "total_str_landed": 8, "total_str_attempted": 16, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 4, "head_attempted": 11, "body_landed": 0, "body_attempted": 1, "leg_landed": 4, "leg_attempted": 4, "distance_landed": 7, "distance_attempted": 15, "clinch_landed": 1, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 2, "kd": 1, "sig_str_landed": 12, "sig_str_attempted": 21, "sig_str_pct": 57, "total_str_landed": 13, "total_str_attempted": 22, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 12, "head_attempted": 21, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 12, "distance_attempted": 21, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "Myktybek Orolbai", "round": 2, "kd": 0, "sig_str_landed": 8, "sig_str_attempted": 20, "sig_str_pct": 40, "total_str_landed": 8, "total_str_attempted": 20, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 7, "head_attempted": 19, "body_landed": 1, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 8, "distance_attempted": 20, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Heavyweight", "winner": "<PERSON>", "method": "KO/TKO", "round": 1, "time": "4:27", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 1, "sig_str_attempted": 6, "sig_str_pct": 16, "total_str_landed": 2, "total_str_attempted": 7, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:38", "head_landed": 1, "head_attempted": 3, "body_landed": 0, "body_attempted": 3, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 1, "distance_attempted": 5, "clinch_landed": 0, "clinch_attempted": 1, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 18, "sig_str_attempted": 30, "sig_str_pct": 60, "total_str_landed": 19, "total_str_attempted": 31, "td_landed": 1, "td_attempted": 2, "td_pct": 50, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "4:22", "head_landed": 9, "head_attempted": 17, "body_landed": 9, "body_attempted": 13, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 8, "distance_attempted": 17, "clinch_landed": 1, "clinch_attempted": 3, "ground_landed": 9, "ground_attempted": 10}]}, {"fighter1_name": "<PERSON><PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Bantamweight", "winner": "<PERSON><PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 21, "sig_str_attempted": 39, "sig_str_pct": 53, "total_str_landed": 21, "total_str_attempted": 39, "td_landed": 1, "td_attempted": 3, "td_pct": 33, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:20", "head_landed": 14, "head_attempted": 29, "body_landed": 4, "body_attempted": 7, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 20, "distance_attempted": 37, "clinch_landed": 1, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 20, "sig_str_pct": 55, "total_str_landed": 11, "total_str_attempted": 20, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 9, "head_attempted": 17, "body_landed": 0, "body_attempted": 1, "leg_landed": 2, "leg_attempted": 2, "distance_landed": 11, "distance_attempted": 20, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 23, "sig_str_attempted": 37, "sig_str_pct": 62, "total_str_landed": 23, "total_str_attempted": 37, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 18, "head_attempted": 30, "body_landed": 5, "body_attempted": 7, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 23, "distance_attempted": 37, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 18, "sig_str_attempted": 35, "sig_str_pct": 51, "total_str_landed": 18, "total_str_attempted": 35, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 12, "head_attempted": 27, "body_landed": 4, "body_attempted": 5, "leg_landed": 2, "leg_attempted": 3, "distance_landed": 18, "distance_attempted": 35, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 32, "sig_str_attempted": 43, "sig_str_pct": 74, "total_str_landed": 32, "total_str_attempted": 43, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 25, "head_attempted": 33, "body_landed": 3, "body_attempted": 6, "leg_landed": 4, "leg_attempted": 4, "distance_landed": 32, "distance_attempted": 43, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 11, "sig_str_attempted": 23, "sig_str_pct": 47, "total_str_landed": 11, "total_str_attempted": 23, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 9, "head_attempted": 18, "body_landed": 2, "body_attempted": 5, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 11, "distance_attempted": 23, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Middleweight", "winner": "<PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 21, "sig_str_attempted": 50, "sig_str_pct": 42, "total_str_landed": 21, "total_str_attempted": 50, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 16, "head_attempted": 39, "body_landed": 5, "body_attempted": 11, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 19, "distance_attempted": 48, "clinch_landed": 2, "clinch_attempted": 2, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 33, "sig_str_attempted": 57, "sig_str_pct": 57, "total_str_landed": 33, "total_str_attempted": 57, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 25, "head_attempted": 47, "body_landed": 3, "body_attempted": 5, "leg_landed": 5, "leg_attempted": 5, "distance_landed": 29, "distance_attempted": 53, "clinch_landed": 4, "clinch_attempted": 4, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 17, "sig_str_attempted": 48, "sig_str_pct": 35, "total_str_landed": 17, "total_str_attempted": 48, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 14, "head_attempted": 43, "body_landed": 2, "body_attempted": 4, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 17, "distance_attempted": 48, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 29, "sig_str_attempted": 45, "sig_str_pct": 64, "total_str_landed": 29, "total_str_attempted": 45, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 24, "head_attempted": 40, "body_landed": 5, "body_attempted": 5, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 29, "distance_attempted": 45, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 23, "sig_str_attempted": 59, "sig_str_pct": 38, "total_str_landed": 24, "total_str_attempted": 60, "td_landed": 0, "td_attempted": 1, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 20, "head_attempted": 52, "body_landed": 3, "body_attempted": 7, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 23, "distance_attempted": 59, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 29, "sig_str_attempted": 53, "sig_str_pct": 54, "total_str_landed": 29, "total_str_attempted": 53, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 22, "head_attempted": 45, "body_landed": 6, "body_attempted": 7, "leg_landed": 1, "leg_attempted": 1, "distance_landed": 29, "distance_attempted": 53, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON><PERSON>", "fighter2_name": "<PERSON>", "weight_class": "Welterweight", "winner": "<PERSON><PERSON><PERSON>", "method": "Decision - Unanimous", "round": 3, "time": "5:00", "stats": [{"fighter_name": "<PERSON><PERSON><PERSON>", "round": 1, "kd": 0, "sig_str_landed": 15, "sig_str_attempted": 31, "sig_str_pct": 48, "total_str_landed": 17, "total_str_attempted": 33, "td_landed": 1, "td_attempted": 3, "td_pct": 33, "sub_att": 0, "pass": 1, "rev": 0, "ctrl_time": "1:29", "head_landed": 12, "head_attempted": 24, "body_landed": 3, "body_attempted": 7, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 11, "distance_attempted": 25, "clinch_landed": 1, "clinch_attempted": 3, "ground_landed": 3, "ground_attempted": 3}, {"fighter_name": "<PERSON>", "round": 1, "kd": 0, "sig_str_landed": 5, "sig_str_attempted": 19, "sig_str_pct": 26, "total_str_landed": 5, "total_str_attempted": 19, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 5, "head_attempted": 18, "body_landed": 0, "body_attempted": 1, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 5, "distance_attempted": 19, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 2, "kd": 0, "sig_str_landed": 25, "sig_str_attempted": 37, "sig_str_pct": 67, "total_str_landed": 26, "total_str_attempted": 38, "td_landed": 1, "td_attempted": 3, "td_pct": 33, "sub_att": 0, "pass": 1, "rev": 0, "ctrl_time": "3:00", "head_landed": 19, "head_attempted": 26, "body_landed": 4, "body_attempted": 8, "leg_landed": 2, "leg_attempted": 3, "distance_landed": 14, "distance_attempted": 24, "clinch_landed": 2, "clinch_attempted": 3, "ground_landed": 9, "ground_attempted": 10}, {"fighter_name": "<PERSON>", "round": 2, "kd": 0, "sig_str_landed": 5, "sig_str_attempted": 14, "sig_str_pct": 35, "total_str_landed": 5, "total_str_attempted": 14, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 5, "head_attempted": 14, "body_landed": 0, "body_attempted": 0, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 4, "distance_attempted": 13, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 1, "ground_attempted": 1}, {"fighter_name": "<PERSON><PERSON><PERSON>", "round": 3, "kd": 0, "sig_str_landed": 21, "sig_str_attempted": 40, "sig_str_pct": 52, "total_str_landed": 22, "total_str_attempted": 41, "td_landed": 1, "td_attempted": 3, "td_pct": 33, "sub_att": 0, "pass": 1, "rev": 0, "ctrl_time": "1:42", "head_landed": 12, "head_attempted": 30, "body_landed": 6, "body_attempted": 7, "leg_landed": 3, "leg_attempted": 3, "distance_landed": 14, "distance_attempted": 32, "clinch_landed": 4, "clinch_attempted": 5, "ground_landed": 3, "ground_attempted": 3}, {"fighter_name": "<PERSON>", "round": 3, "kd": 0, "sig_str_landed": 6, "sig_str_attempted": 16, "sig_str_pct": 37, "total_str_landed": 7, "total_str_attempted": 17, "td_landed": 0, "td_attempted": 0, "td_pct": 0, "sub_att": 0, "pass": 0, "rev": 0, "ctrl_time": "0:00", "head_landed": 4, "head_attempted": 13, "body_landed": 2, "body_attempted": 3, "leg_landed": 0, "leg_attempted": 0, "distance_landed": 6, "distance_attempted": 16, "clinch_landed": 0, "clinch_attempted": 0, "ground_landed": 0, "ground_attempted": 0}]}, {"fighter1_name": "<PERSON><PERSON>", "fighter2_name": "<PERSON><PERSON><PERSON>", "weight_class": "Middleweight", "winner": "<PERSON><PERSON>", "method": "Submission", "round": 3, "time": "3:14", "stats": []}]}