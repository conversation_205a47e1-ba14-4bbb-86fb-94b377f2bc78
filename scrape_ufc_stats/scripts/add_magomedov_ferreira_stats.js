const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '../data/ufc_data.db');
const db = new Database(dbPath);

// Find the fight
const fight = db.prepare(`
    SELECT f.*, 
           f1.first_name || ' ' || f1.last_name as fighter1_name,
           f2.first_name || ' ' || f2.last_name as fighter2_name
    FROM fights f
    JOIN fighters f1 ON f.fighter1_id = f1.id
    JOIN fighters f2 ON f.fighter2_id = f2.id
    WHERE f1.last_name = 'Magomedov' AND f1.first_name = 'Abus'
      AND f2.last_name = '<PERSON><PERSON><PERSON>' AND f2.first_name = '<PERSON>runno'
    ORDER BY f.id DESC
    LIMIT 1
`).get();

if (!fight) {
    console.error('Fight not found!');
    process.exit(1);
}

console.log('Found fight:', fight.fighter1_name, 'vs', fight.fighter2_name);
console.log('Fight ID:', fight.id);

// Prepare insert statement
const insertStats = db.prepare(`
    INSERT INTO fight_stats (
        fight_id, fighter_id, round_number, knockdowns, sig_strikes_landed, sig_strikes_attempted,
        sig_strikes_pct, total_strikes_landed, total_strikes_attempted, takedowns_landed,
        takedowns_attempted, takedowns_pct, submission_attempts, reversals, control_time,
        head_strikes, body_strikes, leg_strikes, distance_strikes, clinch_strikes, ground_strikes
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`);

// Stats data
const stats = [
    // Abus Magomedov - Round 1
    {
        fighter_id: fight.fighter1_id,
        round: 1,
        kd: 0,
        sig_str_landed: 14,
        sig_str_attempted: 29,
        sig_str_pct: 48,
        total_str_landed: 14,
        total_str_attempted: 29,
        td_landed: 0,
        td_attempted: 0,
        td_pct: 0,
        sub_att: 0,
        rev: 0,
        ctrl_time: '0:00',
        head_landed: 4,
        body_landed: 5,
        leg_landed: 5,
        distance_landed: 14,
        clinch_landed: 0,
        ground_landed: 0
    },
    // Brunno Ferreira - Round 1
    {
        fighter_id: fight.fighter2_id,
        round: 1,
        kd: 0,
        sig_str_landed: 2,
        sig_str_attempted: 12,
        sig_str_pct: 16,
        total_str_landed: 2,
        total_str_attempted: 12,
        td_landed: 0,
        td_attempted: 0,
        td_pct: 0,
        sub_att: 0,
        rev: 0,
        ctrl_time: '0:00',
        head_landed: 0,
        body_landed: 2,
        leg_landed: 0,
        distance_landed: 2,
        clinch_landed: 0,
        ground_landed: 0
    },
    // Abus Magomedov - Round 2
    {
        fighter_id: fight.fighter1_id,
        round: 2,
        kd: 0,
        sig_str_landed: 8,
        sig_str_attempted: 23,
        sig_str_pct: 34,
        total_str_landed: 21,
        total_str_attempted: 37,
        td_landed: 2,
        td_attempted: 5,
        td_pct: 40,
        sub_att: 0,
        rev: 0,
        ctrl_time: '2:14',
        head_landed: 3,
        body_landed: 2,
        leg_landed: 3,
        distance_landed: 8,
        clinch_landed: 0,
        ground_landed: 0
    },
    // Brunno Ferreira - Round 2
    {
        fighter_id: fight.fighter2_id,
        round: 2,
        kd: 0,
        sig_str_landed: 20,
        sig_str_attempted: 42,
        sig_str_pct: 47,
        total_str_landed: 22,
        total_str_attempted: 47,
        td_landed: 0,
        td_attempted: 0,
        td_pct: 0,
        sub_att: 1,
        rev: 0,
        ctrl_time: '0:06',
        head_landed: 16,
        body_landed: 4,
        leg_landed: 0,
        distance_landed: 18,
        clinch_landed: 0,
        ground_landed: 2
    },
    // Abus Magomedov - Round 3
    {
        fighter_id: fight.fighter1_id,
        round: 3,
        kd: 0,
        sig_str_landed: 4,
        sig_str_attempted: 7,
        sig_str_pct: 57,
        total_str_landed: 10,
        total_str_attempted: 15,
        td_landed: 2,
        td_attempted: 3,
        td_pct: 66,
        sub_att: 1,
        rev: 0,
        ctrl_time: '1:22',
        head_landed: 3,
        body_landed: 1,
        leg_landed: 0,
        distance_landed: 4,
        clinch_landed: 0,
        ground_landed: 0
    },
    // Brunno Ferreira - Round 3
    {
        fighter_id: fight.fighter2_id,
        round: 3,
        kd: 0,
        sig_str_landed: 7,
        sig_str_attempted: 17,
        sig_str_pct: 41,
        total_str_landed: 12,
        total_str_attempted: 24,
        td_landed: 0,
        td_attempted: 0,
        td_pct: 0,
        sub_att: 0,
        rev: 1,
        ctrl_time: '0:07',
        head_landed: 4,
        body_landed: 3,
        leg_landed: 0,
        distance_landed: 6,
        clinch_landed: 1,
        ground_landed: 0
    }
];

try {
    db.exec('BEGIN TRANSACTION');
    
    // Insert all stats
    for (const stat of stats) {
        insertStats.run(
            fight.id,
            stat.fighter_id,
            stat.round,
            stat.kd,
            stat.sig_str_landed,
            stat.sig_str_attempted,
            stat.sig_str_pct,
            stat.total_str_landed,
            stat.total_str_attempted,
            stat.td_landed,
            stat.td_attempted,
            stat.td_pct,
            stat.sub_att,
            stat.rev,
            stat.ctrl_time,
            stat.head_landed,
            stat.body_landed,
            stat.leg_landed,
            stat.distance_landed,
            stat.clinch_landed,
            stat.ground_landed
        );
        
        const fighterName = stat.fighter_id === fight.fighter1_id ? fight.fighter1_name : fight.fighter2_name;
        console.log(`Added stats for ${fighterName} - Round ${stat.round}`);
    }
    
    db.exec('COMMIT');
    console.log('\nSuccessfully added fight statistics!');
    
} catch (error) {
    db.exec('ROLLBACK');
    console.error('Error adding stats:', error);
    process.exit(1);
} finally {
    db.close();
}