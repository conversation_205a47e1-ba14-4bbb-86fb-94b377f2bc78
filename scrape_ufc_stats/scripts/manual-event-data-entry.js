const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

// Get command line arguments
const args = process.argv.slice(2);
if (args.length < 1) {
    console.error('Usage: node manual-event-data-entry.js <json-file-path>');
    process.exit(1);
}

const jsonFilePath = args[0];
if (!fs.existsSync(jsonFilePath)) {
    console.error(`JSON file not found: ${jsonFilePath}`);
    process.exit(1);
}

// Load the JSON data
const eventData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));

// Connect to database
const dbPath = path.join(__dirname, '../data/ufc_data.db');
const db = new Database(dbPath);

// Helper function to get or create fighter
function getOrCreateFighter(fighterName) {
    // Split name into first and last name
    const nameParts = fighterName.trim().split(' ');
    const firstName = nameParts[0];
    const lastName = nameParts.slice(1).join(' ') || '';
    
    // First check if fighter exists by full name
    let fighter = db.prepare(`
        SELECT id FROM fighters 
        WHERE first_name = ? AND last_name = ?
    `).get(firstName, lastName);
    
    if (!fighter) {
        // Create new fighter with minimal data
        const result = db.prepare(`
            INSERT INTO fighters (first_name, last_name, created_at)
            VALUES (?, ?, datetime('now'))
        `).run(firstName, lastName);
        
        fighter = { id: result.lastInsertRowid };
        console.log(`Created new fighter: ${fighterName} (ID: ${fighter.id})`);
    } else {
        console.log(`Found existing fighter: ${fighterName} (ID: ${fighter.id})`);
    }
    
    return fighter.id;
}

// Start transaction
const insertEvent = db.prepare(`
    INSERT OR REPLACE INTO events (event_name, date, location)
    VALUES (?, ?, ?)
`);

const insertFight = db.prepare(`
    INSERT INTO fights (
        event_id, fighter1_id, fighter2_id, result_method,
        result_round, result_time, bout, winner_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
`);

const insertFightStats = db.prepare(`
    INSERT INTO fight_stats (
        fight_id, fighter_id, round_number, knockdowns, sig_strikes_landed, sig_strikes_attempted,
        sig_strikes_pct, total_strikes_landed, total_strikes_attempted, takedowns_landed,
        takedowns_attempted, takedowns_pct, submission_attempts, reversals, control_time,
        head_strikes, body_strikes, leg_strikes, distance_strikes, clinch_strikes, ground_strikes
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`);

try {
    db.exec('BEGIN TRANSACTION');
    
    // Insert event
    const eventResult = insertEvent.run(
        eventData.event.name,
        eventData.event.date,
        eventData.event.location
    );
    const eventId = eventResult.lastInsertRowid;
    console.log(`\nInserted event: ${eventData.event.name} (ID: ${eventId})`);
    
    // Process each fight
    for (const fight of eventData.fights) {
        console.log(`\nProcessing fight: ${fight.fighter1_name} vs ${fight.fighter2_name}`);
        
        // Get or create fighters
        const fighter1Id = getOrCreateFighter(fight.fighter1_name);
        const fighter2Id = getOrCreateFighter(fight.fighter2_name);
        
        // Determine winner
        let winnerId = null;
        if (fight.winner === fight.fighter1_name) {
            winnerId = fighter1Id;
        } else if (fight.winner === fight.fighter2_name) {
            winnerId = fighter2Id;
        }
        
        // Insert fight
        const fightResult = insertFight.run(
            eventId,
            fighter1Id,
            fighter2Id,
            fight.method,
            fight.round,
            fight.time,
            `${fight.fighter1_name} vs. ${fight.fighter2_name}`,
            winnerId
        );
        const fightId = fightResult.lastInsertRowid;
        console.log(`  Inserted fight (ID: ${fightId})`);
        
        // Insert round-by-round stats
        if (fight.stats && fight.stats.length > 0) {
            for (const stat of fight.stats) {
                // Determine fighter ID based on name
                let fighterId;
                if (stat.fighter_name === fight.fighter1_name) {
                    fighterId = fighter1Id;
                } else if (stat.fighter_name === fight.fighter2_name) {
                    fighterId = fighter2Id;
                } else {
                    console.error(`  WARNING: Unknown fighter name in stats: ${stat.fighter_name}`);
                    continue;
                }
                
                insertFightStats.run(
                    fightId,
                    fighterId,
                    stat.round,
                    stat.kd || 0,
                    stat.sig_str_landed || 0,
                    stat.sig_str_attempted || 0,
                    stat.sig_str_pct || 0,
                    stat.total_str_landed || 0,
                    stat.total_str_attempted || 0,
                    stat.td_landed || 0,
                    stat.td_attempted || 0,
                    stat.td_pct || 0,
                    stat.sub_att || 0,
                    stat.rev || 0,
                    stat.ctrl_time || '0:00',
                    stat.head_landed || 0,
                    stat.body_landed || 0,
                    stat.leg_landed || 0,
                    stat.distance_landed || 0,
                    stat.clinch_landed || 0,
                    stat.ground_landed || 0
                );
                console.log(`  Added stats for ${stat.fighter_name} - Round ${stat.round}`);
            }
        }
    }
    
    db.exec('COMMIT');
    console.log('\nSuccessfully imported all data!');
    
} catch (error) {
    db.exec('ROLLBACK');
    console.error('Error importing data:', error);
    process.exit(1);
} finally {
    db.close();
}