const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '../data/ufc_data.db');
const db = new Database(dbPath, { readonly: true });

// Search for fighters who might be <PERSON><PERSON> by checking fights against known opponents
console.log('Searching for fighters who fought <PERSON> before UFC 308:');
const hollowayFights = db.prepare(`
    SELECT f.*, e.event_name, e.date,
           f1.first_name as f1_first, f1.last_name as f1_last, f1.id as f1_id,
           f2.first_name as f2_first, f2.last_name as f2_last, f2.id as f2_id
    FROM fights f
    JOIN events e ON f.event_id = e.id
    JOIN fighters f1 ON f.fighter1_id = f1.id
    JOIN fighters f2 ON f.fighter2_id = f2.id
    WHERE (f1.last_name = '<PERSON>' OR f2.last_name = '<PERSON>')
      AND e.event_name != 'UFC 308: <PERSON><PERSON> vs. <PERSON>'
    ORDER BY e.date DESC
    LIMIT 10
`).all();

console.log('<PERSON>\'s other fights:');
hollowayFights.forEach(fight => {
    console.log(`${fight.date}: ${fight.event_name} - ${fight.f1_first} ${fight.f1_last} vs ${fight.f2_first} ${fight.f2_last}`);
});

// Search for fighters who might have fought Josh Emmett, Ryan <PERSON>, etc. (known Topuria opponents)
console.log('\nSearching for fighters who fought Josh Emmett:');
const emmettFights = db.prepare(`
    SELECT f.*, e.event_name, e.date,
           f1.first_name as f1_first, f1.last_name as f1_last, f1.id as f1_id,
           f2.first_name as f2_first, f2.last_name as f2_last, f2.id as f2_id
    FROM fights f
    JOIN events e ON f.event_id = e.id
    JOIN fighters f1 ON f.fighter1_id = f1.id
    JOIN fighters f2 ON f.fighter2_id = f2.id
    WHERE (f1.last_name = 'Emmett' OR f2.last_name = 'Emmett')
    ORDER BY e.date DESC
    LIMIT 5
`).all();

emmettFights.forEach(fight => {
    console.log(`${fight.date}: ${fight.event_name} - ${fight.f1_first} ${fight.f1_last} vs ${fight.f2_first} ${fight.f2_last}`);
    
    // Check if either fighter could be Topuria
    const nonEmmett = fight.f1_last === 'Emmett' ? 
        {id: fight.f2_id, name: `${fight.f2_first} ${fight.f2_last}`} : 
        {id: fight.f1_id, name: `${fight.f1_first} ${fight.f1_last}`};
    
    if (nonEmmett.name.toLowerCase().includes('ilia') || nonEmmett.name.toLowerCase().includes('topuria')) {
        console.log(`  *** Potential Topuria: ${nonEmmett.name} (ID: ${nonEmmett.id})`);
    }
});

// Search by nickname variations
console.log('\nSearching by potential nicknames:');
const nicknameSearch = db.prepare(`
    SELECT id, first_name, last_name, nickname
    FROM fighters
    WHERE nickname LIKE '%Matador%' OR nickname LIKE '%Conquistador%' OR nickname LIKE '%Georgian%'
`).all();

nicknameSearch.forEach(f => {
    console.log(`- ${f.first_name} ${f.last_name} "${f.nickname}" (ID: ${f.id})`);
});

db.close();