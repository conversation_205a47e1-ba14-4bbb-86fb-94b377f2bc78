const Database = require('better-sqlite3');
const path = require('path');

const sourcePath = path.join(__dirname, '../data/ufc_data.db');
const targetPath = path.join(__dirname, '../../ufc-ranking-calculator/data/ufc_data.db');

const sourceDb = new Database(sourcePath, { readonly: true });
const targetDb = new Database(targetPath);

// Mapping of events
const eventMappings = [
    {
        name: 'UFC 308: Topuria vs. Holloway',
        sourceId: 733,
        targetId: 24
    },
    {
        name: 'UFC 294: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON> 2',
        sourceId: 734,
        targetId: 66
    },
    {
        name: 'UFC Fight Night: Adesanya vs. Imavov',
        sourceId: 735,
        targetId: 15
    }
];

try {
    targetDb.exec('BEGIN TRANSACTION');
    
    const insertFight = targetDb.prepare(`
        INSERT INTO fights (
            event_id, bout, url, fighter1_id, fighter2_id, winner_id,
            result_method, result_round, result_time, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    const insertFightStats = targetDb.prepare(`
        INSERT INTO fight_stats (
            fight_id, fighter_id, round_number, knockdowns, sig_strikes_landed,
            sig_strikes_attempted, sig_strikes_pct, total_strikes_landed,
            total_strikes_attempted, takedowns_landed, takedowns_attempted,
            takedowns_pct, submission_attempts, reversals, control_time,
            head_strikes, body_strikes, leg_strikes, distance_strikes,
            clinch_strikes, ground_strikes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    
    for (const mapping of eventMappings) {
        console.log(`\nSyncing ${mapping.name}...`);
        
        // Get fights from source with the source event ID
        const fights = sourceDb.prepare(`
            SELECT * FROM fights WHERE event_id = ?
        `).all(mapping.sourceId);
        
        console.log(`  Found ${fights.length} fights to copy`);
        
        for (const fight of fights) {
            // Insert fight with target event ID
            const fightResult = insertFight.run(
                mapping.targetId,  // Use target event ID
                fight.bout,
                fight.url,
                fight.fighter1_id,
                fight.fighter2_id,
                fight.winner_id,
                fight.result_method,
                fight.result_round,
                fight.result_time,
                fight.created_at
            );
            
            const newFightId = fightResult.lastInsertRowid;
            console.log(`    Copied fight: ${fight.bout} (new ID: ${newFightId})`);
            
            // Get fight stats for this fight from source
            const fightStats = sourceDb.prepare(`
                SELECT * FROM fight_stats WHERE fight_id = ?
            `).all(fight.id);
            
            // Copy fight stats with new fight ID
            for (const stat of fightStats) {
                insertFightStats.run(
                    newFightId,  // Use new fight ID
                    stat.fighter_id,
                    stat.round_number,
                    stat.knockdowns,
                    stat.sig_strikes_landed,
                    stat.sig_strikes_attempted,
                    stat.sig_strikes_pct,
                    stat.total_strikes_landed,
                    stat.total_strikes_attempted,
                    stat.takedowns_landed,
                    stat.takedowns_attempted,
                    stat.takedowns_pct,
                    stat.submission_attempts,
                    stat.reversals,
                    stat.control_time,
                    stat.head_strikes,
                    stat.body_strikes,
                    stat.leg_strikes,
                    stat.distance_strikes,
                    stat.clinch_strikes,
                    stat.ground_strikes
                );
            }
            
            console.log(`      Added ${fightStats.length} stat records`);
        }
    }
    
    targetDb.exec('COMMIT');
    console.log('\n✅ All fights and stats synced successfully!');
    
    // Verify the sync
    const targetFightCount = targetDb.prepare('SELECT COUNT(*) as count FROM fights').get();
    console.log(`Target database now has ${targetFightCount.count} fights`);
    
    // Check specific events
    for (const mapping of eventMappings) {
        const fightCount = targetDb.prepare('SELECT COUNT(*) as count FROM fights WHERE event_id = ?').get(mapping.targetId);
        console.log(`${mapping.name}: ${fightCount.count} fights`);
    }
    
} catch (error) {
    targetDb.exec('ROLLBACK');
    console.error('❌ Error during sync:', error);
} finally {
    sourceDb.close();
    targetDb.close();
}