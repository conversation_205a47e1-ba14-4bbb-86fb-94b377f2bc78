const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '../data/ufc_data.db');
const db = new Database(dbPath, { readonly: true });

// Find Ilia Topuria
const topuria = db.prepare("SELECT * FROM fighters WHERE last_name = 'Topuria'").get();
console.log('Ilia Topuria:', topuria);

if (topuria) {
    // Get all fights for Topuria
    const fights = db.prepare(`
        SELECT f.*, e.event_name, e.date,
               f1.first_name || ' ' || f1.last_name as fighter1_full,
               f2.first_name || ' ' || f2.last_name as fighter2_full
        FROM fights f
        JOIN events e ON f.event_id = e.id
        JOIN fighters f1 ON f.fighter1_id = f1.id
        JOIN fighters f2 ON f.fighter2_id = f2.id
        WHERE f.fighter1_id = ? OR f.fighter2_id = ?
        ORDER BY e.date DESC
    `).all(topuria.id, topuria.id);
    
    console.log(`\nFound ${fights.length} fights for Ilia Topuria:`);
    fights.forEach(fight => {
        console.log(`- ${fight.event_name} (${fight.date}): ${fight.fighter1_full} vs ${fight.fighter2_full}`);
        console.log(`  Winner ID: ${fight.winner_id}, Method: ${fight.result_method}`);
    });
    
    // Check specifically for UFC 308
    const ufc308 = fights.find(f => f.event_name.includes('UFC 308'));
    if (ufc308) {
        console.log('\n✅ UFC 308 fight found in database!');
    } else {
        console.log('\n❌ UFC 308 fight NOT found in database!');
    }
}

db.close();