const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '../data/ufc_data.db');
const db = new Database(dbPath);

// Get all table names
const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
console.log('Tables:', tables.map(t => t.name).join(', '));

// Get events table schema
const eventSchema = db.prepare("PRAGMA table_info(events)").all();
console.log('\nEvents table columns:');
eventSchema.forEach(col => {
    console.log(`  ${col.name} (${col.type})`);
});

// Get fights table schema
const fightSchema = db.prepare("PRAGMA table_info(fights)").all();
console.log('\nFights table columns:');
fightSchema.forEach(col => {
    console.log(`  ${col.name} (${col.type})`);
});

// Get fight_stats table schema
const fightStatsSchema = db.prepare("PRAGMA table_info(fight_stats)").all();
console.log('\nFight_stats table columns:');
fightStatsSchema.forEach(col => {
    console.log(`  ${col.name} (${col.type})`);
});

// Get fighters table schema
const fightersSchema = db.prepare("PRAGMA table_info(fighters)").all();
console.log('\nFighters table columns:');
fightersSchema.forEach(col => {
    console.log(`  ${col.name} (${col.type})`);
});

db.close();