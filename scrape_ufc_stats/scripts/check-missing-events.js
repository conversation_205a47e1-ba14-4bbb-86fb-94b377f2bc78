const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '../data/ufc_data.db');
const db = new Database(dbPath);

// Get all events with fight counts
const events = db.prepare(`
    SELECT e.id, e.event_name, e.date, e.location, COUNT(f.id) as fight_count
    FROM events e
    LEFT JOIN fights f ON e.id = f.event_id
    GROUP BY e.id
    ORDER BY e.date DESC
`).all();

console.log('Events in database:\n');

// Group by fight count
const eventsWithFights = events.filter(e => e.fight_count > 0);
const eventsWithoutFights = events.filter(e => e.fight_count === 0);

console.log(`Total events: ${events.length}`);
console.log(`Events with fights: ${eventsWithFights.length}`);
console.log(`Events WITHOUT fights: ${eventsWithoutFights.length}`);

if (eventsWithoutFights.length > 0) {
    console.log('\n⚠️  EVENTS WITH NO FIGHTS:');
    eventsWithoutFights.forEach(event => {
        console.log(`- ${event.event_name} (${event.date}) - ${event.location || 'No location'}`);
    });
}

// Show recent events to verify our imports
console.log('\n📅 Most recent 10 events:');
events.slice(0, 10).forEach(event => {
    console.log(`- ${event.event_name} (${event.date}) - ${event.fight_count} fights`);
});

// Check for the specific events we just imported
console.log('\n✅ Checking recently imported events:');
const recentlyImported = [
    'UFC 308: Topuria vs. Holloway',
    'UFC 294: Makhachev vs. Volkanovski 2',
    'UFC Fight Night: Adesanya vs. Imavov'
];

recentlyImported.forEach(eventName => {
    const event = events.find(e => e.event_name === eventName);
    if (event) {
        console.log(`- ${eventName}: ${event.fight_count} fights ✓`);
    } else {
        console.log(`- ${eventName}: NOT FOUND ✗`);
    }
});

db.close();