const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '../data/ufc_data.db');
const db = new Database(dbPath, { readonly: true });

// Search for Ilia Topuria who already has fights
console.log('Searching for all fighters with "Ilia" or "Topuria":');
const fighters = db.prepare(`
    SELECT f.*, COUNT(fights.id) as fight_count
    FROM fighters f
    LEFT JOIN fights ON (f.id = fights.fighter1_id OR f.id = fights.fighter2_id)
    WHERE f.first_name LIKE '%Ilia%' OR f.last_name LIKE '%Topuria%'
    GROUP BY f.id
    ORDER BY fight_count DESC
`).all();

fighters.forEach(f => {
    console.log(`- ID: ${f.id}, Name: "${f.first_name} ${f.last_name}", Nickname: "${f.nickname || 'none'}", Fights: ${f.fight_count}`);
});

// Check specifically for fighters who might be the real Ilia Topuria
console.log('\nLooking for the real Ilia Topuria by checking recent fights:');
const realIlia = db.prepare(`
    SELECT DISTINCT f.id, f.first_name, f.last_name, f.nickname
    FROM fighters f
    JOIN fights ON (f.id = fights.fighter1_id OR f.id = fights.fighter2_id)
    JOIN events e ON fights.event_id = e.id
    WHERE (f.first_name LIKE '%Ilia%' OR f.nickname LIKE '%El Matador%' OR f.nickname LIKE '%Conquistador%')
      AND e.date > '2020-01-01'
    ORDER BY e.date DESC
`).all();

console.log('Potential real Ilia Topuria candidates:');
realIlia.forEach(f => {
    console.log(`- ID: ${f.id}, Name: "${f.first_name} ${f.last_name}", Nickname: "${f.nickname || 'none'}"`);
    
    // Get their recent fights
    const recentFights = db.prepare(`
        SELECT e.event_name, e.date, fights.bout
        FROM fights
        JOIN events e ON fights.event_id = e.id
        WHERE (fights.fighter1_id = ? OR fights.fighter2_id = ?)
        ORDER BY e.date DESC
        LIMIT 3
    `).all(f.id, f.id);
    
    recentFights.forEach(fight => {
        console.log(`    ${fight.date}: ${fight.event_name} - ${fight.bout}`);
    });
    console.log('');
});

db.close();