const Database = require('better-sqlite3');
const path = require('path');

const sourcePath = path.join(__dirname, '../data/ufc_data.db');
const targetPath = path.join(__dirname, '../../ufc-ranking-calculator/data/ufc_data.db');

const sourceDb = new Database(sourcePath, { readonly: true });
const targetDb = new Database(targetPath);

console.log('Checking for missing fighters...');

// Get all fighter IDs used in the new fights
const newFighterIds = sourceDb.prepare(`
    SELECT DISTINCT f.fighter1_id as fighter_id FROM fights f
    JOIN events e ON f.event_id = e.id
    WHERE e.event_name IN (
        'UFC 308: <PERSON><PERSON> vs. <PERSON>',
        'UFC 294: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON> 2',
        'UFC Fight Night: Adesanya vs. Imavov'
    )
    UNION
    SELECT DISTINCT f.fighter2_id as fighter_id FROM fights f
    JOIN events e ON f.event_id = e.id
    WHERE e.event_name IN (
        'UFC 308: Topuria vs. <PERSON>',
        'UFC 294: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON> 2',
        'UFC Fight Night: <PERSON><PERSON><PERSON> vs. Imavov'
    )
`).all();

console.log(`Found ${newFighterIds.length} unique fighters in new events`);

// Check which ones are missing from target
const missingFighters = [];
for (const {fighter_id} of newFighterIds) {
    const exists = targetDb.prepare('SELECT id FROM fighters WHERE id = ?').get(fighter_id);
    if (!exists) {
        const fighter = sourceDb.prepare('SELECT * FROM fighters WHERE id = ?').get(fighter_id);
        if (fighter) {
            missingFighters.push(fighter);
        }
    }
}

console.log(`Found ${missingFighters.length} missing fighters to sync:`);
missingFighters.forEach(f => {
    console.log(`- ID ${f.id}: ${f.first_name} ${f.last_name} (${f.nickname || 'no nickname'})`);
});

if (missingFighters.length > 0) {
    try {
        targetDb.exec('BEGIN TRANSACTION');
        
        const insertFighter = targetDb.prepare(`
            INSERT OR REPLACE INTO fighters (
                id, first_name, last_name, nickname, url, created_at
            ) VALUES (?, ?, ?, ?, ?, ?)
        `);
        
        for (const fighter of missingFighters) {
            insertFighter.run(
                fighter.id, fighter.first_name, fighter.last_name,
                fighter.nickname, fighter.url, fighter.created_at
            );
        }
        
        targetDb.exec('COMMIT');
        console.log('\n✅ Missing fighters synced successfully!');
        
    } catch (error) {
        targetDb.exec('ROLLBACK');
        console.error('❌ Error syncing fighters:', error);
    }
} else {
    console.log('\n✅ No missing fighters found!');
}

sourceDb.close();
targetDb.close();