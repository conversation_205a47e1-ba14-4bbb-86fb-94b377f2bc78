const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '../data/ufc_data.db');
const db = new Database(dbPath);

// Check if UFC 308 event exists
const event = db.prepare(`
    SELECT * FROM events 
    WHERE event_name LIKE '%UFC 308%'
`).get();

console.log('UFC 308 Event:', event);

// Check fights for this event
if (event) {
    const fights = db.prepare(`
        SELECT f.*, 
               f1.first_name || ' ' || f1.last_name as fighter1_name,
               f2.first_name || ' ' || f2.last_name as fighter2_name,
               w.first_name || ' ' || w.last_name as winner_name
        FROM fights f
        JOIN fighters f1 ON f.fighter1_id = f1.id
        JOIN fighters f2 ON f.fighter2_id = f2.id
        LEFT JOIN fighters w ON f.winner_id = w.id
        WHERE f.event_id = ?
    `).all(event.id);
    
    console.log('\nFights in UFC 308:');
    fights.forEach((fight, index) => {
        console.log(`${index + 1}. ${fight.fighter1_name} vs ${fight.fighter2_name}`);
        console.log(`   Winner: ${fight.winner_name || 'N/A'}`);
        console.log(`   Method: ${fight.result_method}`);
        console.log(`   Round: ${fight.result_round}, Time: ${fight.result_time}`);
        
        // Check if stats exist
        const stats = db.prepare(`
            SELECT COUNT(*) as count FROM fight_stats WHERE fight_id = ?
        `).get(fight.id);
        console.log(`   Stats records: ${stats.count}`);
        
        // Show missing stats warning
        if (stats.count === 0) {
            console.log('   ⚠️  NO STATS FOUND');
        }
        console.log('');
    });
}

// Check specific fight: Topuria vs Holloway
const topuriaHolloway = db.prepare(`
    SELECT f.*, 
           f1.first_name || ' ' || f1.last_name as fighter1_name,
           f2.first_name || ' ' || f2.last_name as fighter2_name
    FROM fights f
    JOIN fighters f1 ON f.fighter1_id = f1.id
    JOIN fighters f2 ON f.fighter2_id = f2.id
    WHERE (f1.last_name = 'Topuria' AND f2.last_name = 'Holloway')
       OR (f1.last_name = 'Holloway' AND f2.last_name = 'Topuria')
    ORDER BY f.id DESC
    LIMIT 1
`).get();

console.log('\nTopuria vs Holloway fight:', topuriaHolloway);

db.close();