"""
UFC Stats Web Interface

Simple Flask web app to browse and search UFC data.
"""

from flask import Flask, render_template, request, jsonify
from ufc_database import UFCDatabase
import pandas as pd

app = Flask(__name__)
db = UFCDatabase()

@app.route('/')
def index():
    """Main dashboard with summary statistics."""
    stats = {
        'total_events': db.query("SELECT COUNT(*) as count FROM events").iloc[0]['count'],
        'total_fighters': db.query("SELECT COUNT(*) as count FROM fighters").iloc[0]['count'],
        'total_fights': db.query("SELECT COUNT(*) as count FROM fights").iloc[0]['count'],
    }
    
    # Recent events
    recent_events = db.query("""
        SELECT event_name, date, location 
        FROM events 
        ORDER BY date DESC 
        LIMIT 10
    """).to_dict('records')
    
    return render_template('index.html', stats=stats, recent_events=recent_events)

@app.route('/fighters')
def fighters():
    """Browse fighters."""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    query = """
        SELECT 
            f.first_name,
            f.last_name,
            f.nickname,
            COUNT(fi.id) as total_fights
        FROM fighters f
        LEFT JOIN fights fi ON (fi.fighter1_id = f.id OR fi.fighter2_id = f.id)
    """
    
    if search:
        query += f" WHERE f.first_name LIKE '%{search}%' OR f.last_name LIKE '%{search}%' OR f.nickname LIKE '%{search}%'"
    
    query += " GROUP BY f.id ORDER BY total_fights DESC LIMIT 50"
    
    fighters_data = db.query(query).to_dict('records')
    
    return render_template('fighters.html', fighters=fighters_data, search=search)

@app.route('/events')
def events():
    """Browse events."""
    events_data = db.query("""
        SELECT 
            e.event_name,
            e.date,
            e.location,
            COUNT(f.id) as fight_count
        FROM events e
        LEFT JOIN fights f ON e.id = f.event_id
        GROUP BY e.id
        ORDER BY e.date DESC
        LIMIT 100
    """).to_dict('records')
    
    return render_template('events.html', events=events_data)

@app.route('/search')
def search():
    """Search functionality."""
    query = request.args.get('q', '')
    results = {'fighters': [], 'events': [], 'fights': []}
    
    if query:
        # Search fighters
        fighters = db.query("""
            SELECT first_name, last_name, nickname
            FROM fighters
            WHERE first_name LIKE ? OR last_name LIKE ? OR nickname LIKE ?
            LIMIT 10
        """, (f"%{query}%", f"%{query}%", f"%{query}%"))
        results['fighters'] = fighters.to_dict('records')
        
        # Search events
        events = db.query("""
            SELECT event_name, date, location
            FROM events
            WHERE event_name LIKE ?
            LIMIT 10
        """, (f"%{query}%",))
        results['events'] = events.to_dict('records')
    
    return jsonify(results)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)