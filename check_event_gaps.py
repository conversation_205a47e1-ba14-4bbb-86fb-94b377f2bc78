import sqlite3
from datetime import datetime, timedelta

# Connect to database
conn = sqlite3.connect('scrape_ufc_stats/data/ufc_data.db')
cursor = conn.cursor()

# Get all events sorted by date
cursor.execute("SELECT event_name, date FROM events WHERE date IS NOT NULL AND date != '' ORDER BY date")

events = cursor.fetchall()
print(f'Total events in database: {len(events)}')
print(f'Date range: {events[0][1]} to {events[-1][1]}')
print()

# Find gaps
gaps = []
for i in range(1, len(events)):
    try:
        prev_date = datetime.strptime(events[i-1][1], '%Y-%m-%d')
        curr_date = datetime.strptime(events[i][1], '%Y-%m-%d')
        gap_days = (curr_date - prev_date).days
        
        if gap_days > 180:  # More than 6 months
            gaps.append({
                'days': gap_days,
                'years': gap_days / 365.25,
                'prev_event': events[i-1][0],
                'prev_date': events[i-1][1],
                'next_event': events[i][0],
                'next_date': events[i][1]
            })
    except:
        pass

# Report gaps
print('SIGNIFICANT GAPS (> 6 months):')
print('=' * 80)
for gap in sorted(gaps, key=lambda x: x['days'], reverse=True):
    print(f"Gap: {gap['days']} days ({gap['years']:.1f} years)")
    print(f"  Before: {gap['prev_event']} ({gap['prev_date']})")
    print(f"  After:  {gap['next_event']} ({gap['next_date']})")
    print()

# Check for yearly gaps
yearly_gaps = [g for g in gaps if g['days'] >= 365]
if yearly_gaps:
    print(f'\nFOUND {len(yearly_gaps)} GAPS OF 1 YEAR OR MORE!')
else:
    print('\nNo gaps of 1 year or more found.')

conn.close()