{"permissions": {"allow": ["Bash(ls:*)", "Bash(npx create-next-app:*)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "Bash(find:*)", "Bash(grep:*)", "Bash(npm run build:*)", "Bash(npx tsc:*)", "Bash(npm run lint)", "Bash(npm run typecheck:*)", "WebFetch(domain:malteranalytics.github.io)", "WebFetch(domain:www.bloodyelbow.com)", "WebFetch(domain:simplifaster.com)", "WebFetch(domain:heavyhandsmma.com)", "WebFetch(domain:pmc.ncbi.nlm.nih.gov)", "Bash(rg:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(git clone:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(python:*)", "Bash(cp:*)", "Bash(sqlite3:*)", "Bash(node:*)", "<PERSON><PERSON>(pkill:*)", "Bash(awk:*)", "<PERSON><PERSON>(sed:*)", "Bash(git fetch:*)", "Bash(npm run:*)", "WebFetch(domain:docs.anthropic.com)", "<PERSON><PERSON>(claude mcp add)", "<PERSON><PERSON>(claude mcp:*)", "mcp__browser-tools__getConsoleLogs", "mcp__browser-tools__getConsoleErrors", "mcp__browser-tools__getNetworkErrors", "mcp__browser-tools__getNetworkLogs", "<PERSON><PERSON>(true)", "mcp__browser-tools__takeScreenshot", "mcp__browser-tools__wipeLogs", "WebFetch(domain:ufcstats.com)", "WebFetch(domain:raw.githubusercontent.com)", "Bash(echo)", "<PERSON><PERSON>(mv:*)", "Bash(timeout 10 npm run dev:*)", "Bash(npx:*)", "<PERSON><PERSON>(mkdir:*)"], "deny": []}, "enableAllProjectMcpServers": false}