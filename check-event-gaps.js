const Database = require('better-sqlite3');
const path = require('path');

// Open the database
const dbPath = path.join(__dirname, 'ufc-ranking-calculator', 'data', 'ufc_data.db');
const db = new Database(dbPath, { readonly: true });

try {
  // Query all events sorted by date
  const events = db.prepare(`
    SELECT event_id, event_name, event_date
    FROM events
    ORDER BY event_date ASC
  `).all();

  console.log(`Total events found: ${events.length}`);
  console.log(`Date range: ${events[0]?.event_date} to ${events[events.length - 1]?.event_date}\n`);

  // Analyze gaps between consecutive events
  const significantGaps = [];
  
  for (let i = 1; i < events.length; i++) {
    const prevEvent = events[i - 1];
    const currEvent = events[i];
    
    // Parse dates
    const prevDate = new Date(prevEvent.event_date);
    const currDate = new Date(currEvent.event_date);
    
    // Calculate gap in days
    const gapInDays = (currDate - prevDate) / (1000 * 60 * 60 * 24);
    const gapInMonths = gapInDays / 30.44; // Average days per month
    
    // Check for gaps of 6 months or more
    if (gapInMonths >= 6) {
      significantGaps.push({
        gapInDays: Math.round(gapInDays),
        gapInMonths: Math.round(gapInMonths * 10) / 10,
        gapInYears: Math.round((gapInMonths / 12) * 10) / 10,
        beforeEvent: {
          id: prevEvent.event_id,
          name: prevEvent.event_name,
          date: prevEvent.event_date
        },
        afterEvent: {
          id: currEvent.event_id,
          name: currEvent.event_name,
          date: currEvent.event_date
        }
      });
    }
  }

  // Sort gaps by size (largest first)
  significantGaps.sort((a, b) => b.gapInDays - a.gapInDays);

  console.log('=== SIGNIFICANT GAPS (6+ MONTHS) ===\n');
  
  // Report yearly gaps first
  const yearlyGaps = significantGaps.filter(gap => gap.gapInMonths >= 12);
  if (yearlyGaps.length > 0) {
    console.log('*** YEARLY GAPS (12+ MONTHS) ***\n');
    yearlyGaps.forEach(gap => {
      console.log(`Gap: ${gap.gapInYears} years (${gap.gapInDays} days)`);
      console.log(`Before: ${gap.beforeEvent.name} (${gap.beforeEvent.date})`);
      console.log(`After:  ${gap.afterEvent.name} (${gap.afterEvent.date})`);
      console.log(`Date Range: ${gap.beforeEvent.date} to ${gap.afterEvent.date}`);
      console.log('---\n');
    });
  }

  // Report other significant gaps
  const otherGaps = significantGaps.filter(gap => gap.gapInMonths >= 6 && gap.gapInMonths < 12);
  if (otherGaps.length > 0) {
    console.log('\n*** OTHER SIGNIFICANT GAPS (6-12 MONTHS) ***\n');
    otherGaps.forEach(gap => {
      console.log(`Gap: ${gap.gapInMonths} months (${gap.gapInDays} days)`);
      console.log(`Before: ${gap.beforeEvent.name} (${gap.beforeEvent.date})`);
      console.log(`After:  ${gap.afterEvent.name} (${gap.afterEvent.date})`);
      console.log(`Date Range: ${gap.beforeEvent.date} to ${gap.afterEvent.date}`);
      console.log('---\n');
    });
  }

  // Summary statistics
  console.log('\n=== SUMMARY ===');
  console.log(`Total gaps of 6+ months: ${significantGaps.length}`);
  console.log(`Gaps of 1+ year: ${yearlyGaps.length}`);
  console.log(`Gaps of 6-12 months: ${otherGaps.length}`);
  
  if (significantGaps.length > 0) {
    console.log(`\nLargest gap: ${significantGaps[0].gapInYears} years (${significantGaps[0].gapInDays} days)`);
    console.log(`Between: ${significantGaps[0].beforeEvent.date} and ${significantGaps[0].afterEvent.date}`);
  }

} catch (error) {
  console.error('Error:', error.message);
} finally {
  db.close();
}