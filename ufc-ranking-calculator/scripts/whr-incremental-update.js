const Database = require('better-sqlite3');
const path = require('path');

/**
 * WHR Incremental Update System
 * 
 * Efficiently updates WHR ratings when new fights are added
 * without recalculating the entire division
 */

class WHRIncrementalUpdater {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        
        this.config = {
            maxNetworkDepth: 3, // How many degrees of separation to update
            convergenceThreshold: 1.0,
            maxIterations: 5,
            ratingFloor: 800,
            ratingCeiling: 2500
        };
        
        console.log('🔄 WHR Incremental Updater initialized');
    }

    /**
     * Update WHR ratings for a specific fight
     */
    async updateForFight(fightId) {
        console.log(`\n🥊 Updating WHR for fight ${fightId}...`);
        
        try {
            // Get fight details
            const fight = this.getFightDetails(fightId);
            if (!fight) {
                throw new Error(`Fight ${fightId} not found`);
            }
            
            console.log(`  📅 ${fight.date}: ${fight.fighter1_name} vs ${fight.fighter2_name} (${fight.weight_class})`);
            
            // Check if this is an edge case (NC, DQ)
            if (this.isEdgeCase(fight)) {
                console.log(`  ⚠️  Edge case fight (${fight.result_method}) - skipping WHR update`);
                return { updated: false, reason: 'edge_case' };
            }
            
            // Get affected fighter network
            const affectedFighters = await this.getAffectedFighterNetwork(fight);
            console.log(`  👥 Found ${affectedFighters.length} fighters in affected network`);
            
            // Get current ratings for affected fighters
            const currentRatings = this.getCurrentRatings(affectedFighters, fight.weight_class);
            
            // Perform incremental update
            const updatedRatings = await this.performIncrementalUpdate(
                fight, affectedFighters, currentRatings
            );
            
            // Save updated ratings
            await this.saveUpdatedRatings(updatedRatings, fight.weight_class);
            
            // Update rankings
            await this.updateRankings(fight.weight_class);
            
            // Log the update
            await this.logUpdate(fight, affectedFighters.length);
            
            console.log(`  ✅ Updated ${updatedRatings.length} fighter ratings`);
            return { 
                updated: true, 
                fightersAffected: affectedFighters.length,
                newRatings: updatedRatings
            };
            
        } catch (error) {
            console.error(`❌ Error updating fight ${fightId}:`, error);
            throw error;
        }
    }

    /**
     * Update WHR ratings for a complete event
     */
    async updateForEvent(eventId) {
        console.log(`\n🏟️  Updating WHR for event ${eventId}...`);
        
        try {
            // Get all fights from the event
            const fights = this.db.prepare(`
                SELECT f.id
                FROM fights f
                WHERE f.event_id = ?
                ORDER BY f.id
            `).all(eventId);
            
            console.log(`  📊 Processing ${fights.length} fights from event`);
            
            const results = [];
            for (const fight of fights) {
                const result = await this.updateForFight(fight.id);
                results.push(result);
            }
            
            const totalUpdated = results.filter(r => r.updated).length;
            console.log(`  ✅ Successfully updated ${totalUpdated}/${fights.length} fights`);
            
            return results;
            
        } catch (error) {
            console.error(`❌ Error updating event ${eventId}:`, error);
            throw error;
        }
    }

    /**
     * Get fight details with fighter names
     */
    getFightDetails(fightId) {
        return this.db.prepare(`
            SELECT 
                f.*,
                e.date,
                e.event_name,
                f1.first_name || ' ' || f1.last_name as fighter1_name,
                f2.first_name || ' ' || f2.last_name as fighter2_name
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE f.id = ?
        `).get(fightId);
    }

    /**
     * Check if fight is an edge case that should be excluded
     */
    isEdgeCase(fight) {
        const method = fight.result_method?.toLowerCase() || '';
        return method.includes('no contest') || 
               method.includes('nc') || 
               method.includes('dq') || 
               method.includes('disqualif');
    }

    /**
     * Get network of fighters affected by this fight
     */
    async getAffectedFighterNetwork(fight) {
        const network = new Set();
        const toProcess = [fight.fighter1_id, fight.fighter2_id];
        
        // Add the two fighters
        network.add(fight.fighter1_id);
        network.add(fight.fighter2_id);
        
        // Expand network by finding opponents (up to maxNetworkDepth)
        for (let depth = 0; depth < this.config.maxNetworkDepth; depth++) {
            const currentLevelFighters = [...toProcess];
            toProcess.length = 0;
            
            for (const fighterId of currentLevelFighters) {
                // Find recent opponents in the same division
                const opponents = this.db.prepare(`
                    SELECT DISTINCT
                        CASE 
                            WHEN f.fighter1_id = ? THEN f.fighter2_id
                            ELSE f.fighter1_id
                        END as opponent_id
                    FROM fights f
                    JOIN events e ON f.event_id = e.id
                    WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                        AND f.weight_class = ?
                        AND e.date >= date('now', '-2 years')
                        AND f.result_method NOT LIKE '%No Contest%'
                        AND f.result_method NOT LIKE '%DQ%'
                `).all(fighterId, fighterId, fighterId, fight.weight_class);
                
                for (const opponent of opponents) {
                    if (!network.has(opponent.opponent_id)) {
                        network.add(opponent.opponent_id);
                        if (depth < this.config.maxNetworkDepth - 1) {
                            toProcess.push(opponent.opponent_id);
                        }
                    }
                }
            }
            
            if (toProcess.length === 0) break;
        }
        
        return [...network];
    }

    /**
     * Get current ratings for affected fighters
     */
    getCurrentRatings(fighterIds, division) {
        if (fighterIds.length === 0) return [];
        
        const placeholders = fighterIds.map(() => '?').join(',');
        return this.db.prepare(`
            SELECT *
            FROM whr_ratings
            WHERE fighter_id IN (${placeholders})
                AND division = ?
        `).all(...fighterIds, division);
    }

    /**
     * Perform incremental rating update
     */
    async performIncrementalUpdate(fight, affectedFighters, currentRatings) {
        console.log(`  🔄 Running incremental convergence...`);
        
        // Create rating map
        const ratings = new Map();
        for (const rating of currentRatings) {
            ratings.set(rating.fighter_id, { ...rating });
        }
        
        // Get division parameters
        const divisionParams = this.db.prepare(`
            SELECT * FROM division_parameters WHERE division = ?
        `).get(fight.weight_class);
        
        if (!divisionParams) {
            throw new Error(`Division parameters not found for ${fight.weight_class}`);
        }
        
        // Run iterative update
        let iteration = 0;
        let converged = false;
        
        while (iteration < this.config.maxIterations && !converged) {
            const previousRatings = new Map();
            for (const [fighterId, rating] of ratings) {
                previousRatings.set(fighterId, rating.rating);
            }
            
            // Update ratings for the new fight
            await this.updateRatingsForFight(fight, ratings, divisionParams);
            
            // Apply strength of schedule adjustments for affected fighters
            await this.applyLocalSoSAdjustments(affectedFighters, ratings, fight.weight_class);
            
            // Check convergence
            let totalChange = 0;
            let count = 0;
            
            for (const [fighterId, rating] of ratings) {
                const prevRating = previousRatings.get(fighterId);
                if (prevRating !== undefined) {
                    totalChange += Math.abs(rating.rating - prevRating);
                    count++;
                }
            }
            
            const avgChange = count > 0 ? totalChange / count : 0;
            console.log(`    Iteration ${iteration + 1}: Avg change = ${avgChange.toFixed(2)} points`);
            
            if (avgChange < this.config.convergenceThreshold) {
                converged = true;
                console.log('    ✅ Converged!');
            }
            
            iteration++;
        }
        
        return [...ratings.values()];
    }

    /**
     * Update ratings specifically for the new fight
     */
    async updateRatingsForFight(fight, ratings, divisionParams) {
        const fighter1 = ratings.get(fight.fighter1_id);
        const fighter2 = ratings.get(fight.fighter2_id);
        
        if (!fighter1 || !fighter2) return;
        
        // Calculate expected outcome
        const ratingDiff = fighter1.rating - fighter2.rating;
        const expectedOutcome = 1 / (1 + Math.pow(10, -ratingDiff / divisionParams.rating_scale_divisor));
        
        // Determine actual outcome
        let actualOutcome;
        if (fight.result_method?.toLowerCase().includes('draw')) {
            // Handle draws (simplified for incremental update)
            actualOutcome = 0.5;
        } else if (fight.winner_id === fight.fighter1_id) {
            actualOutcome = 1;
        } else {
            actualOutcome = 0;
        }
        
        // Calculate K-factors (simplified)
        const k1 = this.calculateKFactor(fighter1, divisionParams);
        const k2 = this.calculateKFactor(fighter2, divisionParams);
        
        // Calculate rating changes
        const change1 = k1 * (actualOutcome - expectedOutcome);
        const change2 = k2 * (expectedOutcome - actualOutcome);
        
        // Apply changes with bounds
        fighter1.rating = Math.max(this.config.ratingFloor, 
                         Math.min(this.config.ratingCeiling, fighter1.rating + change1));
        fighter2.rating = Math.max(this.config.ratingFloor, 
                         Math.min(this.config.ratingCeiling, fighter2.rating + change2));
        
        // Update fight counts (only for the new fight)
        fighter1.fight_count++;
        fighter2.fight_count++;
        fighter1.last_fight_date = fight.date;
        fighter2.last_fight_date = fight.date;
        
        // Update win/loss records
        if (actualOutcome === 1) {
            fighter1.win_count++;
            fighter2.loss_count++;
        } else if (actualOutcome === 0) {
            fighter1.loss_count++;
            fighter2.win_count++;
        }
    }

    /**
     * Calculate K-factor based on experience
     */
    calculateKFactor(fighter, divisionParams) {
        const baseK = divisionParams.k_factor || 32;
        
        if (fighter.fight_count < 5) {
            return baseK * 1.5;  // New fighters
        } else if (fighter.fight_count < 15) {
            return baseK;        // Developing
        } else {
            return baseK * 0.75; // Veterans
        }
    }

    /**
     * Apply local strength of schedule adjustments
     */
    async applyLocalSoSAdjustments(fighterIds, ratings, division) {
        // Simplified SoS calculation for incremental updates
        const sosDampening = 0.85;
        
        for (const fighterId of fighterIds) {
            const fighter = ratings.get(fighterId);
            if (!fighter || fighter.fight_count === 0) continue;
            
            // Get recent opponent ratings (last 2 years)
            const opponentRatings = this.db.prepare(`
                SELECT 
                    CASE 
                        WHEN f.fighter1_id = ? THEN wr2.rating
                        ELSE wr1.rating
                    END as opponent_rating
                FROM fights f
                JOIN events e ON f.event_id = e.id
                LEFT JOIN whr_ratings wr1 ON f.fighter1_id = wr1.fighter_id AND wr1.division = f.weight_class
                LEFT JOIN whr_ratings wr2 ON f.fighter2_id = wr2.fighter_id AND wr2.division = f.weight_class
                WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                    AND f.weight_class = ?
                    AND e.date >= date('now', '-2 years')
                    AND f.result_method NOT LIKE '%No Contest%'
                    AND f.result_method NOT LIKE '%DQ%'
            `).all(fighterId, fighterId, fighterId, division);
            
            if (opponentRatings.length > 0) {
                const validRatings = opponentRatings
                    .filter(r => r.opponent_rating !== null)
                    .map(r => r.opponent_rating);
                
                if (validRatings.length > 0) {
                    const avgOpponentRating = validRatings.reduce((a, b) => a + b, 0) / validRatings.length;
                    const baseRating = 1500; // Default base rating
                    
                    const sosAdjustment = (avgOpponentRating - baseRating) * sosDampening / fighter.fight_count;
                    fighter.rating = Math.max(this.config.ratingFloor,
                                    Math.min(this.config.ratingCeiling, fighter.rating + sosAdjustment));
                }
            }
        }
    }

    /**
     * Save updated ratings to database
     */
    async saveUpdatedRatings(updatedRatings, division) {
        const updateStmt = this.db.prepare(`
            UPDATE whr_ratings
            SET rating = ?, fight_count = ?, win_count = ?, loss_count = ?, last_fight_date = ?
            WHERE fighter_id = ? AND division = ?
        `);
        
        const updates = this.db.transaction(() => {
            for (const rating of updatedRatings) {
                updateStmt.run(
                    rating.rating,
                    rating.fight_count,
                    rating.win_count,
                    rating.loss_count,
                    rating.last_fight_date,
                    rating.fighter_id,
                    division
                );
            }
        });
        
        updates();
    }

    /**
     * Update rankings for the division
     */
    async updateRankings(division) {
        // Get all fighters in division sorted by rating
        const fighters = this.db.prepare(`
            SELECT 
                wr.*,
                f.first_name || ' ' || f.last_name as name
            FROM whr_ratings wr
            JOIN fighters f ON wr.fighter_id = f.id
            WHERE wr.division = ?
            ORDER BY wr.rating DESC
        `).all(division);
        
        // Update rankings
        const rankingStmt = this.db.prepare(`
            UPDATE whr_division_rankings
            SET rank = ?, rating = ?
            WHERE fighter_id = ? AND division = ?
        `);
        
        const rankingUpdates = this.db.transaction(() => {
            fighters.forEach((fighter, index) => {
                rankingStmt.run(
                    index + 1,
                    fighter.rating,
                    fighter.fighter_id,
                    division
                );
            });
        });
        
        rankingUpdates();
    }

    /**
     * Log the incremental update
     */
    async logUpdate(fight, fightersAffected) {
        this.db.prepare(`
            INSERT INTO whr_calculation_log (
                timestamp, algorithm_version, total_fighters, 
                total_fights, convergence_iterations, parameters_used
            ) VALUES (?, ?, ?, ?, ?, ?)
        `).run(
            new Date().toISOString(),
            'incremental_v1.0',
            fightersAffected,
            1, // Single fight update
            this.config.maxIterations,
            JSON.stringify({
                fightId: fight.id,
                division: fight.weight_class,
                maxNetworkDepth: this.config.maxNetworkDepth,
                updateType: 'incremental'
            })
        );
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) this.db.close();
        console.log('🔒 Database connection closed');
    }
}

// Export the class
module.exports = WHRIncrementalUpdater;

// CLI usage
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('Usage: node whr-incremental-update.js <command> <id>');
        console.log('Commands:');
        console.log('  fight <fightId>  - Update ratings for a specific fight');
        console.log('  event <eventId>  - Update ratings for all fights in an event');
        process.exit(1);
    }
    
    const [command, id] = args;
    const updater = new WHRIncrementalUpdater();
    
    async function runUpdate() {
        try {
            if (command === 'fight') {
                await updater.updateForFight(parseInt(id));
            } else if (command === 'event') {
                await updater.updateForEvent(parseInt(id));
            } else {
                console.error('Unknown command:', command);
                process.exit(1);
            }
            
            console.log('\n✅ Incremental update completed successfully!');
        } catch (error) {
            console.error('❌ Update failed:', error);
            process.exit(1);
        } finally {
            updater.close();
        }
    }
    
    runUpdate();
}