const Database = require('better-sqlite3');
const path = require('path');

const db = new Database(path.join(__dirname, '..', 'data', 'ufc_data.db'));

console.log('Checking WHR-related tables...\n');

// Check if whr_fight_results exists and its schema
try {
    const whrFightResultsSchema = db.prepare("PRAGMA table_info(whr_fight_results)").all();
    if (whrFightResultsSchema.length > 0) {
        console.log('WHR_FIGHT_RESULTS table schema:');
        whrFightResultsSchema.forEach(col => {
            console.log(`  ${col.name} (${col.type})`);
        });
        
        // Count records
        const count = db.prepare("SELECT COUNT(*) as count FROM whr_fight_results").get();
        console.log(`\nTotal records in whr_fight_results: ${count.count}`);
    }
} catch (e) {
    console.log('whr_fight_results table does not exist');
}

// Check whr_fight_history
try {
    const whrFightHistorySchema = db.prepare("PRAGMA table_info(whr_fight_history)").all();
    if (whrFightHistorySchema.length > 0) {
        console.log('\n\nWHR_FIGHT_HISTORY table schema:');
        whrFightHistorySchema.forEach(col => {
            console.log(`  ${col.name} (${col.type})`);
        });
        
        // Count records
        const count = db.prepare("SELECT COUNT(*) as count FROM whr_fight_history").get();
        console.log(`\nTotal records in whr_fight_history: ${count.count}`);
    }
} catch (e) {
    console.log('whr_fight_history table does not exist');
}

// Sample some fights to see what's processed
console.log('\n\nSample of recent fights and their WHR status:');
const sampleQuery = `
    SELECT 
        f.id,
        e.event_name,
        e.date,
        f.weight_class,
        f.fight_status,
        f.result_method,
        CASE 
            WHEN wfh.fight_id IS NOT NULL THEN 'Processed'
            ELSE 'Not Processed'
        END as whr_status
    FROM fights f
    JOIN events e ON f.event_id = e.id
    LEFT JOIN whr_fight_history wfh ON f.id = wfh.fight_id
    ORDER BY e.date DESC
    LIMIT 20;
`;

const sampleFights = db.prepare(sampleQuery).all();
sampleFights.forEach(fight => {
    console.log(`${fight.event_name} (${fight.date}) - ${fight.weight_class} - ${fight.result_method} - WHR: ${fight.whr_status}`);
});

db.close();