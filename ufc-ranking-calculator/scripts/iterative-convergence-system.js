const Database = require("better-sqlite3");
const path = require("path");
const TemporalAccuracyFramework = require("./temporal-accuracy-infrastructure");

/**
 * Iterative Convergence System - Roadmap #3
 *
 * Core WHR algorithm implementing:
 * - Strength of Schedule (SoS) calculations
 * - Age curve adjustments
 * - Iterative convergence until stable ratings
 * - Division-specific parameter usage
 * - Temporal accuracy maintenance
 *
 * Implementation Approach:
 * 1. Initial State: Division-specific base ratings, no age/SoS adjustments
 * 2. Iteration Loop:
 *    - Calculate SoS based on current ratings
 *    - Update all fighter ratings based on SoS
 *    - Calculate age curves based on current ratings
 *    - Apply age adjustments to all ratings
 *    - Check convergence (changes < threshold)
 * 3. Dampening: SoS updates (0.85), Age curves (0.7)
 * 4. Typically converges in 3-5 iterations
 */

class IterativeConvergenceSystem {
  constructor(dbPath) {
    this.dbPath = dbPath || path.join(__dirname, "..", "data", "ufc_data.db");
    this.db = new Database(this.dbPath);
    this.temporal = new TemporalAccuracyFramework(this.dbPath);

    // Convergence parameters from roadmap
    this.sosDAMPENING = 0.85;
    this.ageCurveDAMPENING = 0.7;
    this.convergenceThreshold = 1.0; // Rating points
    this.maxIterations = 5; // Per roadmap: typically 3-5 iterations

    // Current system state
    this.currentRatings = new Map(); // fighterID_division -> {rating, deviation, lastUpdated}
    this.divisionParams = new Map();

    console.log("🔄 Iterative Convergence System initialized");
  }

  /**
   * Load division-specific parameters from database
   */
  async loadDivisionParameters() {
    console.log("📊 Loading division parameters...");

    const query = `
            SELECT division, initial_rating, k_factor, rating_scale_divisor
            FROM division_parameters
        `;

    const params = this.db.prepare(query).all();

    for (const param of params) {
      this.divisionParams.set(param.division, {
        initialRating: param.initial_rating,
        kFactor: param.k_factor,
        ratingScale: param.rating_scale_divisor,
      });
    }

    console.log(
      `✅ Loaded parameters for ${this.divisionParams.size} divisions`
    );
  }

  /**
   * Initialize all fighters with division-specific base ratings
   */
  async initializeBaseRatings() {
    console.log("🎯 Initializing base ratings...");

    const divisions = this.temporal.getDivisionDateRanges();
    let totalFighters = 0;

    for (const divisionInfo of divisions) {
      const division = divisionInfo.division;
      const divisionParams = this.divisionParams.get(division);

      if (!divisionParams) {
        console.warn(`⚠️  No parameters for ${division}, skipping`);
        continue;
      }

      // Get all fighters who ever fought in this division
      const fighters = this.temporal.getActiveFightersAtDate(
        division,
        "2030-01-01"
      ); // Future date to get all

      for (const fighterId of fighters) {
        const key = `${fighterId}_${division}`;
        this.currentRatings.set(key, {
          rating: divisionParams.initialRating,
          deviation: 350, // Standard initial deviation
          lastUpdated: null,
        });
      }

      totalFighters += fighters.length;
      console.log(
        `  ${division}: ${fighters.length} fighters initialized at ${divisionParams.initialRating}`
      );
    }

    console.log(
      `✅ Initialized ${totalFighters} fighter-division combinations`
    );
  }

  /**
   * Calculate Recursive Strength of Schedule for all fighters
   * Implements PageRank-style algorithm with age adjustments and temporal accuracy
   */
  async calculateStrengthOfSchedule() {
    console.log("💪 Calculating Recursive Strength of Schedule...");

    // Step 1: Build fight network with temporal accuracy
    const fightNetwork = await this.buildFightNetwork();

    // Step 2: Calculate recursive SoS using PageRank-style algorithm
    const sosValues = await this.calculateRecursiveSoS(fightNetwork);

    // Step 3: Apply SoS-based rating adjustments
    const adjustmentCount = await this.applySoSAdjustments(
      sosValues,
      fightNetwork
    );

    console.log(
      `✅ Applied recursive SoS adjustments to ${adjustmentCount} fighters`
    );
    return adjustmentCount;
  }

  /**
   * Build fight network with temporal accuracy and age adjustments
   */
  async buildFightNetwork() {
    console.log("  🕸️ Building fight network...");

    const fights = this.temporal.getChronologicalFights();
    const network = new Map(); // fighterKey -> { opponents: [], totalStrength: 0, fightCount: 0 }
    const ageCurves = await this.buildAgeCurvesFromData();

    for (const fight of fights) {
      const division = fight.weight_class;
      const fighter1Key = `${fight.fighter1_id}_${division}`;
      const fighter2Key = `${fight.fighter2_id}_${division}`;

      const fighter1Rating = this.currentRatings.get(fighter1Key);
      const fighter2Rating = this.currentRatings.get(fighter2Key);

      if (!fighter1Rating || !fighter2Rating) continue;

      // Get age factors at fight time
      const fighter1AgeFactor = await this.getAgeFactor(
        fight.fighter1_id,
        fight.fighter1_age,
        division,
        ageCurves
      );
      const fighter2AgeFactor = await this.getAgeFactor(
        fight.fighter2_id,
        fight.fighter2_age,
        division,
        ageCurves
      );

      // Calculate age-adjusted opponent strength
      const fighter1AdjustedStrength =
        fighter1Rating.rating * fighter1AgeFactor;
      const fighter2AdjustedStrength =
        fighter2Rating.rating * fighter2AgeFactor;

      // Initialize network nodes if needed
      if (!network.has(fighter1Key)) {
        network.set(fighter1Key, {
          opponents: [],
          totalStrength: 0,
          fightCount: 0,
          wins: 0,
          losses: 0,
          strongOpponentWins: 0,
          weakOpponentLosses: 0,
        });
      }
      if (!network.has(fighter2Key)) {
        network.set(fighter2Key, {
          opponents: [],
          totalStrength: 0,
          fightCount: 0,
          wins: 0,
          losses: 0,
          strongOpponentWins: 0,
          weakOpponentLosses: 0,
        });
      }

      const f1Node = network.get(fighter1Key);
      const f2Node = network.get(fighter2Key);

      // Add opponent relationships with fight outcomes
      const fightResult = {
        opponentKey: fighter2Key,
        opponentStrength: fighter2AdjustedStrength,
        won: fight.winner_id === fight.fighter1_id,
        date: fight.event_date,
        fightId: fight.id,
      };
      f1Node.opponents.push(fightResult);
      f1Node.totalStrength += fighter2AdjustedStrength;
      f1Node.fightCount++;

      const fightResult2 = {
        opponentKey: fighter1Key,
        opponentStrength: fighter1AdjustedStrength,
        won: fight.winner_id === fight.fighter2_id,
        date: fight.event_date,
        fightId: fight.id,
      };
      f2Node.opponents.push(fightResult2);
      f2Node.totalStrength += fighter1AdjustedStrength;
      f2Node.fightCount++;

      // Track wins/losses and quality of opposition
      if (fight.winner_id === fight.fighter1_id) {
        f1Node.wins++;
        f2Node.losses++;
        // Check if this was a strong opponent win for fighter 1
        const divisionParams1 = this.getDivisionParamsFromKey(fighter1Key);
        const baseRating1 = divisionParams1
          ? divisionParams1.initialRating
          : 1500;
        if (fighter2AdjustedStrength > baseRating1 * 1.1) {
          f1Node.strongOpponentWins++;
        }
        // Check if this was a weak opponent loss for fighter 2
        if (fighter1AdjustedStrength < baseRating1 * 0.9) {
          f2Node.weakOpponentLosses++;
        }
      } else if (fight.winner_id === fight.fighter2_id) {
        f2Node.wins++;
        f1Node.losses++;
        // Check if this was a strong opponent win for fighter 2
        const divisionParams2 = this.getDivisionParamsFromKey(fighter2Key);
        const baseRating2 = divisionParams2
          ? divisionParams2.initialRating
          : 1500;
        if (fighter1AdjustedStrength > baseRating2 * 1.1) {
          f2Node.strongOpponentWins++;
        }
        // Check if this was a weak opponent loss for fighter 1
        if (fighter2AdjustedStrength < baseRating2 * 0.9) {
          f1Node.weakOpponentLosses++;
        }
      }
    }

    console.log(`  ✅ Built network with ${network.size} fighters`);
    return network;
  }

  /**
   * Calculate recursive Strength of Schedule using PageRank-style algorithm
   */
  async calculateRecursiveSoS(fightNetwork) {
    console.log("  🔄 Calculating recursive SoS values...");

    const sosValues = new Map(); // fighterKey -> SoS value
    const dampening = 0.85; // PageRank dampening factor
    const maxIterations = 10;
    const convergenceThreshold = 0.01;

    // Initialize SoS values
    for (const [fighterKey, node] of fightNetwork) {
      const avgOpponentStrength =
        node.fightCount > 0 ? node.totalStrength / node.fightCount : 0;
      sosValues.set(fighterKey, avgOpponentStrength);
    }

    // Iterative calculation until convergence
    for (let iteration = 0; iteration < maxIterations; iteration++) {
      const newSosValues = new Map();
      let maxChange = 0;

      for (const [fighterKey, node] of fightNetwork) {
        if (node.fightCount === 0) {
          newSosValues.set(fighterKey, 0);
          continue;
        }

        // Calculate new SoS based on current opponent SoS values
        let totalOpponentSoS = 0;
        for (const opponent of node.opponents) {
          const opponentSoS = sosValues.get(opponent.opponentKey) || 0;
          // Weight by recency and outcome
          const recencyWeight = this.calculateRecencyWeight(opponent.date);
          const outcomeWeight = opponent.won ? 1.2 : 0.8; // Bonus for beating strong opponents
          totalOpponentSoS += opponentSoS * recencyWeight * outcomeWeight;
        }

        const avgOpponentSoS = totalOpponentSoS / node.fightCount;

        // Apply dampening factor (PageRank style)
        const baseStrength = node.totalStrength / node.fightCount;
        const newSoS =
          (1 - dampening) * baseStrength + dampening * avgOpponentSoS;

        newSosValues.set(fighterKey, newSoS);

        // Track convergence
        const oldSoS = sosValues.get(fighterKey) || 0;
        const change = Math.abs(newSoS - oldSoS);
        maxChange = Math.max(maxChange, change);
      }

      sosValues.clear();
      for (const [key, value] of newSosValues) {
        sosValues.set(key, value);
      }

      console.log(
        `    Iteration ${iteration + 1}: max change = ${maxChange.toFixed(3)}`
      );

      if (maxChange < convergenceThreshold) {
        console.log(`    ✅ SoS converged after ${iteration + 1} iterations`);
        break;
      }
    }

    return sosValues;
  }

  /**
   * Apply SoS-based rating adjustments
   */
  async applySoSAdjustments(sosValues, fightNetwork) {
    console.log("  📊 Applying SoS-based rating adjustments...");

    let adjustmentCount = 0;

    for (const [fighterKey, sosValue] of sosValues) {
      const currentRating = this.currentRatings.get(fighterKey);
      const networkNode = fightNetwork.get(fighterKey);

      if (!currentRating || !networkNode || networkNode.fightCount === 0)
        continue;

      const divisionParams = this.getDivisionParamsFromKey(fighterKey);
      const baseRating = divisionParams ? divisionParams.initialRating : 1500;

      // Calculate SoS adjustment based on multiple factors
      const avgOpponentStrength =
        networkNode.totalStrength / networkNode.fightCount;
      const sosBonus = (sosValue - baseRating) * 0.03; // Scale factor for SoS impact

      // Quality bonus: extra credit for beating strong opponents
      const qualityBonus = networkNode.strongOpponentWins * 5; // +5 points per strong opponent beaten

      // Performance penalty: penalty for losing to weak opponents
      const performancePenalty = networkNode.weakOpponentLosses * -3; // -3 points per weak opponent loss

      // Win rate bonus: fighters with better records against strong opposition
      const totalFights = networkNode.wins + networkNode.losses;
      const winRate = totalFights > 0 ? networkNode.wins / totalFights : 0.5;
      const winRateBonus = (winRate - 0.5) * avgOpponentStrength * 0.01;

      // Combine all adjustments
      const totalAdjustment =
        (sosBonus + qualityBonus + performancePenalty + winRateBonus) *
        this.sosDAMPENING;

      currentRating.rating += totalAdjustment;
      adjustmentCount++;

      // Debug logging for significant adjustments
      if (Math.abs(totalAdjustment) > 5) {
        console.log(
          `    ${fighterKey}: SoS=${sosValue.toFixed(
            1
          )}, adj=${totalAdjustment.toFixed(
            2
          )}, quality=${qualityBonus}, perf=${performancePenalty}`
        );
      }
    }

    return adjustmentCount;
  }

  /**
   * Get age factor for a fighter at a specific age and division
   */
  async getAgeFactor(fighterId, age, division, ageCurves) {
    if (!age || age < 18 || age > 50) return 1.0;

    const divisionCurve = ageCurves.get(division);
    if (!divisionCurve) return 1.0;

    return this.calculateAgeAdjustment(age, divisionCurve) / 10 + 1.0; // Convert adjustment to factor
  }

  /**
   * Calculate recency weight for fight outcomes
   */
  calculateRecencyWeight(fightDate) {
    const currentDate = new Date();
    const fightDateObj = new Date(fightDate);
    const daysDiff = (currentDate - fightDateObj) / (1000 * 60 * 60 * 24);
    const yearsDiff = daysDiff / 365.25;

    // Exponential decay: recent fights weighted more heavily
    return Math.exp(-yearsDiff * 0.1); // 10% decay per year
  }

  /**
   * Calculate and apply age curve adjustments
   */
  async calculateAgeCurves() {
    console.log("📈 Calculating age curves...");

    const ageCurves = await this.buildAgeCurvesFromData();
    let adjustmentCount = 0;

    for (const [fighterKey, ratingData] of this.currentRatings) {
      const fighterId = parseInt(fighterKey.split("_")[0]);
      const division = fighterKey.split("_")[1];

      // Get fighter's current age (approximation using latest fight)
      const fighterAge = await this.estimateFighterAge(fighterId, division);

      if (fighterAge && ageCurves.has(division)) {
        const divisionCurve = ageCurves.get(division);
        const ageAdjustment = this.calculateAgeAdjustment(
          fighterAge,
          divisionCurve
        );
        const dampenedAdjustment = ageAdjustment * this.ageCurveDAMPENING;

        ratingData.rating += dampenedAdjustment;
        adjustmentCount++;
      }
    }

    console.log(`✅ Applied age adjustments to ${adjustmentCount} fighters`);
    return adjustmentCount;
  }

  /**
   * Build age curves from empirical data for each division
   */
  async buildAgeCurvesFromData() {
    console.log("  📊 Building age curves from fight data...");

    const ageCurves = new Map();
    const divisions = Array.from(this.divisionParams.keys());

    for (const division of divisions) {
      const curve = await this.calculateDivisionAgeCurve(division);
      ageCurves.set(division, curve);
    }

    return ageCurves;
  }

  /**
   * Calculate age curve for a specific division
   */
  async calculateDivisionAgeCurve(division) {
    const query = `
            SELECT
                f.fighter1_age, f.fighter2_age,
                f.winner_id, f.fighter1_id, f.fighter2_id
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class = ?
                AND f.fighter1_age IS NOT NULL
                AND f.fighter2_age IS NOT NULL
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.fight_status = 'valid'
        `;

    const fights = this.db.prepare(query).all(division);
    const ageGroups = new Map(); // age -> {wins, total}

    for (const fight of fights) {
      // Process both fighters
      this.addAgePerformance(
        ageGroups,
        fight.fighter1_age,
        fight.winner_id === fight.fighter1_id
      );
      this.addAgePerformance(
        ageGroups,
        fight.fighter2_age,
        fight.winner_id === fight.fighter2_id
      );
    }

    // Convert to age curve (age -> performance multiplier)
    const curve = new Map();
    let peakPerformance = 0;

    // Find peak performance age
    for (const [age, data] of ageGroups) {
      if (data.total >= 10) {
        // Minimum sample size
        const winRate = data.wins / data.total;
        if (winRate > peakPerformance) {
          peakPerformance = winRate;
        }
      }
    }

    // Calculate relative performance at each age
    for (const [age, data] of ageGroups) {
      if (data.total >= 5) {
        // Minimum sample for inclusion
        const winRate = data.wins / data.total;
        const relativePerformance = winRate / Math.max(peakPerformance, 0.5); // Avoid division by 0
        curve.set(age, relativePerformance);
      }
    }

    return curve;
  }

  /**
   * Helper: Add age performance data
   */
  addAgePerformance(ageGroups, age, won) {
    const ageRounded = Math.round(age);
    if (!ageGroups.has(ageRounded)) {
      ageGroups.set(ageRounded, { wins: 0, total: 0 });
    }

    const data = ageGroups.get(ageRounded);
    data.total++;
    if (won) data.wins++;
  }

  /**
   * Calculate age adjustment for a fighter
   */
  calculateAgeAdjustment(age, ageCurve) {
    const ageRounded = Math.round(age);

    // Find closest age data points
    let adjustment = 1.0; // Default no adjustment

    if (ageCurve.has(ageRounded)) {
      adjustment = ageCurve.get(ageRounded);
    } else {
      // Interpolate between nearest ages
      const ages = Array.from(ageCurve.keys()).sort((a, b) => a - b);
      const lower = ages.filter((a) => a <= ageRounded).pop();
      const upper = ages.filter((a) => a > ageRounded)[0];

      if (lower && upper) {
        const lowerVal = ageCurve.get(lower);
        const upperVal = ageCurve.get(upper);
        const ratio = (ageRounded - lower) / (upper - lower);
        adjustment = lowerVal + (upperVal - lowerVal) * ratio;
      } else if (lower) {
        adjustment = ageCurve.get(lower);
      } else if (upper) {
        adjustment = ageCurve.get(upper);
      }
    }

    // Convert to rating adjustment (small changes)
    return (adjustment - 1.0) * 10; // Max ±10 points for age
  }

  /**
   * Estimate fighter's current age
   */
  async estimateFighterAge(fighterId, division) {
    const query = `
            SELECT f.fighter1_age, f.fighter2_age, f.fighter1_id
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = ?
                AND (f.fighter1_age IS NOT NULL OR f.fighter2_age IS NOT NULL)
            ORDER BY e.date DESC
            LIMIT 1
        `;

    const fight = this.db.prepare(query).get(fighterId, fighterId, division);

    if (fight) {
      return fight.fighter1_id === fighterId
        ? fight.fighter1_age
        : fight.fighter2_age;
    }

    return null;
  }

  /**
   * Check if ratings have converged
   */
  checkConvergence(previousRatings) {
    if (!previousRatings) return false;

    let maxChange = 0;
    let changedCount = 0;

    for (const [key, currentData] of this.currentRatings) {
      const previousData = previousRatings.get(key);
      if (
        previousData &&
        !isNaN(currentData.rating) &&
        !isNaN(previousData.rating)
      ) {
        const change = Math.abs(currentData.rating - previousData.rating);
        maxChange = Math.max(maxChange, change);
        if (change > 0.1) changedCount++;
      }
    }

    const converged = maxChange < this.convergenceThreshold;
    console.log(
      `  📊 Max change: ${maxChange.toFixed(
        2
      )}, Changed fighters: ${changedCount}`
    );

    return converged;
  }

  /**
   * Run complete iterative convergence
   */
  async runIterativeConvergence() {
    console.log("🔄 Starting iterative convergence...");

    await this.loadDivisionParameters();
    await this.initializeBaseRatings();

    let iteration = 0;
    let converged = false;
    let previousRatings = null;

    while (!converged && iteration < this.maxIterations) {
      iteration++;
      console.log(`\n🔄 Iteration ${iteration}:`);

      // Store previous state for convergence check
      previousRatings = new Map();
      for (const [key, data] of this.currentRatings) {
        previousRatings.set(key, { ...data });
      }

      // Step 1: Calculate and apply SoS adjustments
      await this.calculateStrengthOfSchedule();

      // Step 2: Calculate and apply age curve adjustments
      await this.calculateAgeCurves();

      // Step 3: Check convergence
      converged = this.checkConvergence(previousRatings);

      if (converged) {
        console.log(`✅ Converged after ${iteration} iterations`);
      } else if (iteration === this.maxIterations) {
        console.log(
          `⚠️  Reached maximum iterations (${this.maxIterations}) without full convergence`
        );
      }
    }

    return {
      converged,
      iterations: iteration,
      finalRatings: this.currentRatings,
    };
  }

  /**
   * Helper: Get division parameters from fighter key
   */
  getDivisionParamsFromKey(fighterKey) {
    const division = fighterKey.split("_")[1];
    return this.divisionParams.get(division);
  }

  /**
   * Get current rating for a fighter in a division
   */
  getFighterRating(fighterId, division) {
    const key = `${fighterId}_${division}`;
    return this.currentRatings.get(key);
  }

  /**
   * Display ratings summary
   */
  displayRatingsSummary() {
    console.log("\n📊 Final Ratings Summary");
    console.log("═".repeat(60));

    const divisionSummaries = new Map();

    for (const [key, data] of this.currentRatings) {
      const division = key.split("_")[1];
      if (!divisionSummaries.has(division)) {
        divisionSummaries.set(division, {
          count: 0,
          total: 0,
          min: Infinity,
          max: -Infinity,
        });
      }

      const summary = divisionSummaries.get(division);
      if (!isNaN(data.rating)) {
        summary.count++;
        summary.total += data.rating;
        summary.min = Math.min(summary.min, data.rating);
        summary.max = Math.max(summary.max, data.rating);
      }
    }

    for (const [division, summary] of divisionSummaries) {
      if (summary.count > 0) {
        const avg = summary.total / summary.count;
        console.log(
          `${division}: ${summary.count} fighters, avg: ${avg.toFixed(
            1
          )}, range: ${summary.min.toFixed(1)}-${summary.max.toFixed(1)}`
        );
      } else {
        console.log(`${division}: 0 valid fighters (all NaN ratings)`);
      }
    }
  }

  /**
   * Close connections
   */
  close() {
    if (this.temporal) this.temporal.close();
    if (this.db) this.db.close();
    console.log("🔒 Database connections closed");
  }
}

// Export for use in other modules
module.exports = IterativeConvergenceSystem;

// Test run if called directly
if (require.main === module) {
  console.log("🚀 Testing Iterative Convergence System...");

  const system = new IterativeConvergenceSystem();

  system
    .runIterativeConvergence()
    .then((result) => {
      console.log(`\n✅ Convergence complete:`);
      console.log(`   Converged: ${result.converged}`);
      console.log(`   Iterations: ${result.iterations}`);
      console.log(`   Total ratings: ${result.finalRatings.size}`);

      system.displayRatingsSummary();

      console.log(
        "\n🎯 Iterative Convergence System implementation completed successfully"
      );
      console.log(
        "📊 Ready for Division-Specific Statistical Weights implementation"
      );
    })
    .catch((error) => {
      console.error("❌ Error running convergence:", error);
    })
    .finally(() => {
      system.close();
    });
}
