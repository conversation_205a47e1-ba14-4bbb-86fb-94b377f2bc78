const Database = require('better-sqlite3');
const path = require('path');
const TemporalAccuracyFramework = require('./temporal-accuracy-infrastructure');

/**
 * Iterative Convergence System - Roadmap #3
 * 
 * Core WHR algorithm implementing:
 * - Strength of Schedule (SoS) calculations
 * - Age curve adjustments
 * - Iterative convergence until stable ratings
 * - Division-specific parameter usage
 * - Temporal accuracy maintenance
 * 
 * Implementation Approach:
 * 1. Initial State: Division-specific base ratings, no age/SoS adjustments
 * 2. Iteration Loop:
 *    - Calculate SoS based on current ratings
 *    - Update all fighter ratings based on SoS
 *    - Calculate age curves based on current ratings  
 *    - Apply age adjustments to all ratings
 *    - Check convergence (changes < threshold)
 * 3. Dampening: SoS updates (0.85), Age curves (0.7)
 * 4. Typically converges in 3-5 iterations
 */

class IterativeConvergenceSystem {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        this.temporal = new TemporalAccuracyFramework(this.dbPath);
        
        // Convergence parameters from roadmap
        this.sosDAMPENING = 0.85;
        this.ageCurveDAMPENING = 0.7;
        this.convergenceThreshold = 1.0; // Rating points
        this.maxIterations = 5; // Per roadmap: typically 3-5 iterations
        
        // Current system state
        this.currentRatings = new Map(); // fighterID_division -> {rating, deviation, lastUpdated}
        this.divisionParams = new Map();
        
        console.log('🔄 Iterative Convergence System initialized');
    }

    /**
     * Load division-specific parameters from database
     */
    async loadDivisionParameters() {
        console.log('📊 Loading division parameters...');
        
        const query = `
            SELECT division, initial_rating, k_factor, rating_scale_divisor
            FROM division_parameters
        `;
        
        const params = this.db.prepare(query).all();
        
        for (const param of params) {
            this.divisionParams.set(param.division, {
                initialRating: param.initial_rating,
                kFactor: param.k_factor,
                ratingScale: param.rating_scale_divisor
            });
        }
        
        console.log(`✅ Loaded parameters for ${this.divisionParams.size} divisions`);
    }

    /**
     * Initialize all fighters with division-specific base ratings
     */
    async initializeBaseRatings() {
        console.log('🎯 Initializing base ratings...');
        
        const divisions = this.temporal.getDivisionDateRanges();
        let totalFighters = 0;
        
        for (const divisionInfo of divisions) {
            const division = divisionInfo.division;
            const divisionParams = this.divisionParams.get(division);
            
            if (!divisionParams) {
                console.warn(`⚠️  No parameters for ${division}, skipping`);
                continue;
            }
            
            // Get all fighters who ever fought in this division
            const fighters = this.temporal.getActiveFightersAtDate(division, '2030-01-01'); // Future date to get all
            
            for (const fighterId of fighters) {
                const key = `${fighterId}_${division}`;
                this.currentRatings.set(key, {
                    rating: divisionParams.initialRating,
                    deviation: 350, // Standard initial deviation
                    lastUpdated: null
                });
            }
            
            totalFighters += fighters.length;
            console.log(`  ${division}: ${fighters.length} fighters initialized at ${divisionParams.initialRating}`);
        }
        
        console.log(`✅ Initialized ${totalFighters} fighter-division combinations`);
    }

    /**
     * Calculate Strength of Schedule for all fighters
     */
    async calculateStrengthOfSchedule() {
        console.log('💪 Calculating Strength of Schedule...');
        
        const fights = this.temporal.getChronologicalFights();
        const sosAdjustments = new Map(); // fighterID_division -> adjustment
        
        for (const fight of fights) {
            const division = fight.weight_class;
            const fighter1Key = `${fight.fighter1_id}_${division}`;
            const fighter2Key = `${fight.fighter2_id}_${division}`;
            
            const fighter1Rating = this.currentRatings.get(fighter1Key);
            const fighter2Rating = this.currentRatings.get(fighter2Key);
            
            if (!fighter1Rating || !fighter2Rating) continue;
            
            // Fighter 1's SoS adjustment based on Fighter 2's strength
            if (!sosAdjustments.has(fighter1Key)) {
                sosAdjustments.set(fighter1Key, { totalOpponentStrength: 0, fightCount: 0 });
            }
            const f1Adj = sosAdjustments.get(fighter1Key);
            f1Adj.totalOpponentStrength += fighter2Rating.rating;
            f1Adj.fightCount++;
            
            // Fighter 2's SoS adjustment based on Fighter 1's strength  
            if (!sosAdjustments.has(fighter2Key)) {
                sosAdjustments.set(fighter2Key, { totalOpponentStrength: 0, fightCount: 0 });
            }
            const f2Adj = sosAdjustments.get(fighter2Key);
            f2Adj.totalOpponentStrength += fighter1Rating.rating;
            f2Adj.fightCount++;
        }
        
        // Apply SoS adjustments with dampening
        let adjustmentCount = 0;
        for (const [fighterKey, adjustment] of sosAdjustments) {
            if (adjustment.fightCount === 0) continue;
            
            const avgOpponentStrength = adjustment.totalOpponentStrength / adjustment.fightCount;
            const currentRating = this.currentRatings.get(fighterKey);
            
            if (currentRating) {
                const divisionParams = this.getDivisionParamsFromKey(fighterKey);
                const baseRating = divisionParams ? divisionParams.initialRating : 1500;
                
                // SoS adjustment: positive if faced stronger opponents, negative if weaker
                const sosAdjustment = (avgOpponentStrength - baseRating) * 0.05; // Reduced scale factor
                const dampenedAdjustment = sosAdjustment * this.sosDAMPENING;
                
                currentRating.rating += dampenedAdjustment;
                adjustmentCount++;
            }
        }
        
        console.log(`✅ Applied SoS adjustments to ${adjustmentCount} fighters`);
        return adjustmentCount;
    }

    /**
     * Calculate and apply age curve adjustments
     */
    async calculateAgeCurves() {
        console.log('📈 Calculating age curves...');
        
        const ageCurves = await this.buildAgeCurvesFromData();
        let adjustmentCount = 0;
        
        for (const [fighterKey, ratingData] of this.currentRatings) {
            const fighterId = parseInt(fighterKey.split('_')[0]);
            const division = fighterKey.split('_')[1];
            
            // Get fighter's current age (approximation using latest fight)
            const fighterAge = await this.estimateFighterAge(fighterId, division);
            
            if (fighterAge && ageCurves.has(division)) {
                const divisionCurve = ageCurves.get(division);
                const ageAdjustment = this.calculateAgeAdjustment(fighterAge, divisionCurve);
                const dampenedAdjustment = ageAdjustment * this.ageCurveDAMPENING;
                
                ratingData.rating += dampenedAdjustment;
                adjustmentCount++;
            }
        }
        
        console.log(`✅ Applied age adjustments to ${adjustmentCount} fighters`);
        return adjustmentCount;
    }

    /**
     * Build age curves from empirical data for each division
     */
    async buildAgeCurvesFromData() {
        console.log('  📊 Building age curves from fight data...');
        
        const ageCurves = new Map();
        const divisions = Array.from(this.divisionParams.keys());
        
        for (const division of divisions) {
            const curve = await this.calculateDivisionAgeCurve(division);
            ageCurves.set(division, curve);
        }
        
        return ageCurves;
    }

    /**
     * Calculate age curve for a specific division
     */
    async calculateDivisionAgeCurve(division) {
        const query = `
            SELECT 
                f.fighter1_age, f.fighter2_age,
                f.winner_id, f.fighter1_id, f.fighter2_id
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class = ?
                AND f.fighter1_age IS NOT NULL 
                AND f.fighter2_age IS NOT NULL
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.fight_status = 'valid'
        `;
        
        const fights = this.db.prepare(query).all(division);
        const ageGroups = new Map(); // age -> {wins, total}
        
        for (const fight of fights) {
            // Process both fighters
            this.addAgePerformance(ageGroups, fight.fighter1_age, fight.winner_id === fight.fighter1_id);
            this.addAgePerformance(ageGroups, fight.fighter2_age, fight.winner_id === fight.fighter2_id);
        }
        
        // Convert to age curve (age -> performance multiplier)
        const curve = new Map();
        let peakPerformance = 0;
        
        // Find peak performance age
        for (const [age, data] of ageGroups) {
            if (data.total >= 10) { // Minimum sample size
                const winRate = data.wins / data.total;
                if (winRate > peakPerformance) {
                    peakPerformance = winRate;
                }
            }
        }
        
        // Calculate relative performance at each age
        for (const [age, data] of ageGroups) {
            if (data.total >= 5) { // Minimum sample for inclusion
                const winRate = data.wins / data.total;
                const relativePerformance = winRate / Math.max(peakPerformance, 0.5); // Avoid division by 0
                curve.set(age, relativePerformance);
            }
        }
        
        return curve;
    }

    /**
     * Helper: Add age performance data
     */
    addAgePerformance(ageGroups, age, won) {
        const ageRounded = Math.round(age);
        if (!ageGroups.has(ageRounded)) {
            ageGroups.set(ageRounded, { wins: 0, total: 0 });
        }
        
        const data = ageGroups.get(ageRounded);
        data.total++;
        if (won) data.wins++;
    }

    /**
     * Calculate age adjustment for a fighter
     */
    calculateAgeAdjustment(age, ageCurve) {
        const ageRounded = Math.round(age);
        
        // Find closest age data points
        let adjustment = 1.0; // Default no adjustment
        
        if (ageCurve.has(ageRounded)) {
            adjustment = ageCurve.get(ageRounded);
        } else {
            // Interpolate between nearest ages
            const ages = Array.from(ageCurve.keys()).sort((a, b) => a - b);
            const lower = ages.filter(a => a <= ageRounded).pop();
            const upper = ages.filter(a => a > ageRounded)[0];
            
            if (lower && upper) {
                const lowerVal = ageCurve.get(lower);
                const upperVal = ageCurve.get(upper);
                const ratio = (ageRounded - lower) / (upper - lower);
                adjustment = lowerVal + (upperVal - lowerVal) * ratio;
            } else if (lower) {
                adjustment = ageCurve.get(lower);
            } else if (upper) {
                adjustment = ageCurve.get(upper);
            }
        }
        
        // Convert to rating adjustment (small changes)
        return (adjustment - 1.0) * 10; // Max ±10 points for age
    }

    /**
     * Estimate fighter's current age
     */
    async estimateFighterAge(fighterId, division) {
        const query = `
            SELECT f.fighter1_age, f.fighter2_age, f.fighter1_id
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = ?
                AND (f.fighter1_age IS NOT NULL OR f.fighter2_age IS NOT NULL)
            ORDER BY e.date DESC
            LIMIT 1
        `;
        
        const fight = this.db.prepare(query).get(fighterId, fighterId, division);
        
        if (fight) {
            return fight.fighter1_id === fighterId ? fight.fighter1_age : fight.fighter2_age;
        }
        
        return null;
    }

    /**
     * Check if ratings have converged
     */
    checkConvergence(previousRatings) {
        if (!previousRatings) return false;
        
        let maxChange = 0;
        let changedCount = 0;
        
        for (const [key, currentData] of this.currentRatings) {
            const previousData = previousRatings.get(key);
            if (previousData) {
                const change = Math.abs(currentData.rating - previousData.rating);
                maxChange = Math.max(maxChange, change);
                if (change > 0.1) changedCount++;
            }
        }
        
        const converged = maxChange < this.convergenceThreshold;
        console.log(`  📊 Max change: ${maxChange.toFixed(2)}, Changed fighters: ${changedCount}`);
        
        return converged;
    }

    /**
     * Run complete iterative convergence
     */
    async runIterativeConvergence() {
        console.log('🔄 Starting iterative convergence...');
        
        await this.loadDivisionParameters();
        await this.initializeBaseRatings();
        
        let iteration = 0;
        let converged = false;
        let previousRatings = null;
        
        while (!converged && iteration < this.maxIterations) {
            iteration++;
            console.log(`\n🔄 Iteration ${iteration}:`);
            
            // Store previous state for convergence check
            previousRatings = new Map();
            for (const [key, data] of this.currentRatings) {
                previousRatings.set(key, { ...data });
            }
            
            // Step 1: Calculate and apply SoS adjustments
            await this.calculateStrengthOfSchedule();
            
            // Step 2: Calculate and apply age curve adjustments
            await this.calculateAgeCurves();
            
            // Step 3: Check convergence
            converged = this.checkConvergence(previousRatings);
            
            if (converged) {
                console.log(`✅ Converged after ${iteration} iterations`);
            } else if (iteration === this.maxIterations) {
                console.log(`⚠️  Reached maximum iterations (${this.maxIterations}) without full convergence`);
            }
        }
        
        return {
            converged,
            iterations: iteration,
            finalRatings: this.currentRatings
        };
    }

    /**
     * Helper: Get division parameters from fighter key
     */
    getDivisionParamsFromKey(fighterKey) {
        const division = fighterKey.split('_')[1];
        return this.divisionParams.get(division);
    }

    /**
     * Get current rating for a fighter in a division
     */
    getFighterRating(fighterId, division) {
        const key = `${fighterId}_${division}`;
        return this.currentRatings.get(key);
    }

    /**
     * Display ratings summary
     */
    displayRatingsSummary() {
        console.log('\n📊 Final Ratings Summary');
        console.log('═'.repeat(60));
        
        const divisionSummaries = new Map();
        
        for (const [key, data] of this.currentRatings) {
            const division = key.split('_')[1];
            if (!divisionSummaries.has(division)) {
                divisionSummaries.set(division, { count: 0, total: 0, min: Infinity, max: -Infinity });
            }
            
            const summary = divisionSummaries.get(division);
            summary.count++;
            summary.total += data.rating;
            summary.min = Math.min(summary.min, data.rating);
            summary.max = Math.max(summary.max, data.rating);
        }
        
        for (const [division, summary] of divisionSummaries) {
            const avg = summary.total / summary.count;
            console.log(`${division}: ${summary.count} fighters, avg: ${avg.toFixed(1)}, range: ${summary.min.toFixed(1)}-${summary.max.toFixed(1)}`);
        }
    }

    /**
     * Close connections
     */
    close() {
        if (this.temporal) this.temporal.close();
        if (this.db) this.db.close();
        console.log('🔒 Database connections closed');
    }
}

// Export for use in other modules
module.exports = IterativeConvergenceSystem;

// Test run if called directly
if (require.main === module) {
    console.log('🚀 Testing Iterative Convergence System...');
    
    const system = new IterativeConvergenceSystem();
    
    system.runIterativeConvergence()
        .then(result => {
            console.log(`\n✅ Convergence complete:`);
            console.log(`   Converged: ${result.converged}`);
            console.log(`   Iterations: ${result.iterations}`);
            console.log(`   Total ratings: ${result.finalRatings.size}`);
            
            system.displayRatingsSummary();
            
            console.log('\n🎯 Iterative Convergence System implementation completed successfully');
            console.log('📊 Ready for Division-Specific Statistical Weights implementation');
        })
        .catch(error => {
            console.error('❌ Error running convergence:', error);
        })
        .finally(() => {
            system.close();
        });
}