#!/usr/bin/env node

const Database = require("better-sqlite3");
const path = require("path");

// Database path
const dbPath = path.join(__dirname, "..", "data", "ufc_data.db");

try {
  const db = new Database(dbPath);

  console.log("🥊 UFC LIGHTWEIGHT POWER RANKINGS (WHR System)");
  console.log(
    "═══════════════════════════════════════════════════════════════"
  );
  console.log();

  // Get top 25 lightweight rankings with fighter names
  const query = `
    SELECT
      wr.fighter_id,
      wr.rating,
      wr.rating_deviation,
      wr.fight_count,
      wr.confidence,
      wr.last_fight_date,
      f.first_name,
      f.last_name
    FROM whr_ratings wr
    JOIN fighters f ON wr.fighter_id = f.id
    WHERE wr.division = 'Lightweight'
    ORDER BY wr.rating DESC
    LIMIT 50
  `;

  const lightweights = db.prepare(query).all();

  lightweights.forEach((fighter, index) => {
    const rank = index + 1;
    const name =
      `${fighter.first_name || ""} ${fighter.last_name || ""}`.trim() ||
      `Fighter ${fighter.fighter_id}`;
    const rating = fighter.rating.toFixed(1);
    const deviation = fighter.rating_deviation.toFixed(1);
    const confidence = fighter.confidence;
    const fights = fighter.fight_count;

    // Add confidence indicator
    const confIcon =
      confidence === "established"
        ? "🏆"
        : confidence === "developing"
        ? "📈"
        : "🔄";

    console.log(
      `${rank.toString().padStart(2)}. ${name.padEnd(25)} ${rating.padStart(
        6
      )} ± ${deviation.padStart(5)} (${fights
        .toString()
        .padStart(2)} fights) ${confIcon}`
    );
  });

  console.log();
  console.log("Legend:");
  console.log(
    "🏆 Established (10+ fights) | 📈 Developing (3-9 fights) | 🔄 Provisional (<3 fights)"
  );
  console.log();

  // Find specific fighters of interest
  const islam = lightweights.find(
    (f) => f.first_name === "Islam" && f.last_name === "Makhachev"
  );
  const paddy = lightweights.find(
    (f) => f.first_name === "Paddy" && f.last_name === "Pimblett"
  );

  console.log("Notable Fighters:");
  if (islam) {
    const islamRank = lightweights.indexOf(islam) + 1;
    console.log(
      `🏆 Islam Makhachev: #${islamRank} - ${islam.rating.toFixed(
        1
      )} ± ${islam.rating_deviation.toFixed(1)} (${islam.fight_count} fights)`
    );
  }

  if (paddy) {
    const paddyRank = lightweights.indexOf(paddy) + 1;
    console.log(
      `📈 Paddy Pimblett: #${paddyRank} - ${paddy.rating.toFixed(
        1
      )} ± ${paddy.rating_deviation.toFixed(1)} (${paddy.fight_count} fights)`
    );
  } else {
    // Paddy might not be in top 25, let's find his rank
    const paddyData = db
      .prepare(
        `
      SELECT wr.*, f.first_name, f.last_name
      FROM whr_ratings wr
      JOIN fighters f ON wr.fighter_id = f.id
      WHERE wr.division = 'Lightweight'
      AND f.first_name = 'Paddy'
      AND f.last_name = 'Pimblett'
    `
      )
      .get();

    if (paddyData) {
      const allLightweights = db
        .prepare(
          "SELECT fighter_id FROM whr_ratings WHERE division = 'Lightweight' ORDER BY rating DESC"
        )
        .all();
      const paddyRank =
        allLightweights.findIndex(
          (f) => f.fighter_id === paddyData.fighter_id
        ) + 1;
      console.log(
        `📈 Paddy Pimblett: #${paddyRank} - ${paddyData.rating.toFixed(
          1
        )} ± ${paddyData.rating_deviation.toFixed(1)} (${
          paddyData.fight_count
        } fights) [outside top 25]`
      );
    }
  }

  db.close();
} catch (error) {
  console.error("Error:", error.message);
  process.exit(1);
}
