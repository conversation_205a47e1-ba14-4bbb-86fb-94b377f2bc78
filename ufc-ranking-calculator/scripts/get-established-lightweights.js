#!/usr/bin/env node

const Database = require('better-sqlite3');
const path = require('path');

// Database path
const dbPath = path.join(__dirname, '..', 'data', 'ufc_data.db');

try {
  const db = new Database(dbPath);

  console.log('🥊 UFC LIGHTWEIGHT POWER RANKINGS (Established Fighters Only)');
  console.log('═══════════════════════════════════════════════════════════════');
  console.log();

  // Get established fighters (10+ fights) first
  const establishedQuery = `
    SELECT 
      wr.fighter_id, 
      wr.rating, 
      wr.rating_deviation, 
      wr.fight_count, 
      wr.confidence,
      wr.last_fight_date,
      f.first_name, 
      f.last_name
    FROM whr_ratings wr
    JOIN fighters f ON wr.fighter_id = f.id
    WHERE wr.division = 'Lightweight' AND wr.fight_count >= 10
    ORDER BY wr.rating DESC
    LIMIT 20
  `;

  const established = db.prepare(establishedQuery).all();

  console.log('🏆 ESTABLISHED FIGHTERS (10+ fights):');
  console.log('─────────────────────────────────────────────────────────────');
  
  established.forEach((fighter, index) => {
    const rank = index + 1;
    const name = `${fighter.first_name || ''} ${fighter.last_name || ''}`.trim() || `Fighter ${fighter.fighter_id}`;
    const rating = fighter.rating.toFixed(1);
    const deviation = fighter.rating_deviation.toFixed(1);
    const fights = fighter.fight_count;
    
    console.log(`${rank.toString().padStart(2)}. ${name.padEnd(25)} ${rating.padStart(6)} ± ${deviation.padStart(5)} (${fights.toString().padStart(2)} fights) 🏆`);
  });

  console.log();

  // Get developing fighters (3-9 fights) with high ratings
  const developingQuery = `
    SELECT 
      wr.fighter_id, 
      wr.rating, 
      wr.rating_deviation, 
      wr.fight_count, 
      wr.confidence,
      wr.last_fight_date,
      f.first_name, 
      f.last_name
    FROM whr_ratings wr
    JOIN fighters f ON wr.fighter_id = f.id
    WHERE wr.division = 'Lightweight' AND wr.fight_count >= 3 AND wr.fight_count < 10
    ORDER BY wr.rating DESC
    LIMIT 15
  `;

  const developing = db.prepare(developingQuery).all();

  console.log('📈 TOP DEVELOPING FIGHTERS (3-9 fights):');
  console.log('─────────────────────────────────────────────────────────────');
  
  developing.forEach((fighter, index) => {
    const rank = index + 1;
    const name = `${fighter.first_name || ''} ${fighter.last_name || ''}`.trim() || `Fighter ${fighter.fighter_id}`;
    const rating = fighter.rating.toFixed(1);
    const deviation = fighter.rating_deviation.toFixed(1);
    const fights = fighter.fight_count;
    
    console.log(`${rank.toString().padStart(2)}. ${name.padEnd(25)} ${rating.padStart(6)} ± ${deviation.padStart(5)} (${fights.toString().padStart(2)} fights) 📈`);
  });

  console.log();

  // Find Islam and Paddy specifically
  const islam = db.prepare(`
    SELECT wr.*, f.first_name, f.last_name 
    FROM whr_ratings wr 
    JOIN fighters f ON wr.fighter_id = f.id 
    WHERE wr.division = 'Lightweight' 
    AND f.first_name = 'Islam' 
    AND f.last_name = 'Makhachev'
  `).get();

  const paddy = db.prepare(`
    SELECT wr.*, f.first_name, f.last_name 
    FROM whr_ratings wr 
    JOIN fighters f ON wr.fighter_id = f.id 
    WHERE wr.division = 'Lightweight' 
    AND f.first_name = 'Paddy' 
    AND f.last_name = 'Pimblett'
  `).get();

  console.log('🎯 NOTABLE FIGHTERS:');
  console.log('─────────────────────────────────────────────────────────────');

  if (islam) {
    const allLightweights = db.prepare("SELECT fighter_id FROM whr_ratings WHERE division = 'Lightweight' ORDER BY rating DESC").all();
    const islamRank = allLightweights.findIndex(f => f.fighter_id === islam.fighter_id) + 1;
    console.log(`🏆 Islam Makhachev: #${islamRank} - ${islam.rating.toFixed(1)} ± ${islam.rating_deviation.toFixed(1)} (${islam.fight_count} fights)`);
  }

  if (paddy) {
    const allLightweights = db.prepare("SELECT fighter_id FROM whr_ratings WHERE division = 'Lightweight' ORDER BY rating DESC").all();
    const paddyRank = allLightweights.findIndex(f => f.fighter_id === paddy.fighter_id) + 1;
    console.log(`📈 Paddy Pimblett: #${paddyRank} - ${paddy.rating.toFixed(1)} ± ${paddy.rating_deviation.toFixed(1)} (${paddy.fight_count} fights)`);
  }

  db.close();

} catch (error) {
  console.error('Error:', error.message);
  process.exit(1);
}
