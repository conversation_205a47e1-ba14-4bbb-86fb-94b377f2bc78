const Database = require('better-sqlite3');
const path = require('path');

/**
 * Diagnose Fight Count Bug
 * Check why <PERSON> shows 70 fights when he only has 7 UFC fights
 */

class FightCountDiagnostic {
    constructor() {
        this.dbPath = path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
    }

    async diagnoseFightCountBug() {
        console.log('🔍 DIAGNOSING FIGHT COUNT BUG');
        console.log('═'.repeat(70));

        // Get <PERSON> and <PERSON>'s IDs
        const paddy = this.getFighterByName('<PERSON>');
        const islam = this.getFighterByName('<PERSON>');

        if (!paddy || !islam) {
            console.log('❌ Could not find fighters');
            return;
        }

        console.log(`👤 <PERSON> (ID: ${paddy.id})`);
        console.log(`👤 <PERSON> (ID: ${islam.id})\n`);

        // Check actual fight data in database
        await this.checkActualFightData(paddy.id, islam.id);

        // Check what the unified algorithm would count
        await this.checkUnifiedAlgorithmCounting(paddy.id, islam.id);

        // Check for data duplication issues
        await this.checkDataDuplication(paddy.id, islam.id);

        // Check weight class filtering
        await this.checkWeightClassFiltering(paddy.id, islam.id);
    }

    getFighterByName(name) {
        return this.db.prepare(`
            SELECT id, first_name, last_name, nickname
            FROM fighters 
            WHERE LOWER(first_name || ' ' || last_name) LIKE LOWER(?)
            LIMIT 1
        `).get(`%${name}%`);
    }

    async checkActualFightData(paddyId, islamId) {
        console.log('📊 ACTUAL FIGHT DATA IN DATABASE');
        console.log('─'.repeat(50));

        // Check all fights for Paddy
        const paddyAllFights = this.db.prepare(`
            SELECT 
                f.id,
                f.weight_class,
                f.result_method,
                e.date,
                e.event_name,
                CASE 
                    WHEN f.fighter1_id = ? THEN f2.first_name || ' ' || f2.last_name
                    ELSE f1.first_name || ' ' || f1.last_name
                END as opponent_name
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
            ORDER BY e.date DESC
        `).all(paddyId, paddyId, paddyId);

        // Check all fights for Islam
        const islamAllFights = this.db.prepare(`
            SELECT 
                f.id,
                f.weight_class,
                f.result_method,
                e.date,
                e.event_name,
                CASE 
                    WHEN f.fighter1_id = ? THEN f2.first_name || ' ' || f2.last_name
                    ELSE f1.first_name || ' ' || f1.last_name
                END as opponent_name
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
            ORDER BY e.date DESC
        `).all(islamId, islamId, islamId);

        console.log(`🥊 Paddy's ALL fights in database: ${paddyAllFights.length}`);
        paddyAllFights.forEach((fight, i) => {
            console.log(`  ${i+1}. vs ${fight.opponent_name} (${fight.weight_class}) - ${fight.date}`);
        });

        console.log(`\n🥊 Islam's ALL fights in database: ${islamAllFights.length}`);
        islamAllFights.slice(0, 10).forEach((fight, i) => {
            console.log(`  ${i+1}. vs ${fight.opponent_name} (${fight.weight_class}) - ${fight.date}`);
        });
        if (islamAllFights.length > 10) {
            console.log(`  ... and ${islamAllFights.length - 10} more fights`);
        }

        // Check lightweight only
        const paddyLW = paddyAllFights.filter(f => f.weight_class === 'Lightweight');
        const islamLW = islamAllFights.filter(f => f.weight_class === 'Lightweight');

        console.log(`\n📊 LIGHTWEIGHT ONLY:`);
        console.log(`  Paddy: ${paddyLW.length} Lightweight fights`);
        console.log(`  Islam: ${islamLW.length} Lightweight fights`);
    }

    async checkUnifiedAlgorithmCounting(paddyId, islamId) {
        console.log('\n🔍 UNIFIED ALGORITHM FIGHT QUERY');
        console.log('─'.repeat(50));

        // Replicate the exact query from unified algorithm
        const excludeDivisions = ['Catch Weight'];
        
        const algorithmFights = this.db.prepare(`
            SELECT
                f.id,
                f.event_id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                f.result_round,
                f.result_time,
                f.weight_class,
                e.date as event_date,
                e.event_name
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class NOT IN (${excludeDivisions.map(() => "?").join(",")})
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND (f.fighter1_id = ? OR f.fighter2_id = ? OR f.fighter1_id = ? OR f.fighter2_id = ?)
            ORDER BY e.date ASC, f.id ASC
        `).all(...excludeDivisions, paddyId, paddyId, islamId, islamId);

        const paddyAlgorithmFights = algorithmFights.filter(f => 
            f.fighter1_id === paddyId || f.fighter2_id === paddyId
        );
        const islamAlgorithmFights = algorithmFights.filter(f => 
            f.fighter1_id === islamId || f.fighter2_id === islamId
        );

        console.log(`🤖 Algorithm would process:`);
        console.log(`  Paddy: ${paddyAlgorithmFights.length} fights`);
        console.log(`  Islam: ${islamAlgorithmFights.length} fights`);

        console.log(`\n📋 Paddy's fights that algorithm sees:`);
        paddyAlgorithmFights.forEach((fight, i) => {
            console.log(`  ${i+1}. ${fight.weight_class} - ${fight.event_date} (ID: ${fight.id})`);
        });

        console.log(`\n📋 Islam's fights that algorithm sees (first 10):`);
        islamAlgorithmFights.slice(0, 10).forEach((fight, i) => {
            console.log(`  ${i+1}. ${fight.weight_class} - ${fight.event_date} (ID: ${fight.id})`);
        });
    }

    async checkDataDuplication(paddyId, islamId) {
        console.log('\n🔍 CHECKING FOR DATA DUPLICATION');
        console.log('─'.repeat(50));

        // Check for duplicate fight IDs
        const paddyDuplicates = this.db.prepare(`
            SELECT f.id, COUNT(*) as count
            FROM fights f
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
            GROUP BY f.id
            HAVING COUNT(*) > 1
        `).all(paddyId, paddyId);

        const islamDuplicates = this.db.prepare(`
            SELECT f.id, COUNT(*) as count
            FROM fights f
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
            GROUP BY f.id
            HAVING COUNT(*) > 1
        `).all(islamId, islamId);

        console.log(`🔍 Duplicate fight IDs:`);
        console.log(`  Paddy: ${paddyDuplicates.length} duplicates`);
        console.log(`  Islam: ${islamDuplicates.length} duplicates`);

        if (paddyDuplicates.length > 0) {
            console.log(`  Paddy duplicates:`, paddyDuplicates);
        }
        if (islamDuplicates.length > 0) {
            console.log(`  Islam duplicates:`, islamDuplicates);
        }

        // Check for same opponent multiple times on same date
        const paddySameDate = this.db.prepare(`
            SELECT 
                e.date,
                f.weight_class,
                COUNT(*) as fight_count,
                GROUP_CONCAT(f.id) as fight_ids
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
            GROUP BY e.date, f.weight_class
            HAVING COUNT(*) > 1
        `).all(paddyId, paddyId);

        console.log(`\n📅 Same date multiple fights:`);
        console.log(`  Paddy: ${paddySameDate.length} instances`);
        if (paddySameDate.length > 0) {
            paddySameDate.forEach(dup => {
                console.log(`    ${dup.date} (${dup.weight_class}): ${dup.fight_count} fights (IDs: ${dup.fight_ids})`);
            });
        }
    }

    async checkWeightClassFiltering(paddyId, islamId) {
        console.log('\n⚖️ WEIGHT CLASS BREAKDOWN');
        console.log('─'.repeat(50));

        // Get weight class breakdown for both fighters
        const paddyByWeight = this.db.prepare(`
            SELECT 
                f.weight_class,
                COUNT(*) as fight_count,
                GROUP_CONCAT(e.date || ' vs ' || 
                    CASE 
                        WHEN f.fighter1_id = ? THEN f2.first_name || ' ' || f2.last_name
                        ELSE f1.first_name || ' ' || f1.last_name
                    END
                ) as fights
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
            GROUP BY f.weight_class
            ORDER BY fight_count DESC
        `).all(paddyId, paddyId, paddyId);

        const islamByWeight = this.db.prepare(`
            SELECT 
                f.weight_class,
                COUNT(*) as fight_count
            FROM fights f
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
            GROUP BY f.weight_class
            ORDER BY fight_count DESC
        `).all(islamId, islamId);

        console.log(`🥊 Paddy's fights by weight class:`);
        paddyByWeight.forEach(wc => {
            console.log(`  ${wc.weight_class}: ${wc.fight_count} fights`);
            if (wc.weight_class === 'Lightweight') {
                console.log(`    Fights: ${wc.fights}`);
            }
        });

        console.log(`\n🥊 Islam's fights by weight class:`);
        islamByWeight.forEach(wc => {
            console.log(`  ${wc.weight_class}: ${wc.fight_count} fights`);
        });

        // Check what WHR ratings table shows
        const paddyWHR = this.db.prepare(`
            SELECT division, fight_count, win_count, loss_count
            FROM whr_ratings 
            WHERE fighter_id = ?
        `).all(paddyId);

        const islamWHR = this.db.prepare(`
            SELECT division, fight_count, win_count, loss_count
            FROM whr_ratings 
            WHERE fighter_id = ?
        `).all(islamId);

        console.log(`\n📊 WHR RATINGS TABLE:`);
        console.log(`  Paddy:`, paddyWHR);
        console.log(`  Islam:`, islamWHR);
    }

    close() {
        if (this.db) this.db.close();
    }
}

// Run diagnostic if called directly
if (require.main === module) {
    const diagnostic = new FightCountDiagnostic();
    
    diagnostic.diagnoseFightCountBug()
        .then(() => {
            console.log('\n✅ Diagnostic complete');
            diagnostic.close();
        })
        .catch(error => {
            console.error('\n❌ Diagnostic failed:', error.message);
            console.error(error.stack);
            diagnostic.close();
            process.exit(1);
        });
}

module.exports = FightCountDiagnostic;
