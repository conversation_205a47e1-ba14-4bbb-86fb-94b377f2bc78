const Database = require('better-sqlite3');
const path = require('path');
const WHRMainAlgorithm = require('./whr-main-algorithm');

/**
 * Update WHR After Event Script
 * 
 * Use this script to update WHR ratings after adding new fight results
 * It will only process fights that haven't been calculated yet
 * 
 * Usage: node update-whr-after-event.js [event_date]
 * Example: node update-whr-after-event.js 2025-05-30
 */

class WHREventUpdater {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        this.whr = new WHRMainAlgorithm(this.dbPath);
    }

    /**
     * Get new fights that haven't been processed yet
     */
    getUnprocessedFights(afterDate = null) {
        let query = `
            SELECT 
                f.id,
                e.date as event_date,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                f.result_round,
                f.result_time,
                f.weight_class,
                e.event_name,
                COALESCE(f1.first_name || ' ' || f1.last_name, 'Unknown') as fighter1_name,
                COALESCE(f2.first_name || ' ' || f2.last_name, 'Unknown') as fighter2_name
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.weight_class NOT LIKE '%Catchweight%'
                AND f.fight_status = 'valid'
                AND f.id NOT IN (SELECT fight_id FROM whr_fight_history)
        `;

        const params = [];
        if (afterDate) {
            query += ' AND e.date >= ?';
            params.push(afterDate);
        }

        query += ' ORDER BY e.date ASC, f.id ASC';

        return this.db.prepare(query).all(...params);
    }

    /**
     * Load existing ratings from database
     */
    loadExistingRatings() {
        console.log('📊 Loading existing ratings...');
        
        const ratings = this.db.prepare(`
            SELECT fighter_id, division, rating, rating_deviation, 
                   fight_count, win_count, loss_count, last_fight_date
            FROM whr_ratings
        `).all();

        // Load into WHR algorithm's rating map
        for (const rating of ratings) {
            const key = `${rating.fighter_id}_${rating.division}`;
            this.whr.fighterRatings.set(key, {
                fighterId: rating.fighter_id,
                division: rating.division,
                rating: rating.rating,
                deviation: rating.rating_deviation,
                fightCount: rating.fight_count,
                wins: rating.win_count,
                losses: rating.loss_count,
                lastFightDate: rating.last_fight_date,
                lastUpdated: new Date().toISOString()
            });
        }

        console.log(`  ✅ Loaded ${ratings.length} existing ratings`);
    }

    /**
     * Update ratings with new fights
     */
    async updateWithNewFights(eventDate = null) {
        console.log('🔄 Updating WHR ratings with new fights...');
        
        // Load parameters
        await this.whr.loadAllParameters();
        
        // Load existing ratings
        this.loadExistingRatings();
        
        // Get unprocessed fights
        const newFights = this.getUnprocessedFights(eventDate);
        
        if (newFights.length === 0) {
            console.log('  ℹ️  No new fights to process');
            return;
        }

        console.log(`\n📊 Processing ${newFights.length} new fights...`);
        
        // Group by event for display
        const eventGroups = {};
        for (const fight of newFights) {
            if (!eventGroups[fight.event_name]) {
                eventGroups[fight.event_name] = [];
            }
            eventGroups[fight.event_name].push(fight);
        }

        // Display events being processed
        console.log('\n🎯 Events to process:');
        for (const [eventName, fights] of Object.entries(eventGroups)) {
            console.log(`  - ${eventName}: ${fights.length} fights`);
        }

        // Process each fight
        console.log('\n⚡ Processing fights...');
        for (const fight of newFights) {
            await this.whr.processSingleFight(fight);
            console.log(`  ✅ ${fight.fighter1_name} vs ${fight.fighter2_name} (${fight.weight_class})`);
        }

        // Run convergence on affected divisions
        const affectedDivisions = [...new Set(newFights.map(f => f.weight_class))];
        console.log(`\n🔄 Running convergence for affected divisions: ${affectedDivisions.join(', ')}`);
        await this.runPartialConvergence(affectedDivisions);

        // Save updated ratings
        await this.saveUpdatedRatings();

        // Regenerate rankings
        console.log('\n🏆 Regenerating rankings...');
        await this.whr.generateCurrentRankings();

        console.log(`\n✅ Successfully processed ${newFights.length} new fights!`);
        
        // Show updated top fighters
        await this.displayUpdatedRankings(affectedDivisions);
    }

    /**
     * Run convergence only for affected divisions
     */
    async runPartialConvergence(divisions) {
        // Simplified convergence for affected divisions
        for (const division of divisions) {
            const divisionFighters = Array.from(this.whr.fighterRatings.entries())
                .filter(([key, rating]) => rating.division === division);
            
            // Apply small SoS adjustment
            for (const [key, rating] of divisionFighters) {
                if (rating.fightCount > 0) {
                    // Small adjustment based on average rating in division
                    const avgRating = divisionFighters.reduce((sum, [_, r]) => sum + r.rating, 0) / divisionFighters.length;
                    const adjustment = (avgRating - rating.rating) * 0.01;
                    rating.rating += adjustment;
                }
            }
        }
    }

    /**
     * Save updated ratings to database
     */
    async saveUpdatedRatings() {
        console.log('\n💾 Saving updated ratings...');
        
        // Update existing ratings
        const updateStmt = this.db.prepare(`
            UPDATE whr_ratings 
            SET rating = ?, rating_deviation = ?, fight_count = ?, 
                win_count = ?, loss_count = ?, last_fight_date = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE fighter_id = ? AND division = ?
        `);

        // Insert new ratings
        const insertStmt = this.db.prepare(`
            INSERT INTO whr_ratings (
                fighter_id, division, rating, rating_deviation,
                fight_count, win_count, loss_count, last_fight_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);

        // Save fight history for new fights
        const historyStmt = this.db.prepare(`
            INSERT INTO whr_fight_history (
                fight_id, division, fighter1_id, fighter2_id,
                fighter1_pre_rating, fighter2_pre_rating,
                fighter1_post_rating, fighter2_post_rating,
                rating_change_fighter1, rating_change_fighter2,
                expected_outcome, actual_outcome, surprise_factor,
                k_factor, performance_multiplier
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const updates = this.db.transaction(() => {
            // Update ratings
            for (const rating of this.whr.fighterRatings.values()) {
                const existing = this.db.prepare(
                    'SELECT 1 FROM whr_ratings WHERE fighter_id = ? AND division = ?'
                ).get(rating.fighterId, rating.division);

                if (existing) {
                    updateStmt.run(
                        rating.rating,
                        rating.deviation,
                        rating.fightCount,
                        rating.wins,
                        rating.losses,
                        rating.lastFightDate,
                        rating.fighterId,
                        rating.division
                    );
                } else {
                    insertStmt.run(
                        rating.fighterId,
                        rating.division,
                        rating.rating,
                        rating.deviation,
                        rating.fightCount,
                        rating.wins,
                        rating.losses,
                        rating.lastFightDate
                    );
                }
            }

            // Save fight history
            for (const history of this.whr.fightHistory) {
                // Check if already exists
                const exists = this.db.prepare(
                    'SELECT 1 FROM whr_fight_history WHERE fight_id = ?'
                ).get(history.fightId);

                if (!exists) {
                    const surpriseFactor = Math.abs(history.ratingChanges.actualOutcome - history.expectedOutcome);
                    
                    historyStmt.run(
                        history.fightId,
                        history.division,
                        history.fighter1Id,
                        history.fighter2Id,
                        history.preRatings.fighter1.rating,
                        history.preRatings.fighter2.rating,
                        history.postRatings.fighter1.rating,
                        history.postRatings.fighter2.rating,
                        history.ratingChanges.fighter1Change,
                        history.ratingChanges.fighter2Change,
                        history.expectedOutcome,
                        history.ratingChanges.actualOutcome,
                        surpriseFactor,
                        history.kFactor,
                        history.ratingChanges.performanceMultiplier
                    );
                }
            }
        });

        updates();
        console.log('  ✅ Ratings updated successfully');
    }

    /**
     * Display updated rankings for affected divisions
     */
    async displayUpdatedRankings(divisions) {
        console.log('\n🏆 Updated Rankings');
        console.log('═'.repeat(60));

        for (const division of divisions) {
            console.log(`\n${division} Top 5:`);
            
            const topFighters = this.db.prepare(`
                SELECT 
                    f.first_name || ' ' || f.last_name as name,
                    dr.rank,
                    dr.rating,
                    dr.streak,
                    wr.fight_count,
                    wr.win_count,
                    wr.loss_count
                FROM whr_division_rankings dr
                JOIN fighters f ON dr.fighter_id = f.id
                JOIN whr_ratings wr ON dr.fighter_id = wr.fighter_id AND dr.division = wr.division
                WHERE dr.division = ?
                ORDER BY dr.rank
                LIMIT 5
            `).all(division);

            topFighters.forEach(fighter => {
                const record = `${fighter.win_count}-${fighter.loss_count}`;
                const streakStr = fighter.streak > 0 ? `W${fighter.streak}` : 
                                fighter.streak < 0 ? `L${Math.abs(fighter.streak)}` : '-';
                console.log(`  ${fighter.rank}. ${fighter.name}: ${fighter.rating.toFixed(1)} (${record}, ${streakStr})`);
            });
        }
    }

    /**
     * Close connections
     */
    close() {
        if (this.whr) this.whr.close();
        if (this.db) this.db.close();
    }
}

// Run the updater
if (require.main === module) {
    const eventDate = process.argv[2] || null;
    
    console.log('🔄 WHR Event Updater');
    console.log('═'.repeat(60));
    
    if (eventDate) {
        console.log(`Processing fights from ${eventDate} onwards...`);
    } else {
        console.log('Processing all unprocessed fights...');
    }
    
    const updater = new WHREventUpdater();
    
    updater.updateWithNewFights(eventDate)
        .then(() => {
            console.log('\n✅ WHR update completed successfully!');
        })
        .catch(error => {
            console.error('❌ Error updating WHR:', error);
        })
        .finally(() => {
            updater.close();
        });
}