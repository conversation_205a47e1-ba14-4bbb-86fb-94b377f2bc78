const Database = require('better-sqlite3');
const path = require('path');

/**
 * Analyze Strength of Schedule Issue
 * Compare <PERSON> vs <PERSON> to understand ranking discrepancy
 */

class SoSAnalyzer {
    constructor() {
        this.dbPath = path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
    }

    async analyzeSoSIssue() {
        console.log('🔍 ANALYZING STRENGTH OF SCHEDULE ISSUE');
        console.log('═'.repeat(70));
        console.log('Comparing <PERSON> Pimblett vs Islam Makhachev rankings\n');

        // Get fighter IDs
        const paddy = this.getFighterByName('Paddy Pimblett');
        const islam = this.getFighterByName('Islam Makhachev');

        if (!paddy || !islam) {
            console.log('❌ Could not find both fighters');
            return;
        }

        console.log(`👤 Paddy Pimblett (ID: ${paddy.id})`);
        console.log(`👤 <PERSON> (ID: ${islam.id})\n`);

        // Analyze their current ratings
        await this.compareCurrentRatings(paddy.id, islam.id);

        // Analyze their fight histories
        await this.compareFightHistories(paddy.id, islam.id);

        // Analyze their opponents' strength
        await this.compareOpponentStrength(paddy.id, islam.id);

        // Check for data issues
        await this.checkDataIssues(paddy.id, islam.id);
    }

    getFighterByName(name) {
        return this.db.prepare(`
            SELECT id, first_name, last_name, nickname
            FROM fighters 
            WHERE LOWER(first_name || ' ' || last_name) LIKE LOWER(?)
            LIMIT 1
        `).get(`%${name}%`);
    }

    async compareCurrentRatings(paddyId, islamId) {
        console.log('📊 CURRENT RATINGS COMPARISON');
        console.log('─'.repeat(50));

        const paddyRating = this.db.prepare(`
            SELECT * FROM whr_ratings 
            WHERE fighter_id = ? AND division = 'Lightweight'
        `).get(paddyId);

        const islamRating = this.db.prepare(`
            SELECT * FROM whr_ratings 
            WHERE fighter_id = ? AND division = 'Lightweight'
        `).get(islamId);

        console.log(`Paddy: ${paddyRating.rating.toFixed(1)} ± ${paddyRating.rating_deviation.toFixed(1)}`);
        console.log(`  Record: ${paddyRating.win_count}-${paddyRating.loss_count} (${paddyRating.fight_count} fights)`);
        console.log(`  Last Fight: ${paddyRating.last_fight_date}`);

        console.log(`\nIslam: ${islamRating.rating.toFixed(1)} ± ${islamRating.rating_deviation.toFixed(1)}`);
        console.log(`  Record: ${islamRating.win_count}-${islamRating.loss_count} (${islamRating.fight_count} fights)`);
        console.log(`  Last Fight: ${islamRating.last_fight_date}`);

        const difference = paddyRating.rating - islamRating.rating;
        console.log(`\n🎯 Rating Difference: ${difference.toFixed(1)} points (Paddy higher)`);
    }

    async compareFightHistories(paddyId, islamId) {
        console.log('\n📈 FIGHT HISTORIES COMPARISON');
        console.log('─'.repeat(50));

        // Get Paddy's fights
        const paddyFights = this.db.prepare(`
            SELECT 
                f.id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                e.date,
                e.event_name,
                CASE 
                    WHEN f.fighter1_id = ? THEN f2.first_name || ' ' || f2.last_name
                    ELSE f1.first_name || ' ' || f1.last_name
                END as opponent_name,
                CASE 
                    WHEN f.fighter1_id = ? THEN f2.id
                    ELSE f1.id
                END as opponent_id
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = 'Lightweight'
            ORDER BY e.date DESC
        `).all(paddyId, paddyId, paddyId, paddyId);

        // Get Islam's fights
        const islamFights = this.db.prepare(`
            SELECT 
                f.id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                e.date,
                e.event_name,
                CASE 
                    WHEN f.fighter1_id = ? THEN f2.first_name || ' ' || f2.last_name
                    ELSE f1.first_name || ' ' || f1.last_name
                END as opponent_name,
                CASE 
                    WHEN f.fighter1_id = ? THEN f2.id
                    ELSE f1.id
                END as opponent_id
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = 'Lightweight'
            ORDER BY e.date DESC
        `).all(islamId, islamId, islamId, islamId);

        console.log(`🥊 Paddy's Recent Lightweight Fights (${paddyFights.length} total):`);
        paddyFights.slice(0, 5).forEach(fight => {
            const result = fight.winner_id === paddyId ? 'W' : 'L';
            console.log(`  ${result} vs ${fight.opponent_name} (${fight.date}) - ${fight.result_method}`);
        });

        console.log(`\n🥊 Islam's Recent Lightweight Fights (${islamFights.length} total):`);
        islamFights.slice(0, 5).forEach(fight => {
            const result = fight.winner_id === islamId ? 'W' : 'L';
            console.log(`  ${result} vs ${fight.opponent_name} (${fight.date}) - ${fight.result_method}`);
        });
    }

    async compareOpponentStrength(paddyId, islamId) {
        console.log('\n💪 OPPONENT STRENGTH COMPARISON');
        console.log('─'.repeat(50));

        // Get average opponent ratings for Paddy
        const paddyOpponents = this.db.prepare(`
            SELECT 
                AVG(wr.rating) as avg_opponent_rating,
                COUNT(*) as fight_count,
                GROUP_CONCAT(
                    f1.first_name || ' ' || f1.last_name || ' (' || ROUND(wr.rating, 1) || ')'
                ) as opponents_with_ratings
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON (
                CASE 
                    WHEN f.fighter1_id = ? THEN f.fighter2_id
                    ELSE f.fighter1_id
                END = f1.id
            )
            JOIN whr_ratings wr ON f1.id = wr.fighter_id AND wr.division = 'Lightweight'
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = 'Lightweight'
        `).get(paddyId, paddyId, paddyId);

        // Get average opponent ratings for Islam
        const islamOpponents = this.db.prepare(`
            SELECT 
                AVG(wr.rating) as avg_opponent_rating,
                COUNT(*) as fight_count,
                GROUP_CONCAT(
                    f1.first_name || ' ' || f1.last_name || ' (' || ROUND(wr.rating, 1) || ')'
                ) as opponents_with_ratings
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON (
                CASE 
                    WHEN f.fighter1_id = ? THEN f.fighter2_id
                    ELSE f.fighter1_id
                END = f1.id
            )
            JOIN whr_ratings wr ON f1.id = wr.fighter_id AND wr.division = 'Lightweight'
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = 'Lightweight'
        `).get(islamId, islamId, islamId);

        console.log(`🎯 Paddy's Opponent Strength:`);
        console.log(`  Average Opponent Rating: ${paddyOpponents.avg_opponent_rating?.toFixed(1) || 'N/A'}`);
        console.log(`  Fights with rated opponents: ${paddyOpponents.fight_count}`);
        if (paddyOpponents.opponents_with_ratings) {
            console.log(`  Opponents: ${paddyOpponents.opponents_with_ratings}`);
        }

        console.log(`\n🎯 Islam's Opponent Strength:`);
        console.log(`  Average Opponent Rating: ${islamOpponents.avg_opponent_rating?.toFixed(1) || 'N/A'}`);
        console.log(`  Fights with rated opponents: ${islamOpponents.fight_count}`);
        if (islamOpponents.opponents_with_ratings) {
            console.log(`  Opponents: ${islamOpponents.opponents_with_ratings}`);
        }

        if (paddyOpponents.avg_opponent_rating && islamOpponents.avg_opponent_rating) {
            const sosDifference = islamOpponents.avg_opponent_rating - paddyOpponents.avg_opponent_rating;
            console.log(`\n📊 SoS Difference: ${sosDifference.toFixed(1)} points (Islam's favor)`);
            
            if (sosDifference > 50) {
                console.log('🚨 MAJOR ISSUE: Islam faces much stronger opponents but is ranked lower!');
            }
        }
    }

    async checkDataIssues(paddyId, islamId) {
        console.log('\n🔍 CHECKING FOR DATA ISSUES');
        console.log('─'.repeat(50));

        // Check for unusual fight counts
        const paddyData = this.db.prepare(`
            SELECT 
                COUNT(*) as total_ufc_fights,
                SUM(CASE WHEN winner_id = ? THEN 1 ELSE 0 END) as ufc_wins
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = 'Lightweight'
        `).get(paddyId, paddyId, paddyId);

        const islamData = this.db.prepare(`
            SELECT 
                COUNT(*) as total_ufc_fights,
                SUM(CASE WHEN winner_id = ? THEN 1 ELSE 0 END) as ufc_wins
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = 'Lightweight'
        `).get(islamId, islamId, islamId);

        console.log(`📊 Actual UFC Fight Data:`);
        console.log(`  Paddy: ${paddyData.ufc_wins}/${paddyData.total_ufc_fights} in UFC Lightweight`);
        console.log(`  Islam: ${islamData.ufc_wins}/${islamData.total_ufc_fights} in UFC Lightweight`);

        // Check WHR ratings table for discrepancies
        const paddyWHR = this.db.prepare(`
            SELECT win_count, loss_count, fight_count
            FROM whr_ratings 
            WHERE fighter_id = ? AND division = 'Lightweight'
        `).get(paddyId);

        const islamWHR = this.db.prepare(`
            SELECT win_count, loss_count, fight_count
            FROM whr_ratings 
            WHERE fighter_id = ? AND division = 'Lightweight'
        `).get(islamId);

        console.log(`\n📊 WHR Table Data:`);
        console.log(`  Paddy: ${paddyWHR.win_count}-${paddyWHR.loss_count} (${paddyWHR.fight_count} fights)`);
        console.log(`  Islam: ${islamWHR.win_count}-${islamWHR.loss_count} (${islamWHR.fight_count} fights)`);

        // Check for data corruption
        if (paddyWHR.fight_count !== paddyData.total_ufc_fights) {
            console.log(`🚨 DATA ISSUE: Paddy's fight count mismatch! WHR: ${paddyWHR.fight_count}, Actual: ${paddyData.total_ufc_fights}`);
        }

        if (islamWHR.fight_count !== islamData.total_ufc_fights) {
            console.log(`🚨 DATA ISSUE: Islam's fight count mismatch! WHR: ${islamWHR.fight_count}, Actual: ${islamData.total_ufc_fights}`);
        }

        // Check for rating ceiling issues
        const paddyRating = this.db.prepare(`SELECT rating FROM whr_ratings WHERE fighter_id = ? AND division = 'Lightweight'`).get(paddyId);
        const islamRating = this.db.prepare(`SELECT rating FROM whr_ratings WHERE fighter_id = ? AND division = 'Lightweight'`).get(islamId);

        if (paddyRating.rating >= 1880 && islamRating.rating >= 1870) {
            console.log(`🚨 RATING CEILING ISSUE: Both fighters near ceiling (1885), may be artificially compressed`);
        }

        console.log('\n💡 POTENTIAL CAUSES:');
        console.log('1. Data corruption in fight records');
        console.log('2. Rating ceiling compression (both near 1885 limit)');
        console.log('3. Incorrect opponent strength calculations');
        console.log('4. Temporal accuracy issues (using wrong historical ratings)');
        console.log('5. Performance multiplier bugs inflating Paddy\'s rating');
    }

    close() {
        if (this.db) this.db.close();
    }
}

// Run analysis if called directly
if (require.main === module) {
    const analyzer = new SoSAnalyzer();
    
    analyzer.analyzeSoSIssue()
        .then(() => {
            console.log('\n✅ Analysis complete');
            analyzer.close();
        })
        .catch(error => {
            console.error('\n❌ Analysis failed:', error.message);
            console.error(error.stack);
            analyzer.close();
            process.exit(1);
        });
}

module.exports = SoSAnalyzer;
