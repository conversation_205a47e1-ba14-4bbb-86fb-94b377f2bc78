/**
 * Rating to Probability Examples
 * Shows how rating differences translate to win probabilities
 */

class RatingProbabilityExamples {
    
    /**
     * Calculate win probability from rating difference
     */
    calculateWinProbability(ratingDiff, scaleDivisor) {
        // This is the ELO formula
        return 1 / (1 + Math.pow(10, -ratingDiff / scaleDivisor));
    }
    
    /**
     * Show examples with different scale divisors
     */
    showExamples() {
        console.log('🎯 HOW RATING DIFFERENCES TRANSLATE TO WIN PROBABILITIES');
        console.log('═'.repeat(80));
        
        // Common scale divisors
        const scaleDivisors = [200, 300, 400, 500, 600];
        const ratingDifferences = [0, 10, 25, 38, 50, 100, 150, 200, 300, 400];
        
        for (const scale of scaleDivisors) {
            console.log(`\n📊 With Scale Divisor = ${scale}:`);
            console.log('─'.repeat(60));
            console.log('Rating Diff | Win Probability | Interpretation');
            console.log('─'.repeat(60));
            
            for (const diff of ratingDifferences) {
                const prob = this.calculateWinProbability(diff, scale);
                const percentage = (prob * 100).toFixed(1);
                
                let interpretation = '';
                if (percentage < 45) interpretation = 'Clear underdog';
                else if (percentage < 48) interpretation = 'Slight underdog';
                else if (percentage < 52) interpretation = 'Even match';
                else if (percentage < 55) interpretation = 'Slight favorite';
                else if (percentage < 60) interpretation = 'Moderate favorite';
                else if (percentage < 70) interpretation = 'Clear favorite';
                else interpretation = 'Heavy favorite';
                
                console.log(`${diff.toString().padStart(10)} | ${percentage.padStart(13)}% | ${interpretation}`);
            }
        }
        
        // Show the specific example from the question
        console.log('\n' + '═'.repeat(80));
        console.log('📌 SPECIFIC EXAMPLE: 38 point difference with scale=200');
        console.log('═'.repeat(80));
        
        const diff = 38;
        const scale = 200;
        
        console.log('\nFormula: Probability = 1 / (1 + 10^(-rating_diff / scale))');
        console.log(`\nStep 1: Calculate exponent`);
        console.log(`  -rating_diff / scale = -38 / 200 = -0.19`);
        
        console.log(`\nStep 2: Calculate 10^exponent`);
        console.log(`  10^(-0.19) = ${Math.pow(10, -0.19).toFixed(4)}`);
        
        console.log(`\nStep 3: Add 1`);
        console.log(`  1 + ${Math.pow(10, -0.19).toFixed(4)} = ${(1 + Math.pow(10, -0.19)).toFixed(4)}`);
        
        console.log(`\nStep 4: Take reciprocal`);
        console.log(`  1 / ${(1 + Math.pow(10, -0.19)).toFixed(4)} = ${this.calculateWinProbability(diff, scale).toFixed(4)}`);
        console.log(`  = ${(this.calculateWinProbability(diff, scale) * 100).toFixed(1)}%`);
        
        console.log('\n🔍 WHY THIS MAKES SENSE:');
        console.log('• A 38-point rating advantage is meaningful but not huge');
        console.log('• 60.7% win probability = winning ~3 out of 5 fights');
        console.log('• This allows for upsets while favoring the better fighter');
        console.log('• Scale divisor controls how fast probabilities change');
        console.log('  - Lower scale (200) = ratings matter more');
        console.log('  - Higher scale (600) = more unpredictability');
        
        // Show how scale affects a 100-point difference
        console.log('\n📈 Impact of Scale Divisor on 100-point difference:');
        console.log('─'.repeat(40));
        for (const s of scaleDivisors) {
            const p = this.calculateWinProbability(100, s);
            console.log(`Scale ${s}: ${(p * 100).toFixed(1)}% win probability`);
        }
        
        console.log('\n💡 KEY INSIGHTS:');
        console.log('• ELO/WHR uses logarithmic scaling');
        console.log('• Each "scale divisor" points = 76% win probability');
        console.log('• With scale=200: 200 points = 76% chance');
        console.log('• With scale=400: 400 points = 76% chance');
        console.log('• MMA uses lower scales due to high upset potential');
    }
}

// Run examples
const examples = new RatingProbabilityExamples();
examples.showExamples();