const Database = require('better-sqlite3');
const path = require('path');

const db = new Database(path.join(__dirname, '..', 'data', 'ufc_data.db'));

console.log('Finding unprocessed fights from WHR system...\n');

// Find all fights that were not processed by WHR
const unprocessedQuery = `
    SELECT 
        f.id as fight_id,
        e.event_name,
        e.date as event_date,
        f1.first_name || ' ' || f1.last_name as fighter1_name,
        f2.first_name || ' ' || f2.last_name as fighter2_name,
        f.weight_class,
        f.fight_status,
        f.result_method,
        f.nc_reason
    FROM fights f
    JOIN events e ON f.event_id = e.id
    JOIN fighters f1 ON f.fighter1_id = f1.id
    JOIN fighters f2 ON f.fighter2_id = f2.id
    LEFT JOIN whr_fight_history wfh ON f.id = wfh.fight_id
    WHERE wfh.fight_id IS NULL
    ORDER BY e.date ASC;
`;

const unprocessedFights = db.prepare(unprocessedQuery).all();

console.log(`Total unprocessed fights: ${unprocessedFights.length}\n`);

// Analyze patterns
const patterns = {
    weight_class: {},
    fight_status: {},
    result_method: {},
    has_nc_reason: 0
};

unprocessedFights.forEach(fight => {
    patterns.weight_class[fight.weight_class] = (patterns.weight_class[fight.weight_class] || 0) + 1;
    patterns.fight_status[fight.fight_status] = (patterns.fight_status[fight.fight_status] || 0) + 1;
    patterns.result_method[fight.result_method] = (patterns.result_method[fight.result_method] || 0) + 1;
    if (fight.nc_reason) patterns.has_nc_reason++;
});

console.log('PATTERNS IN UNPROCESSED FIGHTS:');
console.log('\nWeight Classes:');
Object.entries(patterns.weight_class).sort((a,b) => b[1] - a[1]).forEach(([key, count]) => {
    console.log(`  ${key}: ${count} fights`);
});

console.log('\nFight Status:');
Object.entries(patterns.fight_status).sort((a,b) => b[1] - a[1]).forEach(([key, count]) => {
    console.log(`  ${key}: ${count} fights`);
});

console.log('\nResult Method:');
Object.entries(patterns.result_method).sort((a,b) => b[1] - a[1]).forEach(([key, count]) => {
    console.log(`  ${key}: ${count} fights`);
});

console.log(`\nFights with NC reason: ${patterns.has_nc_reason}`);

// Show all 20 unprocessed fights if that's the number
if (unprocessedFights.length === 20) {
    console.log('\n\nALL 20 UNPROCESSED FIGHTS:');
    unprocessedFights.forEach((fight, index) => {
        console.log(`\n${index + 1}. ${fight.event_name} (${fight.event_date})`);
        console.log(`   ${fight.fighter1_name} vs ${fight.fighter2_name}`);
        console.log(`   Weight Class: ${fight.weight_class}`);
        console.log(`   Status: ${fight.fight_status}`);
        console.log(`   Result Method: ${fight.result_method}`);
        if (fight.nc_reason) console.log(`   NC Reason: ${fight.nc_reason}`);
    });
} else {
    // Show first 20
    console.log('\n\nFIRST 20 UNPROCESSED FIGHTS:');
    unprocessedFights.slice(0, 20).forEach((fight, index) => {
        console.log(`\n${index + 1}. ${fight.event_name} (${fight.event_date})`);
        console.log(`   ${fight.fighter1_name} vs ${fight.fighter2_name}`);
        console.log(`   Weight Class: ${fight.weight_class}`);
        console.log(`   Status: ${fight.fight_status}`);
        console.log(`   Result Method: ${fight.result_method}`);
        if (fight.nc_reason) console.log(`   NC Reason: ${fight.nc_reason}`);
    });
}

// Check WHR processing logic
console.log('\n\nCHECKING WHR PROCESSING CRITERIA:');

// Look for specific filtering patterns
const catchWeightCount = unprocessedFights.filter(f => f.weight_class.includes('Catch')).length;
const noContestCount = unprocessedFights.filter(f => f.result_method === 'No Contest').length;
const notCompletedCount = unprocessedFights.filter(f => f.fight_status !== 'Completed').length;

console.log(`\nCatch Weight fights: ${catchWeightCount}`);
console.log(`No Contest fights: ${noContestCount}`);
console.log(`Non-Completed fights: ${notCompletedCount}`);

// Check for other potential issues
const unknownWeightClass = unprocessedFights.filter(f => 
    !['Flyweight', 'Bantamweight', 'Featherweight', 'Lightweight', 'Welterweight', 
     'Middleweight', 'Light Heavyweight', 'Heavyweight', "Women's Strawweight", 
     "Women's Flyweight", "Women's Bantamweight", "Women's Featherweight"].includes(f.weight_class)
);

console.log(`\nNon-standard weight classes: ${unknownWeightClass.length}`);
if (unknownWeightClass.length > 0) {
    console.log('Examples:');
    unknownWeightClass.slice(0, 5).forEach(f => {
        console.log(`  - ${f.weight_class}`);
    });
}

db.close();