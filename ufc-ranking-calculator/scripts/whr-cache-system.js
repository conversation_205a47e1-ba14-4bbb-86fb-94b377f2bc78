const Database = require('better-sqlite3');
const path = require('path');

/**
 * WHR Caching System
 * 
 * Implements caching strategies for improved performance:
 * - Historical calculation caching
 * - Fighter opponent network caching
 * - Temporal snapshot caching
 * - Rating lookup caching
 */

class WHRCacheSystem {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        
        // In-memory caches
        this.caches = {
            fighterNetworks: new Map(),
            temporalSnapshots: new Map(),
            ratingHistory: new Map(),
            divisionParameters: new Map(),
            opponentStrengths: new Map()
        };
        
        // Cache configuration
        this.config = {
            maxCacheSize: 10000,
            cacheExpiryMs: 1000 * 60 * 60, // 1 hour
            cleanupIntervalMs: 1000 * 60 * 10 // 10 minutes
        };
        
        // Start cache cleanup timer
        this.startCacheCleanup();
        
        console.log('💾 WHR Cache System initialized');
    }

    /**
     * Initialize cache tables in database
     */
    async initializeCacheTables() {
        console.log('🔧 Initializing cache tables...');
        
        // Create cache tables
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS whr_cache_fighter_networks (
                fighter_id INTEGER,
                division VARCHAR(50),
                network_data TEXT,
                depth INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                PRIMARY KEY (fighter_id, division, depth)
            )
        `);
        
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS whr_cache_temporal_snapshots (
                cache_key VARCHAR(255) PRIMARY KEY,
                snapshot_data TEXT,
                fight_date DATE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP
            )
        `);
        
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS whr_cache_rating_history (
                fighter_id INTEGER,
                division VARCHAR(50),
                date_range VARCHAR(50),
                rating_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                PRIMARY KEY (fighter_id, division, date_range)
            )
        `);
        
        // Create indices for performance
        this.db.exec(`
            CREATE INDEX IF NOT EXISTS idx_cache_networks_expires 
            ON whr_cache_fighter_networks(expires_at);
            
            CREATE INDEX IF NOT EXISTS idx_cache_snapshots_expires 
            ON whr_cache_temporal_snapshots(expires_at);
            
            CREATE INDEX IF NOT EXISTS idx_cache_history_expires 
            ON whr_cache_rating_history(expires_at);
        `);
        
        console.log('  ✅ Cache tables initialized');
    }

    /**
     * Cache fighter opponent network
     */
    async cacheFighterNetwork(fighterId, division, networkData, depth = 3) {
        const cacheKey = `${fighterId}_${division}_${depth}`;
        const expiresAt = new Date(Date.now() + this.config.cacheExpiryMs);
        
        // Cache in memory
        this.caches.fighterNetworks.set(cacheKey, {
            data: networkData,
            timestamp: Date.now(),
            expiresAt: expiresAt.getTime()
        });
        
        // Cache in database for persistence
        this.db.prepare(`
            INSERT OR REPLACE INTO whr_cache_fighter_networks 
            (fighter_id, division, network_data, depth, expires_at)
            VALUES (?, ?, ?, ?, ?)
        `).run(
            fighterId, 
            division, 
            JSON.stringify(networkData), 
            depth, 
            expiresAt.toISOString()
        );
    }

    /**
     * Get cached fighter network
     */
    async getCachedFighterNetwork(fighterId, division, depth = 3) {
        const cacheKey = `${fighterId}_${division}_${depth}`;
        
        // Check memory cache first
        const memoryCache = this.caches.fighterNetworks.get(cacheKey);
        if (memoryCache && Date.now() < memoryCache.expiresAt) {
            return memoryCache.data;
        }
        
        // Check database cache
        const dbCache = this.db.prepare(`
            SELECT network_data, expires_at
            FROM whr_cache_fighter_networks
            WHERE fighter_id = ? AND division = ? AND depth = ?
                AND expires_at > datetime('now')
        `).get(fighterId, division, depth);
        
        if (dbCache) {
            const networkData = JSON.parse(dbCache.network_data);
            
            // Load back into memory cache
            this.caches.fighterNetworks.set(cacheKey, {
                data: networkData,
                timestamp: Date.now(),
                expiresAt: new Date(dbCache.expires_at).getTime()
            });
            
            return networkData;
        }
        
        return null;
    }

    /**
     * Cache temporal snapshot
     */
    async cacheTemporalSnapshot(cacheKey, snapshotData, fightDate) {
        const expiresAt = new Date(Date.now() + this.config.cacheExpiryMs);
        
        // Cache in memory
        this.caches.temporalSnapshots.set(cacheKey, {
            data: snapshotData,
            timestamp: Date.now(),
            expiresAt: expiresAt.getTime()
        });
        
        // Cache in database
        this.db.prepare(`
            INSERT OR REPLACE INTO whr_cache_temporal_snapshots 
            (cache_key, snapshot_data, fight_date, expires_at)
            VALUES (?, ?, ?, ?)
        `).run(
            cacheKey,
            JSON.stringify(snapshotData),
            fightDate,
            expiresAt.toISOString()
        );
    }

    /**
     * Get cached temporal snapshot
     */
    async getCachedTemporalSnapshot(cacheKey) {
        // Check memory cache first
        const memoryCache = this.caches.temporalSnapshots.get(cacheKey);
        if (memoryCache && Date.now() < memoryCache.expiresAt) {
            return memoryCache.data;
        }
        
        // Check database cache
        const dbCache = this.db.prepare(`
            SELECT snapshot_data, expires_at
            FROM whr_cache_temporal_snapshots
            WHERE cache_key = ? AND expires_at > datetime('now')
        `).get(cacheKey);
        
        if (dbCache) {
            const snapshotData = JSON.parse(dbCache.snapshot_data);
            
            // Load back into memory cache
            this.caches.temporalSnapshots.set(cacheKey, {
                data: snapshotData,
                timestamp: Date.now(),
                expiresAt: new Date(dbCache.expires_at).getTime()
            });
            
            return snapshotData;
        }
        
        return null;
    }

    /**
     * Cache rating history for a fighter
     */
    async cacheRatingHistory(fighterId, division, dateRange, ratingData) {
        const cacheKey = `${fighterId}_${division}_${dateRange}`;
        const expiresAt = new Date(Date.now() + this.config.cacheExpiryMs);
        
        // Cache in memory
        this.caches.ratingHistory.set(cacheKey, {
            data: ratingData,
            timestamp: Date.now(),
            expiresAt: expiresAt.getTime()
        });
        
        // Cache in database
        this.db.prepare(`
            INSERT OR REPLACE INTO whr_cache_rating_history 
            (fighter_id, division, date_range, rating_data, expires_at)
            VALUES (?, ?, ?, ?, ?)
        `).run(
            fighterId,
            division,
            dateRange,
            JSON.stringify(ratingData),
            expiresAt.toISOString()
        );
    }

    /**
     * Get cached rating history
     */
    async getCachedRatingHistory(fighterId, division, dateRange) {
        const cacheKey = `${fighterId}_${division}_${dateRange}`;
        
        // Check memory cache first
        const memoryCache = this.caches.ratingHistory.get(cacheKey);
        if (memoryCache && Date.now() < memoryCache.expiresAt) {
            return memoryCache.data;
        }
        
        // Check database cache
        const dbCache = this.db.prepare(`
            SELECT rating_data, expires_at
            FROM whr_cache_rating_history
            WHERE fighter_id = ? AND division = ? AND date_range = ?
                AND expires_at > datetime('now')
        `).get(fighterId, division, dateRange);
        
        if (dbCache) {
            const ratingData = JSON.parse(dbCache.rating_data);
            
            // Load back into memory cache
            this.caches.ratingHistory.set(cacheKey, {
                data: ratingData,
                timestamp: Date.now(),
                expiresAt: new Date(dbCache.expires_at).getTime()
            });
            
            return ratingData;
        }
        
        return null;
    }

    /**
     * Cache division parameters
     */
    async cacheDivisionParameters(division, parameters) {
        const expiresAt = new Date(Date.now() + this.config.cacheExpiryMs * 24); // 24 hours for stable data
        
        this.caches.divisionParameters.set(division, {
            data: parameters,
            timestamp: Date.now(),
            expiresAt: expiresAt.getTime()
        });
    }

    /**
     * Get cached division parameters
     */
    async getCachedDivisionParameters(division) {
        const cache = this.caches.divisionParameters.get(division);
        if (cache && Date.now() < cache.expiresAt) {
            return cache.data;
        }
        return null;
    }

    /**
     * Cache opponent strength calculations
     */
    async cacheOpponentStrength(fighterId, division, opponentData) {
        const cacheKey = `strength_${fighterId}_${division}`;
        const expiresAt = new Date(Date.now() + this.config.cacheExpiryMs);
        
        this.caches.opponentStrengths.set(cacheKey, {
            data: opponentData,
            timestamp: Date.now(),
            expiresAt: expiresAt.getTime()
        });
    }

    /**
     * Get cached opponent strength
     */
    async getCachedOpponentStrength(fighterId, division) {
        const cacheKey = `strength_${fighterId}_${division}`;
        const cache = this.caches.opponentStrengths.get(cacheKey);
        
        if (cache && Date.now() < cache.expiresAt) {
            return cache.data;
        }
        return null;
    }

    /**
     * Invalidate cache for specific fighter/division
     */
    async invalidateCache(fighterId, division) {
        console.log(`🗑️  Invalidating cache for fighter ${fighterId} in ${division}...`);
        
        // Clear memory caches
        for (const [key, cache] of this.caches.fighterNetworks) {
            if (key.includes(`${fighterId}_${division}`)) {
                this.caches.fighterNetworks.delete(key);
            }
        }
        
        for (const [key, cache] of this.caches.ratingHistory) {
            if (key.includes(`${fighterId}_${division}`)) {
                this.caches.ratingHistory.delete(key);
            }
        }
        
        // Clear database caches
        this.db.prepare(`
            DELETE FROM whr_cache_fighter_networks 
            WHERE fighter_id = ? AND division = ?
        `).run(fighterId, division);
        
        this.db.prepare(`
            DELETE FROM whr_cache_rating_history 
            WHERE fighter_id = ? AND division = ?
        `).run(fighterId, division);
    }

    /**
     * Invalidate all caches (for major updates)
     */
    async invalidateAllCaches() {
        console.log('🗑️  Invalidating all caches...');
        
        // Clear memory caches
        this.caches.fighterNetworks.clear();
        this.caches.temporalSnapshots.clear();
        this.caches.ratingHistory.clear();
        this.caches.divisionParameters.clear();
        this.caches.opponentStrengths.clear();
        
        // Clear database caches
        this.db.exec('DELETE FROM whr_cache_fighter_networks');
        this.db.exec('DELETE FROM whr_cache_temporal_snapshots');
        this.db.exec('DELETE FROM whr_cache_rating_history');
        
        console.log('  ✅ All caches cleared');
    }

    /**
     * Get cache statistics
     */
    getCacheStats() {
        const stats = {
            memoryCaches: {
                fighterNetworks: this.caches.fighterNetworks.size,
                temporalSnapshots: this.caches.temporalSnapshots.size,
                ratingHistory: this.caches.ratingHistory.size,
                divisionParameters: this.caches.divisionParameters.size,
                opponentStrengths: this.caches.opponentStrengths.size
            },
            databaseCaches: {},
            totalMemoryItems: 0
        };
        
        // Calculate total memory items
        for (const size of Object.values(stats.memoryCaches)) {
            stats.totalMemoryItems += size;
        }
        
        // Get database cache sizes
        stats.databaseCaches.fighterNetworks = this.db.prepare(`
            SELECT COUNT(*) as count FROM whr_cache_fighter_networks
            WHERE expires_at > datetime('now')
        `).get().count;
        
        stats.databaseCaches.temporalSnapshots = this.db.prepare(`
            SELECT COUNT(*) as count FROM whr_cache_temporal_snapshots
            WHERE expires_at > datetime('now')
        `).get().count;
        
        stats.databaseCaches.ratingHistory = this.db.prepare(`
            SELECT COUNT(*) as count FROM whr_cache_rating_history
            WHERE expires_at > datetime('now')
        `).get().count;
        
        return stats;
    }

    /**
     * Start automatic cache cleanup
     */
    startCacheCleanup() {
        setInterval(() => {
            this.cleanupExpiredCaches();
        }, this.config.cleanupIntervalMs);
    }

    /**
     * Clean up expired cache entries
     */
    cleanupExpiredCaches() {
        const now = Date.now();
        let cleanedCount = 0;
        
        // Clean memory caches
        for (const [cacheName, cache] of Object.entries(this.caches)) {
            if (cache instanceof Map) {
                for (const [key, entry] of cache) {
                    if (entry.expiresAt && now > entry.expiresAt) {
                        cache.delete(key);
                        cleanedCount++;
                    }
                }
            }
        }
        
        // Clean database caches
        const dbCleanup = this.db.prepare(`
            DELETE FROM whr_cache_fighter_networks 
            WHERE expires_at <= datetime('now')
        `).run();
        cleanedCount += dbCleanup.changes;
        
        const dbCleanup2 = this.db.prepare(`
            DELETE FROM whr_cache_temporal_snapshots 
            WHERE expires_at <= datetime('now')
        `).run();
        cleanedCount += dbCleanup2.changes;
        
        const dbCleanup3 = this.db.prepare(`
            DELETE FROM whr_cache_rating_history 
            WHERE expires_at <= datetime('now')
        `).run();
        cleanedCount += dbCleanup3.changes;
        
        if (cleanedCount > 0) {
            console.log(`🧹 Cleaned up ${cleanedCount} expired cache entries`);
        }
    }

    /**
     * Warm up cache with frequently accessed data
     */
    async warmUpCache() {
        console.log('🔥 Warming up cache...');
        
        // Pre-load division parameters
        const divisions = this.db.prepare(`
            SELECT DISTINCT division FROM division_parameters
        `).all();
        
        for (const div of divisions) {
            const params = this.db.prepare(`
                SELECT * FROM division_parameters WHERE division = ?
            `).get(div.division);
            
            if (params) {
                await this.cacheDivisionParameters(div.division, params);
            }
        }
        
        // Pre-load top fighters' networks
        const topFighters = this.db.prepare(`
            SELECT DISTINCT fighter_id, division 
            FROM whr_division_rankings 
            WHERE rank <= 10
        `).all();
        
        console.log(`  🔥 Pre-loading ${topFighters.length} top fighter networks...`);
        
        // This would normally load actual network data
        // For now, just create placeholder entries
        for (const fighter of topFighters) {
            const networkData = { 
                fighterId: fighter.fighter_id, 
                division: fighter.division,
                preloaded: true 
            };
            await this.cacheFighterNetwork(fighter.fighter_id, fighter.division, networkData);
        }
        
        console.log('  ✅ Cache warm-up complete');
    }

    /**
     * Close database connection and cleanup timers
     */
    close() {
        if (this.db) this.db.close();
        // Clear any remaining timers would go here
        console.log('🔒 Cache system closed');
    }
}

// Export the class
module.exports = WHRCacheSystem;

// Run if called directly
if (require.main === module) {
    console.log('💾 WHR Cache System - Initialization Test');
    console.log('═'.repeat(50));
    
    const cache = new WHRCacheSystem();
    
    async function testCache() {
        try {
            // Initialize cache tables
            await cache.initializeCacheTables();
            
            // Warm up cache
            await cache.warmUpCache();
            
            // Show cache stats
            const stats = cache.getCacheStats();
            console.log('\n📊 Cache Statistics:');
            console.log('  Memory caches:');
            Object.entries(stats.memoryCaches).forEach(([name, size]) => {
                console.log(`    ${name}: ${size} items`);
            });
            console.log(`  Total memory items: ${stats.totalMemoryItems}`);
            console.log('  Database caches:');
            Object.entries(stats.databaseCaches).forEach(([name, size]) => {
                console.log(`    ${name}: ${size} items`);
            });
            
            console.log('\n✅ Cache system test completed successfully!');
            
        } catch (error) {
            console.error('❌ Error:', error);
        } finally {
            cache.close();
        }
    }
    
    testCache();
}