const Database = require('better-sqlite3');
const path = require('path');

/**
 * Fixed WHR Algorithm Implementation
 * 
 * Major fixes:
 * 1. Proper iterative convergence (multiple passes until ratings stabilize)
 * 2. Correct expected outcome calculation
 * 3. Skip Catch Weight division
 * 4. Add rating floors/ceilings to prevent clustering
 * 5. Proper chronological processing
 * 6. Better strength of schedule implementation
 */

class WHRMainAlgorithm {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        
        // Configuration
        this.config = {
            convergenceIterations: 20,  // Number of passes through fight history
            convergenceThreshold: 0.01, // Stop when average rating change < this
            ratingFloor: 800,          // Minimum possible rating
            ratingCeiling: 2500,       // Maximum possible rating
            initialDeviation: 350,     // Starting uncertainty
            minDeviation: 50,          // Minimum uncertainty
            deviationDecay: 0.95,      // How fast deviation decreases
            sosWeight: 0.15,           // Strength of schedule weight (15%)
            excludeDivisions: ['Catch Weight', 'Open Weight'] // Divisions to skip
        };
        
        // State
        this.fighterRatings = new Map();
        this.fightHistory = [];
        this.divisionParameters = new Map();
        
        console.log('🏆 WHR Main Algorithm initialized (Fixed Version)');
    }

    /**
     * Main calculation method with proper convergence
     */
    async calculateCompleteWHRRatings() {
        console.log('🚀 Starting fixed WHR calculation...');
        console.log('═'.repeat(60));
        
        try {
            // Step 1: Load division parameters
            await this.loadDivisionParameters();
            
            // Step 2: Get all fights in STRICT chronological order
            const fights = await this.getChronologicalFights();
            console.log(`\n📊 Processing ${fights.length} fights with convergence...`);
            
            // Step 3: Initialize ratings
            await this.initializeRatings(fights);
            
            // Step 4: Iterative convergence
            let iteration = 0;
            let converged = false;
            
            while (iteration < this.config.convergenceIterations && !converged) {
                const startTime = Date.now();
                const previousRatings = this.captureRatings();
                
                // Reset fight history for this iteration
                this.fightHistory = [];
                
                // Process all fights
                for (const fight of fights) {
                    await this.processFight(fight);
                }
                
                // Apply strength of schedule adjustments
                this.applyStrengthOfSchedule();
                
                // Check convergence
                const avgChange = this.calculateAverageChange(previousRatings);
                const elapsed = ((Date.now() - startTime) / 1000).toFixed(2);
                
                console.log(`  Iteration ${iteration + 1}: Avg change = ${avgChange.toFixed(4)} (${elapsed}s)`);
                
                if (avgChange < this.config.convergenceThreshold) {
                    converged = true;
                    console.log('  ✅ Converged!');
                }
                
                iteration++;
            }
            
            // Step 5: Save results
            await this.saveResults();
            
            // Step 6: Generate rankings
            await this.generateRankings();
            
            console.log('\n✅ WHR calculation complete!');
            return this.fighterRatings;
            
        } catch (error) {
            console.error('❌ Error in WHR calculation:', error);
            throw error;
        }
    }

    /**
     * Load division parameters with data-driven values
     */
    async loadDivisionParameters() {
        console.log('\n📋 Loading division parameters...');
        
        // Default parameters (will be overridden by data-driven values if available)
        const defaultParams = {
            k_factor: 32,
            initial_rating: 1500,
            rating_scale_divisor: 400
        };
        
        // Try to load from database
        const params = this.db.prepare(`
            SELECT division, k_factor, initial_rating, rating_scale_divisor
            FROM division_parameters
            WHERE division NOT IN (${this.config.excludeDivisions.map(d => '?').join(',')})
        `).all(...this.config.excludeDivisions);
        
        if (params.length > 0) {
            for (const p of params) {
                this.divisionParameters.set(p.division, p);
            }
            console.log(`  ✅ Loaded parameters for ${params.length} divisions`);
        } else {
            // Use defaults for all divisions
            const divisions = this.db.prepare(`
                SELECT DISTINCT weight_class FROM fights 
                WHERE weight_class NOT IN (${this.config.excludeDivisions.map(d => '?').join(',')})
            `).all(...this.config.excludeDivisions);
            
            for (const div of divisions) {
                this.divisionParameters.set(div.weight_class, {
                    division: div.weight_class,
                    ...defaultParams
                });
            }
            console.log(`  ⚠️  Using default parameters for ${divisions.length} divisions`);
        }
    }

    /**
     * Get fights in STRICT chronological order
     */
    async getChronologicalFights() {
        const fights = this.db.prepare(`
            SELECT 
                f.id,
                f.event_id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                f.result_round,
                f.result_time,
                f.weight_class,
                e.date as event_date,
                e.event_name
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class NOT IN (${this.config.excludeDivisions.map(d => '?').join(',')})
            ORDER BY e.date ASC, f.id ASC
        `).all(...this.config.excludeDivisions);
        
        console.log(`  ✅ Loaded ${fights.length} fights (excluding ${this.config.excludeDivisions.join(', ')})`);
        return fights;
    }

    /**
     * Initialize all fighter ratings
     */
    async initializeRatings(fights) {
        console.log('\n🎯 Initializing fighter ratings...');
        
        // Get all unique fighter-division combinations
        const fighterDivisions = new Set();
        
        for (const fight of fights) {
            fighterDivisions.add(`${fight.fighter1_id}_${fight.weight_class}`);
            fighterDivisions.add(`${fight.fighter2_id}_${fight.weight_class}`);
        }
        
        // Initialize ratings
        for (const key of fighterDivisions) {
            const [fighterId, division] = key.split('_');
            const params = this.divisionParameters.get(division);
            
            if (params) {
                this.fighterRatings.set(key, {
                    fighterId: parseInt(fighterId),
                    division,
                    rating: params.initial_rating,
                    deviation: this.config.initialDeviation,
                    fightCount: 0,
                    wins: 0,
                    losses: 0,
                    draws: 0,
                    lastFightDate: null
                });
            }
        }
        
        console.log(`  ✅ Initialized ${this.fighterRatings.size} fighter-division ratings`);
    }

    /**
     * Process a single fight
     */
    async processFight(fight) {
        const division = fight.weight_class;
        const params = this.divisionParameters.get(division);
        
        if (!params) return;
        
        const fighter1Key = `${fight.fighter1_id}_${division}`;
        const fighter2Key = `${fight.fighter2_id}_${division}`;
        
        const fighter1 = this.fighterRatings.get(fighter1Key);
        const fighter2 = this.fighterRatings.get(fighter2Key);
        
        if (!fighter1 || !fighter2) return;
        
        // Store pre-fight ratings
        const preRatings = {
            fighter1: { ...fighter1 },
            fighter2: { ...fighter2 }
        };
        
        // Calculate expected outcome (FIXED: proper formula)
        const ratingDiff = fighter1.rating - fighter2.rating;
        const expectedOutcome = 1 / (1 + Math.pow(10, -ratingDiff / params.rating_scale_divisor));
        
        // Determine actual outcome
        let actualOutcome;
        if (fight.result_method && fight.result_method.toLowerCase().includes('draw')) {
            actualOutcome = 0.5;
            fighter1.draws++;
            fighter2.draws++;
        } else if (fight.winner_id === fight.fighter1_id) {
            actualOutcome = 1;
            fighter1.wins++;
            fighter2.losses++;
        } else {
            actualOutcome = 0;
            fighter1.losses++;
            fighter2.wins++;
        }
        
        // Calculate K-factor (can be experience-based if desired)
        const k1 = this.calculateKFactor(fighter1, params);
        const k2 = this.calculateKFactor(fighter2, params);
        
        // Calculate rating changes
        const change1 = k1 * (actualOutcome - expectedOutcome);
        const change2 = k2 * (expectedOutcome - actualOutcome);
        
        // Apply changes with floor/ceiling constraints
        fighter1.rating = Math.max(this.config.ratingFloor, 
                         Math.min(this.config.ratingCeiling, fighter1.rating + change1));
        fighter2.rating = Math.max(this.config.ratingFloor, 
                         Math.min(this.config.ratingCeiling, fighter2.rating + change2));
        
        // Update deviations
        fighter1.deviation = Math.max(this.config.minDeviation, 
                                    fighter1.deviation * this.config.deviationDecay);
        fighter2.deviation = Math.max(this.config.minDeviation, 
                                    fighter2.deviation * this.config.deviationDecay);
        
        // Update metadata
        fighter1.fightCount++;
        fighter2.fightCount++;
        fighter1.lastFightDate = fight.event_date;
        fighter2.lastFightDate = fight.event_date;
        
        // Store fight history
        this.fightHistory.push({
            fightId: fight.id,
            date: fight.event_date,
            division,
            fighter1Id: fight.fighter1_id,
            fighter2Id: fight.fighter2_id,
            winnerId: fight.winner_id,
            preRatings,
            postRatings: {
                fighter1: { ...fighter1 },
                fighter2: { ...fighter2 }
            },
            expectedOutcome,
            actualOutcome,
            surpriseFactor: Math.abs(actualOutcome - expectedOutcome)
        });
    }

    /**
     * Calculate K-factor (optionally based on experience)
     */
    calculateKFactor(fighter, params) {
        // Option 1: Fixed K-factor (current approach)
        return params.k_factor;
        
        // Option 2: Experience-based (uncomment to use)
        /*
        if (fighter.fightCount < 5) return params.k_factor * 1.5;  // New fighters
        if (fighter.fightCount < 15) return params.k_factor;       // Developing
        return params.k_factor * 0.75;                              // Veterans
        */
    }

    /**
     * Apply strength of schedule adjustments
     */
    applyStrengthOfSchedule() {
        for (const [key, fighter] of this.fighterRatings) {
            if (fighter.fightCount === 0) continue;
            
            // Calculate average opponent rating
            const opponentRatings = [];
            for (const fight of this.fightHistory) {
                if (fight.fighter1Id === fighter.fighterId && fight.division === fighter.division) {
                    opponentRatings.push(fight.preRatings.fighter2.rating);
                } else if (fight.fighter2Id === fighter.fighterId && fight.division === fighter.division) {
                    opponentRatings.push(fight.preRatings.fighter1.rating);
                }
            }
            
            if (opponentRatings.length > 0) {
                const avgOpponent = opponentRatings.reduce((a, b) => a + b, 0) / opponentRatings.length;
                const params = this.divisionParameters.get(fighter.division);
                const baseRating = params.initial_rating;
                
                // Apply SoS adjustment
                const sosAdjustment = (avgOpponent - baseRating) * this.config.sosWeight / fighter.fightCount;
                fighter.rating = Math.max(this.config.ratingFloor,
                                Math.min(this.config.ratingCeiling, fighter.rating + sosAdjustment));
            }
        }
    }

    /**
     * Capture current ratings for convergence check
     */
    captureRatings() {
        const snapshot = new Map();
        for (const [key, fighter] of this.fighterRatings) {
            snapshot.set(key, fighter.rating);
        }
        return snapshot;
    }

    /**
     * Calculate average rating change
     */
    calculateAverageChange(previousRatings) {
        let totalChange = 0;
        let count = 0;
        
        for (const [key, fighter] of this.fighterRatings) {
            const prevRating = previousRatings.get(key);
            if (prevRating !== undefined) {
                totalChange += Math.abs(fighter.rating - prevRating);
                count++;
            }
        }
        
        return count > 0 ? totalChange / count : 0;
    }

    /**
     * Save results to database
     */
    async saveResults() {
        console.log('\n💾 Saving results to database...');
        
        // Clear existing data
        this.db.exec('DELETE FROM whr_ratings');
        this.db.exec('DELETE FROM whr_fight_history');
        
        // Save ratings
        const ratingStmt = this.db.prepare(`
            INSERT INTO whr_ratings (
                fighter_id, division, rating, rating_deviation,
                last_fight_date, fight_count, win_count, loss_count
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        const ratingInserts = this.db.transaction(() => {
            for (const fighter of this.fighterRatings.values()) {
                ratingStmt.run(
                    fighter.fighterId,
                    fighter.division,
                    fighter.rating,
                    fighter.deviation,
                    fighter.lastFightDate,
                    fighter.fightCount,
                    fighter.wins,
                    fighter.losses
                );
            }
        });
        ratingInserts();
        
        // Save fight history
        const historyStmt = this.db.prepare(`
            INSERT INTO whr_fight_history (
                fight_id, division, fighter1_id, fighter2_id,
                fighter1_pre_rating, fighter2_pre_rating,
                fighter1_post_rating, fighter2_post_rating,
                rating_change_fighter1, rating_change_fighter2,
                expected_outcome, actual_outcome, surprise_factor,
                k_factor, performance_multiplier
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        const historyInserts = this.db.transaction(() => {
            for (const history of this.fightHistory) {
                const params = this.divisionParameters.get(history.division);
                historyStmt.run(
                    history.fightId,
                    history.division,
                    history.fighter1Id,
                    history.fighter2Id,
                    history.preRatings.fighter1.rating,
                    history.preRatings.fighter2.rating,
                    history.postRatings.fighter1.rating,
                    history.postRatings.fighter2.rating,
                    history.postRatings.fighter1.rating - history.preRatings.fighter1.rating,
                    history.postRatings.fighter2.rating - history.preRatings.fighter2.rating,
                    history.expectedOutcome,
                    history.actualOutcome,
                    history.surpriseFactor,
                    params.k_factor,
                    1.0 // No performance multiplier in this implementation
                );
            }
        });
        historyInserts();
        
        console.log(`  ✅ Saved ${this.fighterRatings.size} ratings`);
        console.log(`  ✅ Saved ${this.fightHistory.length} fight history records`);
    }

    /**
     * Generate division rankings
     */
    async generateRankings() {
        console.log('\n🏆 Generating rankings...');
        
        // Clear existing rankings
        this.db.exec('DELETE FROM whr_division_rankings');
        
        const rankingStmt = this.db.prepare(`
            INSERT INTO whr_division_rankings (
                fighter_id, division, rank, rating, rating_deviation,
                points, streak, dominance_score, activity_score, status
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        // Process each division
        for (const [divisionName, params] of this.divisionParameters) {
            const divisionFighters = Array.from(this.fighterRatings.values())
                .filter(f => f.division === divisionName && f.fightCount > 0)
                .sort((a, b) => b.rating - a.rating);
            
            const inserts = this.db.transaction(() => {
                let rank = 1;
                for (const fighter of divisionFighters) {
                    rankingStmt.run(
                        fighter.fighterId,
                        fighter.division,
                        rank++,
                        fighter.rating,
                        fighter.deviation,
                        fighter.rating - params.initial_rating, // Points relative to initial
                        0, // Streak calculation could be added
                        fighter.rating / fighter.fightCount, // Simple dominance score
                        this.calculateActivityScore(fighter),
                        this.getFighterStatus(fighter)
                    );
                }
            });
            inserts();
            
            console.log(`  📊 ${divisionName}: ${divisionFighters.length} fighters ranked`);
        }
    }

    /**
     * Calculate activity score
     */
    calculateActivityScore(fighter) {
        if (!fighter.lastFightDate) return 0;
        
        const daysSince = (Date.now() - new Date(fighter.lastFightDate).getTime()) / (1000 * 60 * 60 * 24);
        return Math.max(0, 100 - Math.max(0, daysSince - 90) / 3);
    }

    /**
     * Get fighter status
     */
    getFighterStatus(fighter) {
        if (!fighter.lastFightDate) return 'inactive';
        
        const daysSince = (Date.now() - new Date(fighter.lastFightDate).getTime()) / (1000 * 60 * 60 * 24);
        
        if (daysSince < 365) return 'active';
        if (daysSince < 730) return 'inactive';
        return 'retired';
    }

    /**
     * Generate current rankings (alias for generateRankings)
     */
    async generateCurrentRankings() {
        return this.generateRankings();
    }

    /**
     * Display summary statistics (alias for displaySummary)
     */
    displaySummaryStatistics() {
        return this.displaySummary();
    }

    /**
     * Display summary statistics
     */
    displaySummary() {
        console.log('\n📊 Summary Statistics');
        console.log('═'.repeat(60));
        
        // Overall stats
        const allRatings = Array.from(this.fighterRatings.values());
        const avgRating = allRatings.reduce((sum, f) => sum + f.rating, 0) / allRatings.length;
        const minRating = Math.min(...allRatings.map(f => f.rating));
        const maxRating = Math.max(...allRatings.map(f => f.rating));
        
        console.log(`\nOverall:`);
        console.log(`  Total Fighters: ${this.fighterRatings.size}`);
        console.log(`  Avg Rating: ${avgRating.toFixed(1)}`);
        console.log(`  Rating Range: ${minRating.toFixed(1)} - ${maxRating.toFixed(1)}`);
        console.log(`  Rating Spread: ${(maxRating - minRating).toFixed(1)}`);
        
        // Per division stats
        for (const [divisionName] of this.divisionParameters) {
            const divFighters = allRatings.filter(f => f.division === divisionName);
            if (divFighters.length === 0) continue;
            
            const divAvg = divFighters.reduce((sum, f) => sum + f.rating, 0) / divFighters.length;
            const divMin = Math.min(...divFighters.map(f => f.rating));
            const divMax = Math.max(...divFighters.map(f => f.rating));
            
            console.log(`\n${divisionName}:`);
            console.log(`  Fighters: ${divFighters.length}`);
            console.log(`  Avg: ${divAvg.toFixed(1)} | Range: ${divMin.toFixed(1)}-${divMax.toFixed(1)}`);
        }
        
        console.log('\n═'.repeat(60));
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) this.db.close();
        console.log('🔒 Database connection closed');
    }
}

// Export the class
module.exports = WHRMainAlgorithm;

// Run if called directly
if (require.main === module) {
    console.log('🏆 WHR Main Algorithm - Complete System Test');
    console.log('═'.repeat(60));
    
    const whr = new WHRMainAlgorithm();
    
    whr.calculateCompleteWHRRatings()
        .then(async () => {
            // Generate rankings
            await whr.generateCurrentRankings();
            
            // Display summary
            whr.displaySummaryStatistics();
            
            console.log('\n✅ WHR Main Algorithm test completed successfully!');
        })
        .catch(error => {
            console.error('❌ Error:', error);
        })
        .finally(() => {
            whr.close();
        });
}