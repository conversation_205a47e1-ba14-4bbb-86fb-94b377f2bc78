const Database = require("better-sqlite3");
const path = require("path");

/**
 * Detailed Opponent Analysis
 * Compare the actual quality of opponents faced by Paddy vs Islam
 */

class DetailedOpponentAnalysis {
  constructor() {
    this.dbPath = path.join(__dirname, "..", "data", "ufc_data.db");
    this.db = new Database(this.dbPath);
  }

  async analyzeOpponentQuality() {
    console.log("🔍 DETAILED OPPONENT QUALITY ANALYSIS");
    console.log("═".repeat(70));

    const paddy = this.getFighterByName("Paddy Pimblett");
    const islam = this.getFighterByName("Islam Makhachev");

    console.log(`👤 Paddy <PERSON>tt (ID: ${paddy.id})`);
    console.log(`👤 <PERSON> (ID: ${islam.id})\n`);

    // Analyze <PERSON>'s opponents
    console.log("🥊 PADDY'S OPPONENTS ANALYSIS:");
    console.log("─".repeat(50));
    await this.analyzeOpponents(paddy.id, "Paddy");

    console.log("\n🥊 ISLAM'S OPPONENTS ANALYSIS:");
    console.log("─".repeat(50));
    await this.analyzeOpponents(islam.id, "Islam");

    // Compare real-world rankings/status
    console.log("\n🏆 REAL-WORLD OPPONENT COMPARISON:");
    console.log("─".repeat(50));
    await this.compareRealWorldStatus(paddy.id, islam.id);
  }

  getFighterByName(name) {
    return this.db
      .prepare(
        `
            SELECT id, first_name, last_name, nickname
            FROM fighters
            WHERE LOWER(first_name || ' ' || last_name) LIKE LOWER(?)
            LIMIT 1
        `
      )
      .get(`%${name}%`);
  }

  async analyzeOpponents(fighterId, fighterName) {
    const opponents = this.db
      .prepare(
        `
            SELECT
                f1.first_name || ' ' || f1.last_name as opponent_name,
                f1.id as opponent_id,
                e.date as fight_date,
                f.result_method,
                f.winner_id,
                wr.rating as opponent_rating,
                wr.fight_count as opponent_fights,
                wr.win_count as opponent_wins,
                wr.loss_count as opponent_losses
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON (
                CASE
                    WHEN f.fighter1_id = ? THEN f.fighter2_id
                    ELSE f.fighter1_id
                END = f1.id
            )
            LEFT JOIN whr_ratings wr ON f1.id = wr.fighter_id AND wr.division = 'Lightweight'
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = 'Lightweight'
            ORDER BY e.date DESC
        `
      )
      .all(fighterId, fighterId, fighterId);

    let totalRating = 0;
    let ratedOpponents = 0;
    let eliteOpponents = 0;
    let championshipOpponents = 0;

    console.log(`${fighterName}'s opponents (${opponents.length} total):`);

    opponents.forEach((opp, i) => {
      const result = opp.winner_id === fighterId ? "W" : "L";
      const rating = opp.opponent_rating || "Unrated";
      const record = opp.opponent_fights
        ? `${opp.opponent_wins}-${opp.opponent_losses}`
        : "N/A";

      console.log(
        `  ${i + 1}. ${opp.opponent_name} - ${rating} (${record}) - ${
          opp.fight_date
        }`
      );

      if (opp.opponent_rating) {
        totalRating += opp.opponent_rating;
        ratedOpponents++;

        if (opp.opponent_rating >= 1800) eliteOpponents++;
        else if (opp.opponent_rating >= 1700) championshipOpponents++;
      }
    });

    const avgRating =
      ratedOpponents > 0 ? (totalRating / ratedOpponents).toFixed(1) : "N/A";

    console.log(`\n📊 ${fighterName}'s Opponent Summary:`);
    console.log(`  Average Rating: ${avgRating}`);
    console.log(`  Elite Opponents (1800+): ${eliteOpponents}`);
    console.log(`  Championship Level (1700-1799): ${championshipOpponents}`);
    console.log(`  Rated Opponents: ${ratedOpponents}/${opponents.length}`);
  }

  async compareRealWorldStatus(paddyId, islamId) {
    // Get notable opponents for each fighter
    const paddyNotable = ["Michael Chandler", "Tony Ferguson", "Bobby Green"];

    const islamNotable = [
      "Charles Oliveira",
      "Dustin Poirier",
      "Alexander Volkanovski",
      "Dan Hooker",
      "Arman Tsarukyan",
      "Renato Moicano",
    ];

    console.log("🌟 NOTABLE OPPONENTS COMPARISON:");
    console.log("\nPaddy's Notable Opponents:");
    paddyNotable.forEach((name) => {
      console.log(`  • ${name} - Former champion/top contender`);
    });

    console.log("\nIslam's Notable Opponents:");
    islamNotable.forEach((name) => {
      console.log(`  • ${name} - Former champion/elite fighter`);
    });

    console.log("\n🎯 QUALITY ASSESSMENT:");
    console.log("Paddy's best wins:");
    console.log(
      "  • Michael Chandler - Former Bellator champion, title challenger"
    );
    console.log("  • Tony Ferguson - Former interim champion (but declining)");
    console.log("  • Bobby Green - Solid gatekeeper");

    console.log("\nIslam's best wins:");
    console.log(
      "  • Charles Oliveira - Former champion, submission specialist"
    );
    console.log("  • Dustin Poirier - Former interim champion, elite striker");
    console.log("  • Alexander Volkanovski - P4P #1, featherweight GOAT");
    console.log("  • Arman Tsarukyan - Rising elite contender");
    console.log("  • Dan Hooker - Former top 5 contender");

    console.log("\n💡 CONCLUSION:");
    console.log("Islam has faced significantly stronger opposition:");
    console.log("  • Multiple former champions");
    console.log("  • Current P4P #1 (Volkanovski)");
    console.log("  • Elite contenders across multiple fights");
    console.log("  • Longer track record of elite competition");

    console.log("\nPaddy has faced good but not elite opposition:");
    console.log("  • One elite opponent (Chandler)");
    console.log("  • One declining legend (Ferguson)");
    console.log("  • Mostly solid but not elite fighters");
    console.log("  • Shorter track record");
  }

  close() {
    if (this.db) this.db.close();
  }
}

// Run analysis if called directly
if (require.main === module) {
  const analyzer = new DetailedOpponentAnalysis();

  analyzer
    .analyzeOpponentQuality()
    .then(() => {
      console.log("\n✅ Analysis complete");
      analyzer.close();
    })
    .catch((error) => {
      console.error("\n❌ Analysis failed:", error.message);
      console.error(error.stack);
      analyzer.close();
      process.exit(1);
    });
}

module.exports = DetailedOpponentAnalysis;
