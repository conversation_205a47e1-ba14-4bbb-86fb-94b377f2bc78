const Database = require('better-sqlite3');
const path = require('path');

/**
 * Division Rankings Display Script
 * Shows current WHR power rankings for any division
 */

class DivisionRankings {
    constructor() {
        this.dbPath = path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
    }

    showDivisionRankings(division = 'Lightweight', limit = 25) {
        console.log(`🥊 ${division.toUpperCase()} POWER RANKINGS`);
        console.log('═'.repeat(80));
        console.log('Based on WHR Enhanced Strength of Schedule System');
        console.log('Philosophy-compliant: Pure merit-based evaluation\n');

        // Get rankings for the division
        const rankings = this.db.prepare(`
            SELECT 
                dr.rank,
                dr.rating,
                dr.rating_deviation,
                dr.points,
                dr.status,
                f.first_name,
                f.last_name,
                f.nickname,
                wr.win_count,
                wr.loss_count,
                wr.fight_count,
                wr.last_fight_date
            FROM whr_division_rankings dr
            JOIN fighters f ON dr.fighter_id = f.id
            JOIN whr_ratings wr ON dr.fighter_id = wr.fighter_id AND dr.division = wr.division
            WHERE dr.division = ?
            ORDER BY dr.rank
            LIMIT ?
        `).all(division, limit);

        if (rankings.length === 0) {
            console.log(`❌ No rankings found for ${division} division`);
            return;
        }

        console.log('Rank | Fighter                    | Rating    | Record    | Last Fight | Status');
        console.log('─'.repeat(80));

        rankings.forEach(fighter => {
            const rank = `#${fighter.rank}`.padEnd(4);
            
            // Format fighter name
            const nickname = fighter.nickname ? ` "${fighter.nickname}"` : '';
            const fullName = `${fighter.first_name} ${fighter.last_name}${nickname}`;
            const name = fullName.length > 25 ? fullName.substring(0, 22) + '...' : fullName.padEnd(25);
            
            // Format rating with deviation
            const rating = `${fighter.rating.toFixed(1)} ±${fighter.rating_deviation.toFixed(0)}`.padEnd(9);
            
            // Format record
            const record = `${fighter.win_count}-${fighter.loss_count}`.padEnd(9);
            
            // Format last fight date
            const lastFight = fighter.last_fight_date ? 
                fighter.last_fight_date.substring(0, 10).padEnd(11) : 
                'N/A'.padEnd(11);
            
            // Format status
            const status = fighter.status || 'Active';
            
            console.log(`${rank} | ${name} | ${rating} | ${record} | ${lastFight} | ${status}`);
        });

        // Division statistics
        const stats = this.db.prepare(`
            SELECT 
                COUNT(*) as total_fighters,
                AVG(rating) as avg_rating,
                MAX(rating) as max_rating,
                MIN(rating) as min_rating,
                AVG(rating_deviation) as avg_deviation
            FROM whr_division_rankings dr
            WHERE dr.division = ?
        `).get(division);

        console.log('\n📊 DIVISION STATISTICS:');
        console.log('─'.repeat(40));
        console.log(`Total Fighters: ${stats.total_fighters}`);
        console.log(`Average Rating: ${stats.avg_rating.toFixed(1)}`);
        console.log(`Rating Range: ${stats.min_rating.toFixed(1)} - ${stats.max_rating.toFixed(1)}`);
        console.log(`Average Deviation: ±${stats.avg_deviation.toFixed(1)}`);

        // Show rating tiers
        console.log('\n🏆 RATING TIERS:');
        console.log('─'.repeat(40));
        
        const tiers = [
            { name: 'Elite (1800+)', min: 1800, max: 2000 },
            { name: 'Championship (1700-1799)', min: 1700, max: 1799 },
            { name: 'Top Contender (1600-1699)', min: 1600, max: 1699 },
            { name: 'High Level (1550-1599)', min: 1550, max: 1599 },
            { name: 'Solid UFC (1500-1549)', min: 1500, max: 1549 },
            { name: 'Developing (1450-1499)', min: 1450, max: 1499 },
            { name: 'Entry Level (<1450)', min: 0, max: 1449 }
        ];

        tiers.forEach(tier => {
            const count = this.db.prepare(`
                SELECT COUNT(*) as count
                FROM whr_division_rankings dr
                WHERE dr.division = ? AND dr.rating >= ? AND dr.rating <= ?
            `).get(division, tier.min, tier.max).count;
            
            if (count > 0) {
                console.log(`${tier.name}: ${count} fighters`);
            }
        });

        // Show recent activity
        console.log('\n📅 RECENT ACTIVITY:');
        console.log('─'.repeat(40));
        
        const recentFights = this.db.prepare(`
            SELECT COUNT(*) as count
            FROM whr_division_rankings dr
            WHERE dr.division = ? AND dr.rating > 0
        `).get(division).count;

        const activeFighters = this.db.prepare(`
            SELECT COUNT(*) as count
            FROM whr_division_rankings dr
            JOIN whr_ratings wr ON dr.fighter_id = wr.fighter_id AND dr.division = wr.division
            WHERE dr.division = ? 
                AND wr.last_fight_date >= date('now', '-2 years')
        `).get(division).count;

        console.log(`Active Fighters (last 2 years): ${activeFighters}/${recentFights}`);
        
        const activityRate = ((activeFighters / recentFights) * 100).toFixed(1);
        console.log(`Activity Rate: ${activityRate}%`);
    }

    showAllDivisions() {
        console.log('🏆 ALL DIVISION RANKINGS SUMMARY');
        console.log('═'.repeat(60));
        
        const divisions = this.db.prepare(`
            SELECT DISTINCT division 
            FROM whr_division_rankings 
            WHERE division != 'Catch Weight'
            ORDER BY division
        `).all();

        console.log('Division              | Fighters | Avg Rating | Top Rating');
        console.log('─'.repeat(60));

        divisions.forEach(div => {
            const stats = this.db.prepare(`
                SELECT 
                    COUNT(*) as total,
                    AVG(rating) as avg_rating,
                    MAX(rating) as max_rating
                FROM whr_division_rankings
                WHERE division = ?
            `).get(div.division);

            const divName = div.division.padEnd(20);
            const fighters = stats.total.toString().padEnd(8);
            const avgRating = stats.avg_rating.toFixed(1).padEnd(10);
            const maxRating = stats.max_rating.toFixed(1);

            console.log(`${divName} | ${fighters} | ${avgRating} | ${maxRating}`);
        });
    }

    close() {
        if (this.db) this.db.close();
    }
}

// Run rankings display if called directly
if (require.main === module) {
    const rankings = new DivisionRankings();
    
    // Get division from command line argument or default to Lightweight
    const division = process.argv[2] || 'Lightweight';
    const limit = parseInt(process.argv[3]) || 25;
    
    try {
        if (division.toLowerCase() === 'all') {
            rankings.showAllDivisions();
        } else {
            rankings.showDivisionRankings(division, limit);
        }
    } catch (error) {
        console.error('❌ Error:', error.message);
    } finally {
        rankings.close();
    }
}

module.exports = DivisionRankings;
