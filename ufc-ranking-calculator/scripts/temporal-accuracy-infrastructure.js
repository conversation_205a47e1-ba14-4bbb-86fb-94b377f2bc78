const Database = require('better-sqlite3');
const path = require('path');

/**
 * Temporal Accuracy Infrastructure - Priority #1
 * 
 * Core Principle: All calculations use only data available at the time of each fight
 * - No future data leakage
 * - Historical accuracy maintained
 * - Point-in-time data snapshots
 * - Real-time update capability
 */

class TemporalAccuracyFramework {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        this.snapshots = new Map(); // Cache for temporal data snapshots
        this.chronologicalFights = null;
        
        console.log('🕐 Temporal Accuracy Framework initialized');
    }

    /**
     * Get all fights in strict chronological order for temporal processing
     * Foundation for maintaining temporal integrity
     */
    getChronologicalFights() {
        if (this.chronologicalFights) return this.chronologicalFights;

        console.log('📅 Loading fights in chronological order...');
        
        const query = `
            SELECT 
                f.id,
                e.date as event_date,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                f.result_round,
                f.result_time,
                f.weight_class,
                COALESCE(f1.first_name || ' ' || f1.last_name, 'Unknown') as fighter1_name,
                COALESCE(f2.first_name || ' ' || f2.last_name, 'Unknown') as fighter2_name
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE e.date IS NOT NULL
                AND e.date != ''
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.weight_class NOT LIKE '%Catchweight%'
                AND f.fight_status = 'valid'
            ORDER BY e.date ASC, f.id ASC
        `;

        this.chronologicalFights = this.db.prepare(query).all();
        console.log(`✅ Loaded ${this.chronologicalFights.length} fights in chronological order`);
        return this.chronologicalFights;
    }

    /**
     * Get fighter's complete state as of a specific date
     * Critical for temporal accuracy - no future data included
     */
    getFighterStateAtDate(fighterId, division, asOfDate) {
        const cacheKey = `${fighterId}_${division}_${asOfDate}`;
        
        if (this.snapshots.has(cacheKey)) {
            return this.snapshots.get(cacheKey);
        }

        // Get all fights for this fighter up to and including the specified date
        const fightsQuery = `
            SELECT 
                f.id,
                e.date as event_date,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                f.result_round,
                f.weight_class,
                CASE 
                    WHEN f.fighter1_id = ? THEN f.fighter2_id 
                    ELSE f.fighter1_id 
                END as opponent_id
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = ?
                AND e.date <= ?
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.weight_class NOT LIKE '%Catchweight%'
                AND f.fight_status = 'valid'
            ORDER BY e.date ASC
        `;

        const fights = this.db.prepare(fightsQuery).all(fighterId, fighterId, fighterId, division, asOfDate);

        // Calculate record as of this date (temporal snapshot)
        let wins = 0, losses = 0, draws = 0;
        const opponents = [];
        
        for (const fight of fights) {
            if (fight.result_method && fight.result_method.toLowerCase().includes('draw')) {
                draws++;
            } else if (fight.winner_id === fighterId) {
                wins++;
            } else {
                losses++;
            }
            
            opponents.push({
                id: fight.opponent_id,
                date: fight.event_date,
                result: fight.winner_id === fighterId ? 'W' : 
                       (fight.result_method && fight.result_method.toLowerCase().includes('draw')) ? 'D' : 'L',
                fightId: fight.id
            });
        }

        const state = {
            fighterId,
            division,
            asOfDate,
            totalFights: fights.length,
            wins,
            losses,
            draws,
            opponents,
            lastFightDate: fights.length > 0 ? fights[fights.length - 1].event_date : null
        };

        // Cache the result for performance
        this.snapshots.set(cacheKey, state);
        return state;
    }

    /**
     * Get all fighters active in a division as of a specific date
     * Used for division-wide calculations at any point in time
     */
    getActiveFightersAtDate(division, asOfDate) {
        const query = `
            SELECT DISTINCT f.fighter1_id as fighter_id
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class = ?
                AND e.date <= ?
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.weight_class NOT LIKE '%Catchweight%'
                AND f.fight_status = 'valid'
            UNION
            SELECT DISTINCT f.fighter2_id as fighter_id
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class = ?
                AND e.date <= ?
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.weight_class NOT LIKE '%Catchweight%'
                AND f.fight_status = 'valid'
        `;

        const fighters = this.db.prepare(query).all(division, asOfDate, division, asOfDate);
        return fighters.map(row => row.fighter_id).filter(id => id); // Remove nulls
    }

    /**
     * Create temporal data snapshot for a specific fight
     * Everything needed to calculate ratings as they were at fight time
     */
    createFightSnapshot(fight) {
        const fighter1State = this.getFighterStateAtDate(
            fight.fighter1_id, 
            fight.weight_class, 
            fight.event_date
        );
        
        const fighter2State = this.getFighterStateAtDate(
            fight.fighter2_id, 
            fight.weight_class, 
            fight.event_date
        );

        return {
            fightId: fight.id,
            date: fight.event_date,
            division: fight.weight_class,
            fighter1: {
                id: fight.fighter1_id,
                name: fight.fighter1_name,
                state: fighter1State
            },
            fighter2: {
                id: fight.fighter2_id,
                name: fight.fighter2_name,
                state: fighter2State
            },
            result: {
                winnerId: fight.winner_id,
                method: fight.result_method,
                round: fight.result_round,
                time: fight.result_time
            }
        };
    }

    /**
     * Process all fights maintaining strict temporal order
     * Core function for historical rating calculations
     */
    processAllFightsTemporally(progressCallback = null) {
        console.log('🕐 Starting temporal processing of all fights...');
        
        const fights = this.getChronologicalFights();
        const snapshots = [];
        let processedCount = 0;

        for (const fight of fights) {
            try {
                const snapshot = this.createFightSnapshot(fight);
                snapshots.push(snapshot);
                processedCount++;

                if (progressCallback && processedCount % 500 === 0) {
                    progressCallback(processedCount, fights.length);
                }

            } catch (error) {
                console.error(`❌ Error processing fight ${fight.id}:`, error);
            }
        }

        console.log(`✅ Temporal processing complete: ${snapshots.length} fight snapshots created`);
        return snapshots;
    }

    /**
     * Validate temporal integrity - critical safety check
     * Ensures no future data leakage in any calculations
     */
    validateTemporalIntegrity(snapshots) {
        console.log('🔍 Validating temporal integrity...');
        
        let violations = 0;
        
        for (const snapshot of snapshots) {
            // Check fighter 1 temporal integrity
            for (const opponent of snapshot.fighter1.state.opponents) {
                if (opponent.date > snapshot.date) {
                    console.error(`❌ TEMPORAL VIOLATION: Fighter ${snapshot.fighter1.id} has future fight data (${opponent.date} > ${snapshot.date})`);
                    violations++;
                }
            }
            
            // Check fighter 2 temporal integrity
            for (const opponent of snapshot.fighter2.state.opponents) {
                if (opponent.date > snapshot.date) {
                    console.error(`❌ TEMPORAL VIOLATION: Fighter ${snapshot.fighter2.id} has future fight data (${opponent.date} > ${snapshot.date})`);
                    violations++;
                }
            }
        }

        if (violations === 0) {
            console.log('✅ Temporal integrity validated - no future data leakage detected');
            return true;
        } else {
            console.error(`❌ CRITICAL: Found ${violations} temporal integrity violations`);
            return false;
        }
    }

    /**
     * Get unique divisions with their date ranges
     * Used for division-specific processing
     */
    getDivisionDateRanges() {
        const query = `
            SELECT 
                f.weight_class as division,
                MIN(e.date) as earliest_date,
                MAX(e.date) as latest_date,
                COUNT(f.id) as total_fights
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.weight_class NOT LIKE '%Catchweight%'
                AND f.fight_status = 'valid'
            GROUP BY f.weight_class
            ORDER BY total_fights DESC
        `;

        return this.db.prepare(query).all();
    }

    /**
     * Clean cache for memory management
     */
    clearCache() {
        this.snapshots.clear();
        console.log('🧹 Temporal data cache cleared');
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) {
            this.db.close();
            console.log('🔒 Database connection closed');
        }
    }
}

// Export for use in other modules
module.exports = TemporalAccuracyFramework;

// Test run if called directly
if (require.main === module) {
    console.log('🚀 Testing Temporal Accuracy Infrastructure...');
    
    const framework = new TemporalAccuracyFramework();
    
    try {
        // Test 1: Load chronological fights
        const fights = framework.getChronologicalFights();
        console.log(`📈 Test 1 PASSED: Loaded ${fights.length} fights chronologically`);
        
        // Test 2: Check division date ranges
        const divisions = framework.getDivisionDateRanges();
        console.log('📊 Test 2 PASSED: Division date ranges:');
        divisions.forEach(div => {
            console.log(`  ${div.division}: ${div.earliest_date} to ${div.latest_date} (${div.total_fights} fights)`);
        });
        
        // Test 3: Test temporal state retrieval
        if (fights.length > 0) {
            const testFight = fights[Math.floor(fights.length / 2)];
            const state = framework.getFighterStateAtDate(
                testFight.fighter1_id,
                testFight.weight_class,
                testFight.event_date
            );
            
            console.log(`🔍 Test 3 PASSED: Fighter state for ${testFight.fighter1_name}:`);
            console.log(`   Record as of ${testFight.event_date}: ${state.wins}-${state.losses}-${state.draws}`);
            console.log(`   Total fights in ${testFight.weight_class}: ${state.totalFights}`);
        }
        
        // Test 4: Test fight snapshot creation
        if (fights.length > 0) {
            const testFight = fights[100]; // Use fight #100
            const snapshot = framework.createFightSnapshot(testFight);
            console.log(`📸 Test 4 PASSED: Fight snapshot created for ${snapshot.fighter1.name} vs ${snapshot.fighter2.name}`);
        }
        
        console.log('✅ Temporal Accuracy Infrastructure tests completed successfully');
        console.log('🎯 Ready for Division-Specific Base Parameters implementation');
        
    } catch (error) {
        console.error('❌ Error testing temporal infrastructure:', error);
    } finally {
        framework.close();
    }
}