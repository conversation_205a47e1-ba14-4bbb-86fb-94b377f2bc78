const Database = require('better-sqlite3');
const path = require('path');

/**
 * Comprehensive WHR System Audit
 * 
 * Performs deep analysis to identify potential issues with:
 * - Logic inconsistencies
 * - Data integrity problems
 * - Metric calculation errors
 * - Performance bottlenecks
 * - Edge cases and anomalies
 */

class ComprehensiveSystemAudit {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        this.issues = [];
        this.warnings = [];
        this.recommendations = [];
    }

    /**
     * Run complete system audit
     */
    async runCompleteAudit() {
        console.log('🔍 Comprehensive WHR System Audit');
        console.log('═'.repeat(60));

        try {
            // 1. Data Integrity Checks
            await this.auditDataIntegrity();
            
            // 2. Logic Consistency Checks
            await this.auditLogicConsistency();
            
            // 3. Rating Distribution Analysis
            await this.auditRatingDistributions();
            
            // 4. Temporal Accuracy Verification
            await this.auditTemporalAccuracy();
            
            // 5. Parameter Validation
            await this.auditParameterValidation();
            
            // 6. Edge Case Detection
            await this.auditEdgeCases();
            
            // 7. Performance Analysis
            await this.auditPerformance();
            
            // 8. Prediction Accuracy Deep Dive
            await this.auditPredictionAccuracy();

            // Generate comprehensive report
            this.generateAuditReport();
            
        } catch (error) {
            console.error('❌ Audit failed:', error);
            throw error;
        }
    }

    /**
     * 1. Data Integrity Audit
     */
    async auditDataIntegrity() {
        console.log('\n🔍 1. Data Integrity Audit');
        console.log('─'.repeat(40));

        // Check for orphaned records
        const orphanedRatings = this.db.prepare(`
            SELECT COUNT(*) as count
            FROM whr_ratings wr
            WHERE NOT EXISTS (SELECT 1 FROM fighters f WHERE f.id = wr.fighter_id)
        `).get().count;

        if (orphanedRatings > 0) {
            this.issues.push(`Found ${orphanedRatings} orphaned ratings without corresponding fighters`);
        }

        // Check for missing division parameters
        const divisionsWithoutParams = this.db.prepare(`
            SELECT DISTINCT wr.division
            FROM whr_ratings wr
            WHERE NOT EXISTS (SELECT 1 FROM division_parameters dp WHERE dp.division = wr.division)
        `).all();

        if (divisionsWithoutParams.length > 0) {
            this.issues.push(`Divisions without parameters: ${divisionsWithoutParams.map(d => d.division).join(', ')}`);
        }

        // Check for duplicate rankings
        const duplicateRankings = this.db.prepare(`
            SELECT division, rank, COUNT(*) as count
            FROM whr_division_rankings
            WHERE ranking_date = (SELECT MAX(ranking_date) FROM whr_division_rankings)
            GROUP BY division, rank
            HAVING count > 1
        `).all();

        if (duplicateRankings.length > 0) {
            this.issues.push(`Found ${duplicateRankings.length} duplicate rankings`);
        }

        // Check for rating outliers
        const ratingOutliers = this.db.prepare(`
            SELECT fighter_id, division, rating
            FROM whr_ratings
            WHERE rating < 1000 OR rating > 2000
        `).all();

        if (ratingOutliers.length > 0) {
            this.warnings.push(`Found ${ratingOutliers.length} fighters with extreme ratings (< 1000 or > 2000)`);
        }

        // Check for negative deviations
        const negativeDeviations = this.db.prepare(`
            SELECT COUNT(*) as count
            FROM whr_ratings
            WHERE rating_deviation < 0
        `).get().count;

        if (negativeDeviations > 0) {
            this.issues.push(`Found ${negativeDeviations} fighters with negative rating deviations`);
        }

        console.log(`  ✅ Checked orphaned records, missing parameters, duplicates, outliers`);
        console.log(`  Issues: ${this.issues.length}, Warnings: ${this.warnings.length}`);
    }

    /**
     * 2. Logic Consistency Audit
     */
    async auditLogicConsistency() {
        console.log('\n🔍 2. Logic Consistency Audit');
        console.log('─'.repeat(40));

        // Check if win/loss counts match fight history
        const recordMismatches = this.db.prepare(`
            WITH calculated_records AS (
                SELECT 
                    wr.fighter_id,
                    wr.division,
                    wr.win_count as stored_wins,
                    wr.loss_count as stored_losses,
                    COUNT(CASE WHEN fh.actual_outcome = 1 THEN 1 END) as calculated_wins,
                    COUNT(CASE WHEN fh.actual_outcome = 0 THEN 1 END) as calculated_losses
                FROM whr_ratings wr
                LEFT JOIN whr_fight_history fh ON 
                    (fh.fighter1_id = wr.fighter_id OR fh.fighter2_id = wr.fighter_id) 
                    AND fh.division = wr.division
                GROUP BY wr.fighter_id, wr.division
            )
            SELECT *
            FROM calculated_records
            WHERE stored_wins != calculated_wins OR stored_losses != calculated_losses
            LIMIT 10
        `).all();

        if (recordMismatches.length > 0) {
            this.issues.push(`Found ${recordMismatches.length} fighters with win/loss count mismatches`);
        }

        // Check for impossible rating changes
        const extremeChanges = this.db.prepare(`
            SELECT 
                fight_id,
                division,
                rating_change_fighter1,
                rating_change_fighter2,
                k_factor,
                expected_outcome,
                actual_outcome
            FROM whr_fight_history
            WHERE ABS(rating_change_fighter1) > 100 OR ABS(rating_change_fighter2) > 100
        `).all();

        if (extremeChanges.length > 0) {
            this.warnings.push(`Found ${extremeChanges.length} fights with extreme rating changes (>100 points)`);
        }

        // Check for ratings that don't sum to zero (should be zero-sum)
        const nonZeroSumFights = this.db.prepare(`
            SELECT 
                fight_id,
                rating_change_fighter1 + rating_change_fighter2 as sum_change
            FROM whr_fight_history
            WHERE ABS(rating_change_fighter1 + rating_change_fighter2) > 0.1
            LIMIT 5
        `).all();

        if (nonZeroSumFights.length > 0) {
            this.warnings.push(`Found ${nonZeroSumFights.length} fights where rating changes don't sum to zero`);
        }

        // Check for temporal consistency in fight processing
        const temporalInconsistencies = this.db.prepare(`
            SELECT COUNT(*) as count
            FROM whr_fight_history fh1
            JOIN whr_fight_history fh2 ON fh1.fighter1_id = fh2.fighter1_id AND fh1.division = fh2.division
            JOIN fights f1 ON fh1.fight_id = f1.id
            JOIN fights f2 ON fh2.fight_id = f2.id
            JOIN events e1 ON f1.event_id = e1.id
            JOIN events e2 ON f2.event_id = e2.id
            WHERE e1.date > e2.date AND fh1.calculation_timestamp < fh2.calculation_timestamp
        `).get().count;

        if (temporalInconsistencies > 0) {
            this.issues.push(`Found ${temporalInconsistencies} temporal inconsistencies in fight processing`);
        }

        console.log(`  ✅ Checked record consistency, rating changes, zero-sum property, temporal order`);
    }

    /**
     * 3. Rating Distribution Analysis
     */
    async auditRatingDistributions() {
        console.log('\n🔍 3. Rating Distribution Analysis');
        console.log('─'.repeat(40));

        const distributions = this.db.prepare(`
            SELECT 
                division,
                COUNT(*) as fighter_count,
                AVG(rating) as avg_rating,
                MIN(rating) as min_rating,
                MAX(rating) as max_rating
            FROM whr_ratings
            WHERE fight_count >= 3
            GROUP BY division
        `).all();

        // Calculate outliers separately for each division
        distributions.forEach(div => {
            const outliers = this.db.prepare(`
                SELECT 
                    COUNT(CASE WHEN rating > ? + 200 THEN 1 END) as high_outliers,
                    COUNT(CASE WHEN rating < ? - 200 THEN 1 END) as low_outliers
                FROM whr_ratings
                WHERE division = ? AND fight_count >= 3
            `).get(div.avg_rating, div.avg_rating, div.division);
            
            div.high_outliers = outliers.high_outliers;
            div.low_outliers = outliers.low_outliers;
        });

        distributions.forEach(div => {
            // Check for unrealistic averages
            if (div.avg_rating < 1400 || div.avg_rating > 1600) {
                this.warnings.push(`${div.division} has unusual average rating: ${div.avg_rating.toFixed(0)}`);
            }

            // Check for too many outliers
            const outlierPercentage = (div.high_outliers + div.low_outliers) / div.fighter_count * 100;
            if (outlierPercentage > 10) {
                this.warnings.push(`${div.division} has ${outlierPercentage.toFixed(1)}% outlier ratings`);
            }

            // Check for insufficient spread
            const spread = div.max_rating - div.min_rating;
            if (spread < 200) {
                this.warnings.push(`${div.division} has insufficient rating spread: ${spread.toFixed(0)} points`);
            }
        });

        console.log(`  ✅ Analyzed rating distributions for ${distributions.length} divisions`);
    }

    /**
     * 4. Temporal Accuracy Verification
     */
    async auditTemporalAccuracy() {
        console.log('\n🔍 4. Temporal Accuracy Verification');
        console.log('─'.repeat(40));

        // Check for future data leakage
        const futureDataLeaks = this.db.prepare(`
            SELECT COUNT(*) as count
            FROM whr_fight_history fh
            JOIN fights f ON fh.fight_id = f.id
            JOIN events e ON f.event_id = e.id
            WHERE fh.calculation_timestamp < e.date
        `).get().count;

        if (futureDataLeaks > 0) {
            this.issues.push(`Found ${futureDataLeaks} instances of potential future data leakage`);
        }

        // Check for chronological processing order
        const chronologyViolations = this.db.prepare(`
            WITH fight_sequence AS (
                SELECT 
                    fh.fighter1_id as fighter_id,
                    fh.division,
                    fh.fight_id,
                    e.date,
                    fh.fighter1_pre_rating as pre_rating,
                    ROW_NUMBER() OVER (PARTITION BY fh.fighter1_id, fh.division ORDER BY e.date) as sequence
                FROM whr_fight_history fh
                JOIN fights f ON fh.fight_id = f.id
                JOIN events e ON f.event_id = e.id
                UNION ALL
                SELECT 
                    fh.fighter2_id as fighter_id,
                    fh.division,
                    fh.fight_id,
                    e.date,
                    fh.fighter2_pre_rating as pre_rating,
                    ROW_NUMBER() OVER (PARTITION BY fh.fighter2_id, fh.division ORDER BY e.date) as sequence
                FROM whr_fight_history fh
                JOIN fights f ON fh.fight_id = f.id
                JOIN events e ON f.event_id = e.id
            )
            SELECT COUNT(*) as violations
            FROM fight_sequence fs1
            JOIN fight_sequence fs2 ON 
                fs1.fighter_id = fs2.fighter_id AND 
                fs1.division = fs2.division AND
                fs1.sequence = fs2.sequence - 1
            WHERE fs1.date > fs2.date
        `).get().violations;

        if (chronologyViolations > 0) {
            this.issues.push(`Found ${chronologyViolations} chronological processing violations`);
        }

        console.log(`  ✅ Verified temporal accuracy and chronological processing`);
    }

    /**
     * 5. Parameter Validation
     */
    async auditParameterValidation() {
        console.log('\n🔍 5. Parameter Validation');
        console.log('─'.repeat(40));

        // Check empirical parameters exist and are reasonable
        const empiricalParams = this.db.prepare(`
            SELECT parameter_type, parameter_data
            FROM empirical_parameters
        `).all();

        if (empiricalParams.length === 0) {
            this.issues.push('No empirical parameters found in database');
            return;
        }

        empiricalParams.forEach(param => {
            try {
                const data = JSON.parse(param.parameter_data);
                
                switch (param.parameter_type) {
                    case 'ring_rust':
                        if (data.threshold < 200 || data.threshold > 500) {
                            this.warnings.push(`Ring rust threshold seems unusual: ${data.threshold} days`);
                        }
                        if (data.penalty < 0.01 || data.penalty > 0.2) {
                            this.warnings.push(`Ring rust penalty seems unusual: ${data.penalty * 100}%`);
                        }
                        break;
                        
                    case 'k_factor_adjustments':
                        Object.entries(data).forEach(([level, multiplier]) => {
                            if (multiplier < 0.5 || multiplier > 2.0) {
                                this.warnings.push(`K-factor multiplier for ${level} seems extreme: ${multiplier}`);
                            }
                        });
                        break;
                }
            } catch (error) {
                this.issues.push(`Invalid JSON in empirical parameter: ${param.parameter_type}`);
            }
        });

        // Check division parameters are complete
        const incompleteDivisions = this.db.prepare(`
            SELECT division
            FROM division_parameters
            WHERE initial_rating IS NULL OR k_factor IS NULL OR rating_scale_divisor IS NULL
        `).all();

        if (incompleteDivisions.length > 0) {
            this.issues.push(`Incomplete parameters for divisions: ${incompleteDivisions.map(d => d.division).join(', ')}`);
        }

        console.log(`  ✅ Validated ${empiricalParams.length} empirical parameter sets`);
    }

    /**
     * 6. Edge Case Detection
     */
    async auditEdgeCases() {
        console.log('\n🔍 6. Edge Case Detection');
        console.log('─'.repeat(40));

        // Fighters with 0 fights but ratings
        const zeroFightRatings = this.db.prepare(`
            SELECT COUNT(*) as count
            FROM whr_ratings
            WHERE fight_count = 0 AND rating != (
                SELECT initial_rating 
                FROM division_parameters dp 
                WHERE dp.division = whr_ratings.division
            )
        `).get().count;

        if (zeroFightRatings > 0) {
            this.warnings.push(`Found ${zeroFightRatings} fighters with 0 fights but non-initial ratings`);
        }

        // Fighters with very high deviations after many fights
        const highDeviationVeterans = this.db.prepare(`
            SELECT COUNT(*) as count
            FROM whr_ratings
            WHERE fight_count > 10 AND rating_deviation > 100
        `).get().count;

        if (highDeviationVeterans > 0) {
            this.warnings.push(`Found ${highDeviationVeterans} veterans with high rating uncertainty`);
        }

        // Cross-division rating inconsistencies
        const crossDivisionInconsistencies = this.db.prepare(`
            SELECT 
                fighter_id,
                COUNT(*) as division_count,
                MAX(rating) - MIN(rating) as rating_spread
            FROM whr_ratings
            WHERE fight_count >= 3
            GROUP BY fighter_id
            HAVING division_count > 1 AND rating_spread > 300
        `).all();

        if (crossDivisionInconsistencies.length > 0) {
            this.warnings.push(`Found ${crossDivisionInconsistencies.length} fighters with large cross-division rating differences`);
        }

        // Fighters ranked but with very few fights
        const underqualifiedRanked = this.db.prepare(`
            SELECT COUNT(*) as count
            FROM whr_division_rankings dr
            JOIN whr_ratings wr ON dr.fighter_id = wr.fighter_id AND dr.division = wr.division
            WHERE wr.fight_count < 3 AND dr.rank <= 15
        `).get().count;

        if (underqualifiedRanked > 0) {
            this.warnings.push(`Found ${underqualifiedRanked} top-15 fighters with < 3 fights`);
        }

        console.log(`  ✅ Detected potential edge cases and anomalies`);
    }

    /**
     * 7. Performance Analysis
     */
    async auditPerformance() {
        console.log('\n🔍 7. Performance Analysis');
        console.log('─'.repeat(40));

        // Check for missing indexes
        const indexes = this.db.prepare(`
            SELECT name FROM sqlite_master WHERE type = 'index'
        `).all();

        const requiredIndexes = [
            'fighter_id', 'division', 'event_id', 'date'
        ];

        const indexNames = indexes.map(i => i.name.toLowerCase());
        const missingIndexes = requiredIndexes.filter(req => 
            !indexNames.some(idx => idx.includes(req))
        );

        if (missingIndexes.length > 0) {
            this.recommendations.push(`Consider adding indexes for: ${missingIndexes.join(', ')}`);
        }

        // Check table sizes
        const tableSizes = this.db.prepare(`
            SELECT 
                name,
                COUNT(*) as row_count
            FROM (
                SELECT 'whr_ratings' as name UNION ALL
                SELECT 'whr_fight_history' UNION ALL
                SELECT 'whr_division_rankings' UNION ALL
                SELECT 'fighters' UNION ALL
                SELECT 'fights'
            ) tables
            JOIN (
                SELECT COUNT(*) as cnt FROM whr_ratings
                UNION ALL SELECT COUNT(*) FROM whr_fight_history
                UNION ALL SELECT COUNT(*) FROM whr_division_rankings  
                UNION ALL SELECT COUNT(*) FROM fighters
                UNION ALL SELECT COUNT(*) FROM fights
            ) counts
        `);

        // Estimate query performance
        const slowQueries = [];
        
        try {
            const start = Date.now();
            this.db.prepare(`
                SELECT dr.*, f.first_name, f.last_name
                FROM whr_division_rankings dr
                JOIN fighters f ON dr.fighter_id = f.id
                WHERE dr.division = 'Lightweight'
                ORDER BY dr.rank
            `).all();
            const rankingTime = Date.now() - start;
            
            if (rankingTime > 100) {
                this.recommendations.push(`Ranking query took ${rankingTime}ms, consider optimization`);
            }
        } catch (error) {
            this.warnings.push('Could not test ranking query performance');
        }

        console.log(`  ✅ Analyzed performance characteristics and indexing`);
    }

    /**
     * 8. Prediction Accuracy Deep Dive
     */
    async auditPredictionAccuracy() {
        console.log('\n🔍 8. Prediction Accuracy Deep Dive');
        console.log('─'.repeat(40));

        // Analyze prediction accuracy by division
        const accuracyByDivision = this.db.prepare(`
            SELECT 
                division,
                COUNT(*) as total_predictions,
                AVG(CASE 
                    WHEN (expected_outcome > 0.5 AND actual_outcome = 1) OR
                         (expected_outcome < 0.5 AND actual_outcome = 0) OR
                         (expected_outcome = 0.5 AND actual_outcome = 0.5)
                    THEN 1.0 ELSE 0.0 
                END) as accuracy,
                AVG(ABS(expected_outcome - actual_outcome)) as avg_error
            FROM whr_fight_history
            GROUP BY division
            ORDER BY accuracy DESC
        `).all();

        accuracyByDivision.forEach(div => {
            if (div.accuracy < 0.45) {
                this.warnings.push(`${div.division} has very low prediction accuracy: ${(div.accuracy * 100).toFixed(1)}%`);
            }
            if (div.avg_error > 0.5) {
                this.warnings.push(`${div.division} has high prediction error: ${div.avg_error.toFixed(3)}`);
            }
        });

        // Check for systematic biases
        const favoriteWinRate = this.db.prepare(`
            SELECT 
                AVG(CASE WHEN expected_outcome > 0.5 THEN actual_outcome ELSE 1 - actual_outcome END) as favorite_win_rate
            FROM whr_fight_history
            WHERE expected_outcome != 0.5
        `).get().favorite_win_rate;

        if (Math.abs(favoriteWinRate - 0.5) > 0.1) {
            this.warnings.push(`Systematic bias detected: favorites win ${(favoriteWinRate * 100).toFixed(1)}% of the time`);
        }

        console.log(`  ✅ Analyzed prediction accuracy across ${accuracyByDivision.length} divisions`);
        console.log(`  Overall favorite win rate: ${(favoriteWinRate * 100).toFixed(1)}%`);
    }

    /**
     * Generate comprehensive audit report
     */
    generateAuditReport() {
        console.log('\n' + '═'.repeat(60));
        console.log('📋 COMPREHENSIVE SYSTEM AUDIT REPORT');
        console.log('═'.repeat(60));

        if (this.issues.length === 0 && this.warnings.length === 0) {
            console.log('✅ EXCELLENT: No critical issues or warnings found!');
            console.log('   Your WHR system appears to be functioning optimally.');
        } else {
            if (this.issues.length > 0) {
                console.log('\n❌ CRITICAL ISSUES:');
                this.issues.forEach((issue, i) => {
                    console.log(`   ${i + 1}. ${issue}`);
                });
            }

            if (this.warnings.length > 0) {
                console.log('\n⚠️  WARNINGS:');
                this.warnings.forEach((warning, i) => {
                    console.log(`   ${i + 1}. ${warning}`);
                });
            }
        }

        if (this.recommendations.length > 0) {
            console.log('\n💡 RECOMMENDATIONS:');
            this.recommendations.forEach((rec, i) => {
                console.log(`   ${i + 1}. ${rec}`);
            });
        }

        // Summary
        console.log('\n📊 AUDIT SUMMARY:');
        console.log(`   Critical Issues: ${this.issues.length}`);
        console.log(`   Warnings: ${this.warnings.length}`);
        console.log(`   Recommendations: ${this.recommendations.length}`);

        // Overall health score
        let healthScore = 100;
        healthScore -= this.issues.length * 20;
        healthScore -= this.warnings.length * 5;
        healthScore = Math.max(0, healthScore);

        console.log(`   System Health Score: ${healthScore}/100`);

        if (healthScore >= 90) {
            console.log('   Status: EXCELLENT 🏆');
        } else if (healthScore >= 75) {
            console.log('   Status: GOOD ✅');
        } else if (healthScore >= 50) {
            console.log('   Status: NEEDS ATTENTION ⚠️');
        } else {
            console.log('   Status: CRITICAL ISSUES ❌');
        }

        console.log('═'.repeat(60));

        // Save detailed report
        const report = {
            timestamp: new Date().toISOString(),
            healthScore,
            issues: this.issues,
            warnings: this.warnings,
            recommendations: this.recommendations,
            summary: {
                criticalIssues: this.issues.length,
                warnings: this.warnings.length,
                recommendations: this.recommendations.length
            }
        };

        const reportPath = path.join(__dirname, '..', '..', 'analysis-output', 'comprehensive-audit-report.json');
        require('fs').writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📄 Detailed report saved to: ${reportPath}`);
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) this.db.close();
    }
}

// Run audit if called directly
if (require.main === module) {
    const auditor = new ComprehensiveSystemAudit();
    
    auditor.runCompleteAudit()
        .then(() => {
            console.log('\n✅ Comprehensive audit completed!');
        })
        .catch(error => {
            console.error('❌ Audit failed:', error);
        })
        .finally(() => {
            auditor.close();
        });
}

module.exports = ComprehensiveSystemAudit;