const Database = require('better-sqlite3');
const path = require('path');

/**
 * Enhanced Backtesting Script
 * 
 * Tests the enhanced SoS system by using current ratings from whr_ratings table
 * to make fresh predictions on historical fights with proper temporal accuracy
 */

class EnhancedBacktesting {
    constructor() {
        this.dbPath = path.join(__dirname, '..', '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        
        console.log('🧪 Enhanced Backtesting System initialized');
    }

    async runEnhancedBacktesting() {
        console.log('🚀 ENHANCED BACKTESTING WITH CURRENT RATINGS');
        console.log('═'.repeat(70));
        console.log('Using enhanced SoS ratings to predict historical fights');
        console.log('Excluding catch weight fights from all calculations\n');
        
        // Get division parameters
        const divisionParams = this.loadDivisionParameters();
        
        // Test each division
        const results = [];
        let totalFights = 0;
        let totalCorrect = 0;
        
        for (const [division, params] of divisionParams) {
            if (division === 'Catch Weight') continue; // Skip catch weight
            
            const result = await this.testDivisionWithCurrentRatings(division, params);
            if (result) {
                results.push(result);
                totalFights += result.totalFights;
                totalCorrect += result.correctPredictions;
            }
        }
        
        // Overall summary
        const overallAccuracy = (totalCorrect / totalFights * 100).toFixed(1);
        
        console.log('\n═'.repeat(70));
        console.log('📊 ENHANCED BACKTESTING SUMMARY');
        console.log('═'.repeat(70));
        console.log(`Total Fights Tested: ${totalFights.toLocaleString()}`);
        console.log(`Correct Predictions: ${totalCorrect.toLocaleString()}`);
        console.log(`Overall Accuracy: ${overallAccuracy}%`);
        
        // Compare to baseline
        const baseline = 53.3;
        const improvement = parseFloat(overallAccuracy) - baseline;
        console.log(`Baseline (before SoS): 53.3%`);
        console.log(`Improvement: ${improvement >= 0 ? '+' : ''}${improvement.toFixed(1)}%`);
        
        // Performance rating
        let rating = 'POOR';
        if (parseFloat(overallAccuracy) >= 62) rating = 'EXCELLENT (TARGET ACHIEVED!)';
        else if (parseFloat(overallAccuracy) >= 58) rating = 'GOOD (TARGET RANGE)';
        else if (parseFloat(overallAccuracy) >= 55) rating = 'FAIR';
        
        console.log(`\n🏆 System Performance: ${rating}`);
        
        return {
            overallAccuracy: parseFloat(overallAccuracy),
            improvement,
            totalFights,
            totalCorrect,
            divisionResults: results,
            rating
        };
    }

    /**
     * Test division accuracy using current enhanced ratings
     */
    async testDivisionWithCurrentRatings(division, params) {
        console.log(`\n🥊 Testing ${division} with Enhanced Ratings...`);
        
        // Get current ratings for this division
        const currentRatings = new Map();
        const ratings = this.db.prepare(`
            SELECT fighter_id, rating, rating_deviation, fight_count
            FROM whr_ratings 
            WHERE division = ?
        `).all(division);
        
        ratings.forEach(r => {
            currentRatings.set(r.fighter_id, r);
        });
        
        console.log(`   📊 Loaded ${currentRatings.size} fighter ratings`);
        
        // Get historical fights for this division (excluding catch weight)
        const fights = this.db.prepare(`
            SELECT 
                f.id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                f.weight_class,
                e.date as event_date,
                e.event_name,
                f1.first_name || ' ' || f1.last_name as fighter1_name,
                f2.first_name || ' ' || f2.last_name as fighter2_name
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE f.weight_class = ?
                AND f.weight_class != 'Catch Weight'
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%Draw%'
                AND f.winner_id IS NOT NULL
            ORDER BY e.date
        `).all(division);
        
        if (fights.length === 0) {
            console.log(`   ❌ No valid fights found for ${division}`);
            return null;
        }
        
        // Make predictions using current enhanced ratings
        let correctPredictions = 0;
        let totalFights = 0;
        let totalLogLoss = 0;
        const confidenceStats = [
            { threshold: 0.55, count: 0, correct: 0 },
            { threshold: 0.60, count: 0, correct: 0 },
            { threshold: 0.65, count: 0, correct: 0 },
            { threshold: 0.70, count: 0, correct: 0 }
        ];
        
        for (const fight of fights) {
            const fighter1Rating = currentRatings.get(fight.fighter1_id);
            const fighter2Rating = currentRatings.get(fight.fighter2_id);
            
            // Skip if we don't have ratings for both fighters
            if (!fighter1Rating || !fighter2Rating) continue;
            
            // Calculate prediction using enhanced ratings
            const ratingDiff = fighter1Rating.rating - fighter2Rating.rating;
            const expectedOutcome = 1 / (1 + Math.pow(10, -ratingDiff / params.rating_scale_divisor));
            
            // Determine actual outcome
            const actualOutcome = fight.winner_id === fight.fighter1_id ? 1 : 0;
            
            // Check prediction accuracy
            const predictedWinner = expectedOutcome > 0.5 ? 1 : 0;
            if (predictedWinner === actualOutcome) {
                correctPredictions++;
            }
            
            totalFights++;
            
            // Log loss calculation
            const clampedProb = Math.max(0.001, Math.min(0.999, expectedOutcome));
            if (actualOutcome === 1) {
                totalLogLoss += -Math.log(clampedProb);
            } else {
                totalLogLoss += -Math.log(1 - clampedProb);
            }
            
            // Confidence analysis
            const confidence = Math.max(expectedOutcome, 1 - expectedOutcome);
            for (const stat of confidenceStats) {
                if (confidence >= stat.threshold) {
                    stat.count++;
                    if (predictedWinner === actualOutcome) {
                        stat.correct++;
                    }
                    break;
                }
            }
        }
        
        if (totalFights === 0) {
            console.log(`   ❌ No valid predictions could be made for ${division}`);
            return null;
        }
        
        // Calculate metrics
        const accuracy = (correctPredictions / totalFights * 100).toFixed(1);
        const avgLogLoss = (totalLogLoss / totalFights).toFixed(3);
        
        console.log(`   📊 Fights analyzed: ${totalFights.toLocaleString()}`);
        console.log(`   🎯 Accuracy: ${accuracy}%`);
        console.log(`   📈 Log Loss: ${avgLogLoss} (lower is better)`);
        
        // Show confidence analysis
        console.log(`   🔍 Confidence Analysis:`);
        confidenceStats.forEach(stat => {
            if (stat.count > 0) {
                const confAccuracy = (stat.correct / stat.count * 100).toFixed(1);
                console.log(`      ${(stat.threshold * 100).toFixed(0)}%+ confidence: ${confAccuracy}% (${stat.count} fights)`);
            }
        });
        
        return {
            division,
            totalFights,
            correctPredictions,
            accuracy: parseFloat(accuracy),
            logLoss: parseFloat(avgLogLoss),
            confidenceStats: confidenceStats.filter(s => s.count > 0)
        };
    }

    /**
     * Load division parameters
     */
    loadDivisionParameters() {
        const params = this.db.prepare(`
            SELECT division, rating_scale_divisor, k_factor, initial_rating
            FROM division_parameters
        `).all();
        
        const paramMap = new Map();
        params.forEach(p => {
            paramMap.set(p.division, p);
        });
        
        return paramMap;
    }

    close() {
        if (this.db) this.db.close();
        console.log('🔒 Database connection closed');
    }
}

// Run enhanced backtesting if called directly
if (require.main === module) {
    const backtester = new EnhancedBacktesting();
    
    backtester.runEnhancedBacktesting()
        .then(results => {
            console.log('\n🎉 Enhanced backtesting completed successfully!');
            console.log(`🎯 Final accuracy: ${results.overallAccuracy}%`);
            
            if (results.overallAccuracy >= 58) {
                console.log('🏆 TARGET ACCURACY ACHIEVED! (58-62% range)');
            } else if (results.improvement > 0) {
                console.log('📈 System shows improvement over baseline');
            } else {
                console.log('⚠️  System needs further optimization');
            }
            
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Enhanced backtesting failed:', error.message);
            console.error(error.stack);
            process.exit(1);
        })
        .finally(() => {
            backtester.close();
        });
}

module.exports = EnhancedBacktesting;
