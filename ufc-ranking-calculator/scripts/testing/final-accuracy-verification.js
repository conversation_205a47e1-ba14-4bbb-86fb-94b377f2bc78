const Database = require('better-sqlite3');
const path = require('path');

/**
 * Final Accuracy Verification Script
 * 
 * Provides the definitive accuracy assessment of the enhanced SoS system
 * using multiple testing methodologies to avoid overfitting
 */

class FinalAccuracyVerification {
    constructor() {
        this.dbPath = path.join(__dirname, '..', '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        
        console.log('🔍 Final Accuracy Verification System initialized');
    }

    async runFinalVerification() {
        console.log('🎯 FINAL ACCURACY VERIFICATION');
        console.log('═'.repeat(70));
        console.log('Multiple testing methodologies to determine true accuracy\n');
        
        const results = {
            currentAccuracy: await this.testCurrentAccuracy(),
            recentFights: await this.testRecentFights(),
            crossValidation: await this.testCrossValidation(),
            baseline: 53.3
        };
        
        // Final assessment
        this.provideFinalAssessment(results);
        
        return results;
    }

    /**
     * Test 1: Current System Accuracy (2024-2025 fights)
     */
    async testCurrentAccuracy() {
        console.log('📊 Test 1: Current System Accuracy (2024-2025)');
        console.log('─'.repeat(50));
        
        const fights = this.db.prepare(`
            SELECT 
                f.weight_class,
                COUNT(*) as total_fights,
                SUM(CASE 
                    WHEN (fh.expected_outcome > 0.5 AND fh.actual_outcome = 1) OR
                         (fh.expected_outcome < 0.5 AND fh.actual_outcome = 0)
                    THEN 1 ELSE 0 
                END) as correct_predictions
            FROM whr_fight_history fh
            JOIN fights f ON fh.fight_id = f.id
            JOIN events e ON f.event_id = e.id
            WHERE e.date >= '2024-01-01'
                AND f.weight_class != 'Catch Weight'
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%Draw%'
            GROUP BY f.weight_class
        `).all();
        
        let totalFights = 0;
        let totalCorrect = 0;
        
        console.log('Division Results:');
        fights.forEach(fight => {
            const accuracy = fight.total_fights > 0 ? 
                (fight.correct_predictions / fight.total_fights * 100).toFixed(1) : 0;
            console.log(`  ${fight.weight_class}: ${accuracy}% (${fight.correct_predictions}/${fight.total_fights})`);
            
            totalFights += fight.total_fights;
            totalCorrect += fight.correct_predictions;
        });
        
        const overallAccuracy = totalFights > 0 ? 
            (totalCorrect / totalFights * 100).toFixed(1) : 0;
        
        console.log(`\nOverall 2024-2025 Accuracy: ${overallAccuracy}%`);
        console.log(`Total fights tested: ${totalFights}`);
        
        return {
            accuracy: parseFloat(overallAccuracy),
            totalFights,
            totalCorrect,
            divisionBreakdown: fights
        };
    }

    /**
     * Test 2: Recent Fights (Last 6 months)
     */
    async testRecentFights() {
        console.log('\n📊 Test 2: Recent Fights (Last 6 months)');
        console.log('─'.repeat(50));
        
        const sixMonthsAgo = new Date();
        sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
        const cutoffDate = sixMonthsAgo.toISOString().split('T')[0];
        
        const result = this.db.prepare(`
            SELECT 
                COUNT(*) as total_fights,
                SUM(CASE 
                    WHEN (fh.expected_outcome > 0.5 AND fh.actual_outcome = 1) OR
                         (fh.expected_outcome < 0.5 AND fh.actual_outcome = 0)
                    THEN 1 ELSE 0 
                END) as correct_predictions
            FROM whr_fight_history fh
            JOIN fights f ON fh.fight_id = f.id
            JOIN events e ON f.event_id = e.id
            WHERE e.date >= ?
                AND f.weight_class != 'Catch Weight'
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%Draw%'
        `).get(cutoffDate);
        
        const accuracy = result.total_fights > 0 ? 
            (result.correct_predictions / result.total_fights * 100).toFixed(1) : 0;
        
        console.log(`Recent fights accuracy: ${accuracy}%`);
        console.log(`Total recent fights: ${result.total_fights}`);
        
        return {
            accuracy: parseFloat(accuracy),
            totalFights: result.total_fights,
            totalCorrect: result.correct_predictions
        };
    }

    /**
     * Test 3: Cross-Validation (Hold-out test)
     */
    async testCrossValidation() {
        console.log('\n📊 Test 3: Cross-Validation (Hold-out 20%)');
        console.log('─'.repeat(50));
        
        // Use last 20% of fights from each division as test set
        const divisions = this.db.prepare(`
            SELECT DISTINCT weight_class 
            FROM fights 
            WHERE weight_class != 'Catch Weight'
        `).all();
        
        let totalTestFights = 0;
        let totalCorrect = 0;
        
        for (const div of divisions) {
            const divisionFights = this.db.prepare(`
                SELECT 
                    COUNT(*) as total_fights,
                    SUM(CASE 
                        WHEN (fh.expected_outcome > 0.5 AND fh.actual_outcome = 1) OR
                             (fh.expected_outcome < 0.5 AND fh.actual_outcome = 0)
                        THEN 1 ELSE 0 
                    END) as correct_predictions
                FROM (
                    SELECT fh.*, ROW_NUMBER() OVER (ORDER BY e.date DESC) as rn,
                           COUNT(*) OVER () as total_count
                    FROM whr_fight_history fh
                    JOIN fights f ON fh.fight_id = f.id
                    JOIN events e ON f.event_id = e.id
                    WHERE f.weight_class = ?
                        AND f.result_method NOT LIKE '%No Contest%'
                        AND f.result_method NOT LIKE '%Draw%'
                ) ranked_fights
                WHERE rn <= total_count * 0.2
            `).get(div.weight_class);
            
            if (divisionFights.total_fights > 0) {
                totalTestFights += divisionFights.total_fights;
                totalCorrect += divisionFights.correct_predictions;
            }
        }
        
        const accuracy = totalTestFights > 0 ? 
            (totalCorrect / totalTestFights * 100).toFixed(1) : 0;
        
        console.log(`Cross-validation accuracy: ${accuracy}%`);
        console.log(`Total test fights: ${totalTestFights}`);
        
        return {
            accuracy: parseFloat(accuracy),
            totalFights: totalTestFights,
            totalCorrect
        };
    }

    /**
     * Provide final assessment
     */
    provideFinalAssessment(results) {
        console.log('\n🏆 FINAL ASSESSMENT');
        console.log('═'.repeat(70));
        
        const accuracies = [
            results.currentAccuracy.accuracy,
            results.recentFights.accuracy,
            results.crossValidation.accuracy
        ].filter(acc => acc > 0);
        
        if (accuracies.length === 0) {
            console.log('❌ Unable to determine accuracy - insufficient data');
            return;
        }
        
        const avgAccuracy = accuracies.reduce((a, b) => a + b, 0) / accuracies.length;
        const minAccuracy = Math.min(...accuracies);
        const maxAccuracy = Math.max(...accuracies);
        
        console.log(`📊 Accuracy Range: ${minAccuracy.toFixed(1)}% - ${maxAccuracy.toFixed(1)}%`);
        console.log(`📊 Average Accuracy: ${avgAccuracy.toFixed(1)}%`);
        console.log(`📊 Baseline (before SoS): ${results.baseline}%`);
        
        const improvement = avgAccuracy - results.baseline;
        console.log(`📊 Improvement: ${improvement >= 0 ? '+' : ''}${improvement.toFixed(1)}%`);
        
        // Final verdict
        let verdict = '';
        let status = '';
        
        if (avgAccuracy >= 62) {
            verdict = 'EXCELLENT - TARGET EXCEEDED!';
            status = '🏆';
        } else if (avgAccuracy >= 58) {
            verdict = 'GOOD - TARGET ACHIEVED!';
            status = '✅';
        } else if (avgAccuracy >= 55) {
            verdict = 'FAIR - MEANINGFUL IMPROVEMENT';
            status = '📈';
        } else if (improvement > 0) {
            verdict = 'SLIGHT IMPROVEMENT';
            status = '📊';
        } else {
            verdict = 'NO IMPROVEMENT';
            status = '❌';
        }
        
        console.log(`\n${status} FINAL VERDICT: ${verdict}`);
        
        // Recommendations
        console.log('\n💡 RECOMMENDATIONS:');
        if (avgAccuracy >= 58) {
            console.log('• System is performing excellently and ready for production');
            console.log('• Consider implementing confidence-based betting strategies');
            console.log('• Monitor performance on future fights to ensure consistency');
        } else if (improvement > 0) {
            console.log('• System shows improvement but may need further optimization');
            console.log('• Consider additional features like recent form, stylistic matchups');
            console.log('• Test with larger datasets to confirm improvements');
        } else {
            console.log('• System needs significant improvements');
            console.log('• Review SoS calculation methodology');
            console.log('• Consider alternative rating algorithms');
        }
        
        return {
            avgAccuracy,
            improvement,
            verdict,
            status
        };
    }

    close() {
        if (this.db) this.db.close();
        console.log('\n🔒 Database connection closed');
    }
}

// Run final verification if called directly
if (require.main === module) {
    const verifier = new FinalAccuracyVerification();
    
    verifier.runFinalVerification()
        .then(results => {
            console.log('\n🎉 Final accuracy verification completed!');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Final verification failed:', error.message);
            console.error(error.stack);
            process.exit(1);
        })
        .finally(() => {
            verifier.close();
        });
}

module.exports = FinalAccuracyVerification;
