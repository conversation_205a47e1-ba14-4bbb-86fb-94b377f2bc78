const Database = require("better-sqlite3");
const path = require("path");

/**
 * Actual Behavior Testing
 *
 * Tests how the system actually works vs how it should work
 * Verifies real data loading, parameter usage, and calculations
 */

class ActualBehaviorTest {
  constructor() {
    this.dbPath = path.join(__dirname, "..", "..", "data", "ufc_data.db");
    this.db = new Database(this.dbPath);
  }

  async runAllTests() {
    console.log("🔍 ACTUAL BEHAVIOR VERIFICATION TESTS");
    console.log("═".repeat(60));

    try {
      await this.testDataLoading();
      await this.testParameterUsage();
      await this.testCalculationFlow();
      await this.testActualVsExpected();

      console.log("\n✅ All behavior tests completed");
    } catch (error) {
      console.error("\n❌ Behavior test failed:", error.message);
      throw error;
    }
  }

  /**
   * Test 1: Verify what data is actually loaded
   */
  async testDataLoading() {
    console.log("\n📊 Test 1: Data Loading Verification");
    console.log("─".repeat(50));

    // Check if calculated parameters exist
    const tables = this.db
      .prepare("SELECT name FROM sqlite_master WHERE type='table'")
      .all();
    const tableNames = tables.map((t) => t.name);

    console.log("Available tables:");
    tableNames.forEach((name) => console.log(`  - ${name}`));

    // Check division parameters (actual table name)
    if (tableNames.includes("division_parameters")) {
      const params = this.db
        .prepare("SELECT * FROM division_parameters LIMIT 3")
        .all();
      console.log("\n✅ Division parameters loaded:");
      params.forEach((p) => {
        console.log(
          `  ${p.division}: K=${p.k_factor}, Initial=${p.initial_rating}`
        );
      });
    } else {
      console.log("\n❌ Division parameters table missing");
    }

    // Check age curves
    if (tableNames.includes("division_age_curves")) {
      const curves = this.db
        .prepare(
          "SELECT division, peak_age, confidence FROM division_age_curves LIMIT 3"
        )
        .all();
      console.log("\n✅ Age curves loaded:");
      curves.forEach((c) => {
        console.log(
          `  ${c.division}: Peak=${c.peak_age}, Confidence=${c.confidence}`
        );
      });
    } else {
      console.log("\n❌ Age curves table missing");
    }

    // Check statistical weights
    if (tableNames.includes("division_statistical_weights")) {
      const weights = this.db
        .prepare("SELECT COUNT(*) as count FROM division_statistical_weights")
        .get();
      console.log(`\n✅ Statistical weights: ${weights.count} records`);
    } else {
      console.log("\n❌ Statistical weights table missing");
    }

    // Check time decay (actual table name)
    if (tableNames.includes("time_decay_parameters")) {
      const decay = this.db
        .prepare(
          "SELECT division, decay_rate, half_life FROM time_decay_parameters LIMIT 3"
        )
        .all();
      console.log("\n✅ Time decay parameters:");
      decay.forEach((d) => {
        console.log(
          `  ${d.division}: Rate=${d.decay_rate}, Half-life=${d.half_life} days`
        );
      });
    } else {
      console.log("\n❌ Time decay table missing");
    }
  }

  /**
   * Test 2: Verify parameters are actually used in calculations
   */
  async testParameterUsage() {
    console.log("\n📊 Test 2: Parameter Usage Verification");
    console.log("─".repeat(50));

    // Check if WHR ratings use calculated parameters
    const sampleRatings = this.db
      .prepare(
        `
            SELECT wr.*, dbp.k_factor, dbp.initial_rating
            FROM whr_ratings wr
            LEFT JOIN division_parameters dbp ON wr.division = dbp.division
            WHERE wr.fight_count >= 5
            LIMIT 5
        `
      )
      .all();

    if (sampleRatings.length > 0) {
      console.log("✅ Sample ratings with parameters:");
      sampleRatings.forEach((r) => {
        console.log(
          `  ${r.division}: Rating=${r.rating.toFixed(0)}, K=${
            r.k_factor || "DEFAULT"
          }, Initial=${r.initial_rating || "DEFAULT"}`
        );
      });
    } else {
      console.log("❌ No ratings found or parameters not linked");
    }

    // Check if age adjustments are applied
    const ageAdjustments = this.db
      .prepare(
        `
            SELECT COUNT(*) as count
            FROM whr_calculation_events
            WHERE event_type = 'age_adjustment'
        `
      )
      .get();

    console.log(
      `\n${ageAdjustments.count > 0 ? "✅" : "❌"} Age adjustments applied: ${
        ageAdjustments.count
      } events`
    );

    // Check if time decay is applied
    const timeDecayEvents = this.db
      .prepare(
        `
            SELECT COUNT(*) as count
            FROM whr_calculation_events
            WHERE event_type = 'time_decay'
        `
      )
      .get();

    console.log(
      `${timeDecayEvents.count > 0 ? "✅" : "❌"} Time decay applied: ${
        timeDecayEvents.count
      } events`
    );
  }

  /**
   * Test 3: Verify actual calculation flow
   */
  async testCalculationFlow() {
    console.log("\n📊 Test 3: Calculation Flow Verification");
    console.log("─".repeat(50));

    // Check calculation events to see what actually happened
    const eventTypes = this.db
      .prepare(
        `
            SELECT event_type, COUNT(*) as count
            FROM whr_calculation_events
            GROUP BY event_type
            ORDER BY count DESC
        `
      )
      .all();

    if (eventTypes.length > 0) {
      console.log("✅ Calculation events recorded:");
      eventTypes.forEach((e) => {
        console.log(`  ${e.event_type}: ${e.count.toLocaleString()} events`);
      });
    } else {
      console.log("❌ No calculation events found");
    }

    // Check if convergence actually happened
    const convergenceEvents = this.db
      .prepare(
        `
            SELECT details
            FROM whr_calculation_events
            WHERE event_type = 'convergence_iteration'
            ORDER BY timestamp DESC
            LIMIT 3
        `
      )
      .all();

    if (convergenceEvents.length > 0) {
      console.log("\n✅ Convergence iterations found:");
      convergenceEvents.forEach((e) => {
        const details = JSON.parse(e.details);
        console.log(
          `  Iteration ${details.iteration}: Avg change = ${
            details.avg_change?.toFixed(2) || "N/A"
          }`
        );
      });
    } else {
      console.log("\n❌ No convergence iterations recorded");
    }
  }

  /**
   * Test 4: Compare actual vs expected behavior
   */
  async testActualVsExpected() {
    console.log("\n📊 Test 4: Actual vs Expected Behavior");
    console.log("─".repeat(50));

    // Test a specific fighter's rating calculation
    const testFighter = this.db
      .prepare(
        `
            SELECT wr.*, f.first_name, f.last_name
            FROM whr_ratings wr
            JOIN fighters f ON wr.fighter_id = f.id
            WHERE wr.fight_count >= 10
            ORDER BY wr.rating DESC
            LIMIT 1
        `
      )
      .get();

    if (testFighter) {
      console.log(
        `\n🥊 Testing fighter: ${testFighter.first_name} ${testFighter.last_name}`
      );
      console.log(`   Division: ${testFighter.division}`);
      console.log(`   Rating: ${testFighter.rating.toFixed(0)}`);
      console.log(`   Fights: ${testFighter.fight_count}`);
      console.log(`   Confidence: ${testFighter.confidence_level}`);

      // Get their fight history
      const fightHistory = this.db
        .prepare(
          `
                SELECT fh.*, e.date
                FROM whr_fight_history fh
                JOIN fights f ON fh.fight_id = f.id
                JOIN events e ON f.event_id = e.id
                WHERE (fh.fighter1_id = ? OR fh.fighter2_id = ?)
                AND fh.division = ?
                ORDER BY e.date DESC
                LIMIT 3
            `
        )
        .all(
          testFighter.fighter_id,
          testFighter.fighter_id,
          testFighter.division
        );

      console.log(`\n   Recent fights:`);
      fightHistory.forEach((fight) => {
        const isF1 = fight.fighter1_id === testFighter.fighter_id;
        const preRating = isF1
          ? fight.fighter1_pre_rating
          : fight.fighter2_pre_rating;
        const postRating = isF1
          ? fight.fighter1_post_rating
          : fight.fighter2_post_rating;
        const change = postRating - preRating;
        const outcome = isF1 ? fight.actual_outcome : 1 - fight.actual_outcome;

        console.log(
          `     ${fight.date}: ${preRating.toFixed(0)} → ${postRating.toFixed(
            0
          )} (${change > 0 ? "+" : ""}${change.toFixed(1)}) - ${
            outcome > 0.5 ? "WIN" : "LOSS"
          }`
        );
      });
    }

    // Check if ratings follow expected distribution
    const ratingStats = this.db
      .prepare(
        `
            SELECT
                division,
                COUNT(*) as count,
                AVG(rating) as avg_rating,
                MIN(rating) as min_rating,
                MAX(rating) as max_rating
            FROM whr_ratings
            WHERE fight_count >= 3
            GROUP BY division
            ORDER BY count DESC
            LIMIT 5
        `
      )
      .all();

    console.log("\n📊 Rating distributions:");
    ratingStats.forEach((stat) => {
      const avgExpected = 1500; // Expected average
      const deviation = Math.abs(stat.avg_rating - avgExpected);
      const status = deviation < 100 ? "✅" : "⚠️";

      console.log(
        `   ${status} ${stat.division}: Avg=${stat.avg_rating.toFixed(0)} (${
          stat.count
        } fighters)`
      );
    });
  }

  close() {
    if (this.db) this.db.close();
  }
}

// Run tests if called directly
if (require.main === module) {
  const tester = new ActualBehaviorTest();

  tester
    .runAllTests()
    .then(() => {
      console.log("\n🏁 Behavior verification completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("\n💥 Behavior verification failed:", error);
      process.exit(1);
    })
    .finally(() => {
      tester.close();
    });
}

module.exports = ActualBehaviorTest;
