const Database = require('better-sqlite3');
const path = require('path');

/**
 * Division-Specific Backtesting Framework
 * 
 * Comprehensive backtesting of WHR system accuracy by division
 * Tests prediction accuracy, calibration, and statistical significance
 */

class DivisionBacktesting {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
    }

    /**
     * Run comprehensive backtesting by division
     */
    async runDivisionBacktesting() {
        console.log('🧪 Division-Specific Backtesting Analysis');
        console.log('═'.repeat(70));

        // Get all divisions with sufficient data
        const divisions = this.db.prepare(`
            SELECT DISTINCT division, COUNT(*) as fight_count
            FROM whr_fight_history
            GROUP BY division
            HAVING fight_count >= 50
            ORDER BY fight_count DESC
        `).all();

        console.log(`\n📊 Testing ${divisions.length} divisions with sufficient data...\n`);

        const results = [];
        let totalFights = 0;
        let totalCorrect = 0;

        for (const div of divisions) {
            const result = await this.testDivisionAccuracy(div.division);
            results.push(result);
            totalFights += result.totalFights;
            totalCorrect += result.correctPredictions;
        }

        // Overall summary
        const overallAccuracy = (totalCorrect / totalFights * 100).toFixed(1);
        
        console.log('\n' + '═'.repeat(70));
        console.log('📊 OVERALL BACKTESTING SUMMARY');
        console.log('═'.repeat(70));
        console.log(`Total Fights Tested: ${totalFights.toLocaleString()}`);
        console.log(`Correct Predictions: ${totalCorrect.toLocaleString()}`);
        console.log(`Overall Accuracy: ${overallAccuracy}%`);
        
        // Statistical significance test
        const expectedRandom = totalFights * 0.5;
        const improvement = totalCorrect - expectedRandom;
        const improvementPercent = (improvement / expectedRandom * 100).toFixed(1);
        
        console.log(`Random Baseline: 50.0%`);
        console.log(`Improvement: +${improvementPercent}% over random`);
        
        // Confidence intervals
        const p = totalCorrect / totalFights;
        const se = Math.sqrt(p * (1 - p) / totalFights);
        const ci95Lower = ((p - 1.96 * se) * 100).toFixed(1);
        const ci95Upper = ((p + 1.96 * se) * 100).toFixed(1);
        
        console.log(`95% Confidence Interval: [${ci95Lower}%, ${ci95Upper}%]`);
        
        // Performance rating
        let rating = 'POOR';
        if (parseFloat(overallAccuracy) >= 65) rating = 'EXCELLENT';
        else if (parseFloat(overallAccuracy) >= 60) rating = 'GOOD';
        else if (parseFloat(overallAccuracy) >= 55) rating = 'FAIR';
        
        console.log(`\n🏆 System Performance Rating: ${rating}`);
        
        return {
            overallAccuracy: parseFloat(overallAccuracy),
            totalFights,
            totalCorrect,
            divisionResults: results,
            rating
        };
    }

    /**
     * Test accuracy for a specific division
     */
    async testDivisionAccuracy(division) {
        console.log(`\n🥊 Testing ${division}...`);
        
        // Get fight history for this division
        const fights = this.db.prepare(`
            SELECT 
                fh.*,
                f1.first_name || ' ' || f1.last_name as fighter1_name,
                f2.first_name || ' ' || f2.last_name as fighter2_name,
                e.date,
                e.event_name
            FROM whr_fight_history fh
            JOIN fighters f1 ON fh.fighter1_id = f1.id
            JOIN fighters f2 ON fh.fighter2_id = f2.id
            JOIN fights f ON fh.fight_id = f.id
            JOIN events e ON f.event_id = e.id
            WHERE fh.division = ?
            ORDER BY e.date
        `).all(division);

        if (fights.length === 0) {
            console.log(`   ❌ No fight data found for ${division}`);
            return null;
        }

        // Calculate accuracy metrics
        let correctPredictions = 0;
        let totalFights = fights.length;
        let totalLogLoss = 0;
        let calibrationBuckets = Array(10).fill(0).map(() => ({ count: 0, correct: 0 }));
        
        // Confidence buckets for calibration analysis
        const confidenceBuckets = [0.5, 0.6, 0.7, 0.8, 0.9, 1.0];
        const bucketStats = confidenceBuckets.map(threshold => ({ 
            threshold, 
            count: 0, 
            correct: 0, 
            avgConfidence: 0,
            totalConfidence: 0
        }));

        fights.forEach(fight => {
            const predicted = fight.expected_outcome > 0.5 ? 1 : 0;
            const actual = fight.actual_outcome;
            
            // Basic accuracy
            if ((predicted === 1 && actual === 1) || (predicted === 0 && actual === 0)) {
                correctPredictions++;
            }
            
            // Log loss calculation
            const prob = fight.expected_outcome;
            const clampedProb = Math.max(0.001, Math.min(0.999, prob));
            if (actual === 1) {
                totalLogLoss += -Math.log(clampedProb);
            } else {
                totalLogLoss += -Math.log(1 - clampedProb);
            }
            
            // Calibration analysis
            const confidence = Math.max(fight.expected_outcome, 1 - fight.expected_outcome);
            const bucketIndex = Math.min(9, Math.floor(confidence * 10));
            calibrationBuckets[bucketIndex].count++;
            if ((fight.expected_outcome > 0.5 && actual === 1) || 
                (fight.expected_outcome < 0.5 && actual === 0)) {
                calibrationBuckets[bucketIndex].correct++;
            }
            
            // Confidence bucket analysis
            for (let bucket of bucketStats) {
                if (confidence >= bucket.threshold) {
                    bucket.count++;
                    bucket.totalConfidence += confidence;
                    if ((fight.expected_outcome > 0.5 && actual === 1) || 
                        (fight.expected_outcome < 0.5 && actual === 0)) {
                        bucket.correct++;
                    }
                    break;
                }
            }
        });

        // Calculate metrics
        const accuracy = (correctPredictions / totalFights * 100).toFixed(1);
        const avgLogLoss = (totalLogLoss / totalFights).toFixed(3);
        
        // Update average confidence for buckets
        bucketStats.forEach(bucket => {
            if (bucket.count > 0) {
                bucket.avgConfidence = bucket.totalConfidence / bucket.count;
            }
        });

        console.log(`   📊 Fights analyzed: ${totalFights.toLocaleString()}`);
        console.log(`   🎯 Accuracy: ${accuracy}%`);
        console.log(`   📈 Log Loss: ${avgLogLoss} (lower is better)`);
        
        // Show confidence bucket performance
        console.log(`   🔍 Confidence Analysis:`);
        bucketStats.forEach(bucket => {
            if (bucket.count > 0) {
                const bucketAccuracy = (bucket.correct / bucket.count * 100).toFixed(1);
                console.log(`      ${bucket.threshold.toFixed(1)}+ confidence: ${bucketAccuracy}% (${bucket.count} fights)`);
            }
        });

        // Find notable predictions
        const bestPredictions = fights
            .filter(f => Math.abs(f.expected_outcome - f.actual_outcome) < 0.1)
            .sort((a, b) => Math.abs(a.expected_outcome - 0.5) - Math.abs(b.expected_outcome - 0.5))
            .slice(0, 3);
            
        const worstPredictions = fights
            .filter(f => Math.abs(f.expected_outcome - f.actual_outcome) > 0.4)
            .sort((a, b) => Math.abs(b.expected_outcome - b.actual_outcome) - Math.abs(a.expected_outcome - a.actual_outcome))
            .slice(0, 3);

        if (bestPredictions.length > 0) {
            console.log(`   ✅ Best predictions:`);
            bestPredictions.forEach(f => {
                const winner = f.actual_outcome > 0.5 ? f.fighter1_name : f.fighter2_name;
                console.log(`      ${winner} (predicted: ${f.expected_outcome.toFixed(3)}, actual: ${f.actual_outcome})`);
            });
        }

        if (worstPredictions.length > 0) {
            console.log(`   ❌ Biggest misses:`);
            worstPredictions.forEach(f => {
                const winner = f.actual_outcome > 0.5 ? f.fighter1_name : f.fighter2_name;
                const error = Math.abs(f.expected_outcome - f.actual_outcome).toFixed(3);
                console.log(`      ${winner} (predicted: ${f.expected_outcome.toFixed(3)}, error: ${error})`);
            });
        }

        return {
            division,
            totalFights,
            correctPredictions,
            accuracy: parseFloat(accuracy),
            logLoss: parseFloat(avgLogLoss),
            confidenceStats: bucketStats.filter(b => b.count > 0)
        };
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) this.db.close();
    }
}

// Run backtesting if called directly
if (require.main === module) {
    const backtester = new DivisionBacktesting();
    
    backtester.runDivisionBacktesting()
        .then(results => {
            console.log('\n🏁 Backtesting completed successfully!');
            process.exit(0);
        })
        .catch(error => {
            console.error('\n❌ Backtesting failed:', error.message);
            console.error(error.stack);
            process.exit(1);
        })
        .finally(() => {
            backtester.close();
        });
}

module.exports = DivisionBacktesting;
