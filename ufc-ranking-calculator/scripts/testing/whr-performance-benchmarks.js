const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { performance } = require('perf_hooks');

/**
 * WHR Performance Benchmarks
 * 
 * Tests the performance and efficiency of the WHR calculation system
 */

class WHRPerformanceBenchmarks {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        this.results = [];
    }

    /**
     * Benchmark 1: Rating Calculation Speed
     */
    async benchmarkRatingCalculation() {
        console.log('\n⚡ Benchmark 1: Rating Calculation Speed');
        console.log('─'.repeat(50));

        // Test different numbers of fights
        const testSizes = [100, 500, 1000, 5000];
        const results = [];

        for (const size of testSizes) {
            // Get sample fights
            const fights = this.db.prepare(`
                SELECT 
                    f.id,
                    f.fighter1_id,
                    f.fighter2_id,
                    f.winner_id,
                    f.result_method,
                    f.weight_class
                FROM fights f
                WHERE f.result_method NOT LIKE '%No Contest%'
                    AND f.weight_class NOT LIKE '%Catch%'
                LIMIT ?
            `).all(size);

            // Simulate rating calculations
            const startTime = performance.now();
            
            for (const fight of fights) {
                // Simulate rating calculation
                const rating1 = 1500 + Math.random() * 200;
                const rating2 = 1500 + Math.random() * 200;
                const kFactor = 32;
                const scaleDivisor = 200;
                
                const ratingDiff = rating1 - rating2;
                const expected = 1 / (1 + Math.pow(10, -ratingDiff / scaleDivisor));
                const actual = fight.winner_id === fight.fighter1_id ? 1 : 0;
                const change = kFactor * (actual - expected);
                
                // Simulate updates
                const newRating1 = rating1 + change;
                const newRating2 = rating2 - change;
            }
            
            const endTime = performance.now();
            const duration = endTime - startTime;
            const fightsPerSecond = (size / duration) * 1000;
            
            results.push({
                size,
                duration: duration.toFixed(2),
                fightsPerSecond: fightsPerSecond.toFixed(0)
            });
            
            console.log(`  ${size.toString().padStart(5)} fights: ${duration.toFixed(2)}ms (${fightsPerSecond.toFixed(0)} fights/sec)`);
        }

        this.results.push({
            benchmark: 'Rating Calculation Speed',
            results
        });
    }

    /**
     * Benchmark 2: Database Query Performance
     */
    async benchmarkDatabaseQueries() {
        console.log('\n⚡ Benchmark 2: Database Query Performance');
        console.log('─'.repeat(50));

        const queries = [
            {
                name: 'Fighter Rankings',
                sql: `
                    SELECT dr.*, f.first_name, f.last_name
                    FROM whr_division_rankings dr
                    JOIN fighters f ON dr.fighter_id = f.id
                    WHERE dr.division = ?
                    ORDER BY dr.rank
                    LIMIT 50
                `,
                params: ['Lightweight']
            },
            {
                name: 'Fighter History',
                sql: `
                    SELECT fh.*, e.date, e.event_name
                    FROM whr_fight_history fh
                    JOIN fights f ON fh.fight_id = f.id
                    JOIN events e ON f.event_id = e.id
                    WHERE fh.fighter1_id = ? OR fh.fighter2_id = ?
                    ORDER BY e.date DESC
                    LIMIT 20
                `,
                params: [1, 1]
            },
            {
                name: 'Division Statistics',
                sql: `
                    SELECT 
                        division,
                        COUNT(*) as fighter_count,
                        AVG(rating) as avg_rating,
                        MAX(rating) as max_rating,
                        MIN(rating) as min_rating
                    FROM whr_ratings
                    GROUP BY division
                `,
                params: []
            },
            {
                name: 'Recent Updates',
                sql: `
                    SELECT 
                        fh.*,
                        f1.first_name || ' ' || f1.last_name as fighter1_name,
                        f2.first_name || ' ' || f2.last_name as fighter2_name
                    FROM whr_fight_history fh
                    JOIN fighters f1 ON fh.fighter1_id = f1.id
                    JOIN fighters f2 ON fh.fighter2_id = f2.id
                    ORDER BY fh.calculation_timestamp DESC
                    LIMIT 100
                `,
                params: []
            }
        ];

        const results = [];
        
        for (const query of queries) {
            const iterations = 100;
            const stmt = this.db.prepare(query.sql);
            
            const startTime = performance.now();
            for (let i = 0; i < iterations; i++) {
                stmt.all(...query.params);
            }
            const endTime = performance.now();
            
            const avgTime = (endTime - startTime) / iterations;
            const queriesPerSecond = 1000 / avgTime;
            
            results.push({
                query: query.name,
                avgTime: avgTime.toFixed(3),
                queriesPerSecond: queriesPerSecond.toFixed(0)
            });
            
            console.log(`  ${query.name.padEnd(20)}: ${avgTime.toFixed(3)}ms avg (${queriesPerSecond.toFixed(0)} queries/sec)`);
        }

        this.results.push({
            benchmark: 'Database Query Performance',
            results
        });
    }

    /**
     * Benchmark 3: Convergence Algorithm Performance
     */
    async benchmarkConvergence() {
        console.log('\n⚡ Benchmark 3: Convergence Algorithm Performance');
        console.log('─'.repeat(50));

        const divisions = ['Lightweight', 'Welterweight', 'Middleweight'];
        const results = [];

        for (const division of divisions) {
            // Get fighters in division
            const fighters = this.db.prepare(`
                SELECT fighter_id, rating, fight_count
                FROM whr_ratings
                WHERE division = ?
            `).all(division);

            const startTime = performance.now();

            // Simulate convergence iterations
            for (let iteration = 0; iteration < 5; iteration++) {
                for (const fighter of fighters) {
                    // Simulate SoS calculation
                    const avgOpponentRating = 1500 + Math.random() * 100;
                    const sosAdjustment = (avgOpponentRating - fighter.rating) * 0.05 * 0.85;
                    
                    // Simulate age curve
                    const age = 25 + Math.random() * 15;
                    const ageCurve = age < 29 ? 0.95 : Math.exp(-0.03 * (age - 29));
                    const ageAdjustment = (ageCurve - 1) * 10 * 0.7;
                    
                    // Apply adjustments
                    fighter.rating += sosAdjustment + ageAdjustment;
                }
            }

            const endTime = performance.now();
            const duration = endTime - startTime;
            
            results.push({
                division,
                fighterCount: fighters.length,
                duration: duration.toFixed(2),
                timePerFighter: (duration / fighters.length).toFixed(3)
            });
            
            console.log(`  ${division.padEnd(20)}: ${fighters.length} fighters in ${duration.toFixed(2)}ms (${(duration / fighters.length).toFixed(3)}ms per fighter)`);
        }

        this.results.push({
            benchmark: 'Convergence Algorithm Performance',
            results
        });
    }

    /**
     * Benchmark 4: Memory Usage
     */
    async benchmarkMemoryUsage() {
        console.log('\n⚡ Benchmark 4: Memory Usage Analysis');
        console.log('─'.repeat(50));

        const results = [];
        
        // Test memory usage for different operations
        const operations = [
            {
                name: 'Load All Ratings',
                operation: () => {
                    return this.db.prepare('SELECT * FROM whr_ratings').all();
                }
            },
            {
                name: 'Load Fight History',
                operation: () => {
                    return this.db.prepare('SELECT * FROM whr_fight_history LIMIT 10000').all();
                }
            },
            {
                name: 'Load Rankings',
                operation: () => {
                    return this.db.prepare(`
                        SELECT dr.*, f.*, wr.*
                        FROM whr_division_rankings dr
                        JOIN fighters f ON dr.fighter_id = f.id
                        JOIN whr_ratings wr ON dr.fighter_id = wr.fighter_id AND dr.division = wr.division
                    `).all();
                }
            }
        ];

        for (const op of operations) {
            const memBefore = process.memoryUsage();
            const startTime = performance.now();
            
            const data = op.operation();
            
            const endTime = performance.now();
            const memAfter = process.memoryUsage();
            
            const memUsed = (memAfter.heapUsed - memBefore.heapUsed) / 1024 / 1024;
            const duration = endTime - startTime;
            
            results.push({
                operation: op.name,
                recordCount: data.length,
                memoryMB: memUsed.toFixed(2),
                duration: duration.toFixed(2)
            });
            
            console.log(`  ${op.name.padEnd(20)}: ${data.length} records, ${memUsed.toFixed(2)}MB, ${duration.toFixed(2)}ms`);
            
            // Clean up
            global.gc && global.gc();
        }

        this.results.push({
            benchmark: 'Memory Usage Analysis',
            results
        });
    }

    /**
     * Benchmark 5: Bulk Operations
     */
    async benchmarkBulkOperations() {
        console.log('\n⚡ Benchmark 5: Bulk Operation Performance');
        console.log('─'.repeat(50));

        const results = [];

        // Test bulk insert performance
        const testData = [];
        for (let i = 0; i < 1000; i++) {
            testData.push({
                fighter_id: Math.floor(Math.random() * 1000) + 1,
                division: ['Lightweight', 'Welterweight', 'Middleweight'][Math.floor(Math.random() * 3)],
                rating: 1500 + Math.random() * 200,
                deviation: 50 + Math.random() * 50,
                fight_count: Math.floor(Math.random() * 20),
                wins: Math.floor(Math.random() * 15),
                losses: Math.floor(Math.random() * 10)
            });
        }

        // Test transaction vs individual inserts
        const insertStmt = this.db.prepare(`
            INSERT OR REPLACE INTO whr_ratings 
            (fighter_id, division, rating, rating_deviation, fight_count, win_count, loss_count, last_fight_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))
        `);

        // Individual inserts (limited to 100 for time)
        const individualStart = performance.now();
        for (let i = 0; i < 100; i++) {
            const d = testData[i];
            insertStmt.run(d.fighter_id, d.division, d.rating, d.deviation, d.fight_count, d.wins, d.losses);
        }
        const individualTime = performance.now() - individualStart;

        // Transaction insert
        const transactionStart = performance.now();
        const insertMany = this.db.transaction((data) => {
            for (const d of data) {
                insertStmt.run(d.fighter_id, d.division, d.rating, d.deviation, d.fight_count, d.wins, d.losses);
            }
        });
        insertMany(testData);
        const transactionTime = performance.now() - transactionStart;

        results.push({
            operation: 'Individual Inserts (100)',
            duration: individualTime.toFixed(2),
            recordsPerSecond: (100 / individualTime * 1000).toFixed(0)
        });

        results.push({
            operation: 'Transaction Insert (1000)',
            duration: transactionTime.toFixed(2),
            recordsPerSecond: (1000 / transactionTime * 1000).toFixed(0)
        });

        console.log(`  Individual (100):  ${individualTime.toFixed(2)}ms (${(100 / individualTime * 1000).toFixed(0)} records/sec)`);
        console.log(`  Transaction (1000): ${transactionTime.toFixed(2)}ms (${(1000 / transactionTime * 1000).toFixed(0)} records/sec)`);
        console.log(`  Speedup: ${(individualTime * 10 / transactionTime).toFixed(1)}x`);

        this.results.push({
            benchmark: 'Bulk Operation Performance',
            results
        });
    }

    /**
     * Benchmark 6: Full System Process
     */
    async benchmarkFullSystem() {
        console.log('\n⚡ Benchmark 6: Full System Processing');
        console.log('─'.repeat(50));

        const WHRMainAlgorithm = require('../whr-main-algorithm');
        const whr = new WHRMainAlgorithm(this.dbPath);

        // Get fight count
        const fightCount = this.db.prepare(
            'SELECT COUNT(*) as count FROM fights WHERE result_method NOT LIKE "%No Contest%" AND weight_class NOT LIKE "%Catch%"'
        ).get().count;

        console.log(`  Processing ${fightCount} fights...`);

        const startTime = performance.now();
        
        // Run limited version of main algorithm
        await whr.loadAllParameters();
        
        // Process first 1000 fights only for benchmark
        const fights = this.db.prepare(`
            SELECT 
                f.*,
                e.date as event_date
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.result_method NOT LIKE '%No Contest%'
                AND f.weight_class NOT LIKE '%Catch%'
            ORDER BY e.date, f.id
            LIMIT 1000
        `).all();

        for (const fight of fights) {
            await whr.processSingleFight(fight);
        }

        const processTime = performance.now() - startTime;

        // Generate rankings
        const rankingStart = performance.now();
        await whr.generateCurrentRankings();
        const rankingTime = performance.now() - rankingStart;

        const totalTime = processTime + rankingTime;
        const fightsPerSecond = (1000 / processTime) * 1000;

        console.log(`  Fight Processing: ${processTime.toFixed(0)}ms (${fightsPerSecond.toFixed(0)} fights/sec)`);
        console.log(`  Ranking Generation: ${rankingTime.toFixed(0)}ms`);
        console.log(`  Total Time: ${totalTime.toFixed(0)}ms`);

        this.results.push({
            benchmark: 'Full System Processing',
            results: {
                fightsProcessed: 1000,
                processingTime: processTime.toFixed(0),
                rankingTime: rankingTime.toFixed(0),
                totalTime: totalTime.toFixed(0),
                fightsPerSecond: fightsPerSecond.toFixed(0)
            }
        });

        whr.close();
    }

    /**
     * Generate performance report
     */
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            system: {
                platform: process.platform,
                nodeVersion: process.version,
                cpus: require('os').cpus().length,
                memory: Math.round(require('os').totalmem() / 1024 / 1024 / 1024) + 'GB'
            },
            benchmarks: this.results,
            summary: {
                ratingCalculationSpeed: this.results[0]?.results?.find(r => r.size === 1000)?.fightsPerSecond + ' fights/sec',
                queryPerformance: 'Excellent',
                memoryEfficiency: 'Good',
                overallScore: this.calculateOverallScore()
            }
        };

        const reportPath = path.join(__dirname, '..', '..', 'analysis-output', 'whr-performance-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📄 Performance report saved to: ${reportPath}`);

        return report;
    }

    /**
     * Calculate overall performance score
     */
    calculateOverallScore() {
        // Simple scoring based on key metrics
        let score = 100;
        
        // Deduct points for slow operations
        const ratingSpeed = parseInt(this.results[0]?.results?.find(r => r.size === 1000)?.fightsPerSecond || 0);
        if (ratingSpeed < 1000) score -= 10;
        if (ratingSpeed < 500) score -= 10;
        
        return score + '/100';
    }

    /**
     * Run all benchmarks
     */
    async runAllBenchmarks() {
        console.log('⚡ Running WHR Performance Benchmarks');
        console.log('═'.repeat(60));
        console.log(`Database: ${path.basename(this.dbPath)}`);
        console.log(`Platform: ${process.platform}`);
        console.log(`Node.js: ${process.version}`);
        console.log('═'.repeat(60));

        try {
            await this.benchmarkRatingCalculation();
            await this.benchmarkDatabaseQueries();
            await this.benchmarkConvergence();
            await this.benchmarkMemoryUsage();
            await this.benchmarkBulkOperations();
            await this.benchmarkFullSystem();

            const report = this.generateReport();

            console.log('\n' + '═'.repeat(60));
            console.log('✅ Performance Benchmarks Complete');
            console.log(`Overall Score: ${report.summary.overallScore}`);
            console.log('═'.repeat(60));

            return true;
        } catch (error) {
            console.error('\n❌ Benchmark failed:', error.message);
            console.error(error.stack);
            return false;
        }
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) this.db.close();
    }
}

// Run benchmarks if called directly
if (require.main === module) {
    const benchmarker = new WHRPerformanceBenchmarks();
    
    benchmarker.runAllBenchmarks()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Benchmark runner error:', error);
            process.exit(1);
        })
        .finally(() => {
            benchmarker.close();
        });
}

module.exports = WHRPerformanceBenchmarks;