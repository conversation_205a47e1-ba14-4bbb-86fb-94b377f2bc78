const Database = require('better-sqlite3');
const path = require('path');

/**
 * Investigate Critical Issues Found in Audit
 */

class IssueInvestigator {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
    }

    /**
     * Investigate win/loss count mismatches
     */
    investigateRecordMismatches() {
        console.log('🔍 Investigating Win/Loss Count Mismatches');
        console.log('─'.repeat(50));

        // Find specific fighters with mismatches
        const mismatches = this.db.prepare(`
            WITH calculated_records AS (
                SELECT 
                    wr.fighter_id,
                    wr.division,
                    wr.win_count as stored_wins,
                    wr.loss_count as stored_losses,
                    wr.fight_count as stored_fights,
                    COUNT(CASE 
                        WHEN (fh.fighter1_id = wr.fighter_id AND fh.actual_outcome = 1) OR
                             (fh.fighter2_id = wr.fighter_id AND fh.actual_outcome = 0)
                        THEN 1 
                    END) as calculated_wins,
                    COUNT(CASE 
                        WHEN (fh.fighter1_id = wr.fighter_id AND fh.actual_outcome = 0) OR
                             (fh.fighter2_id = wr.fighter_id AND fh.actual_outcome = 1)
                        THEN 1 
                    END) as calculated_losses,
                    COUNT(fh.fight_id) as total_fights_history
                FROM whr_ratings wr
                LEFT JOIN whr_fight_history fh ON 
                    (fh.fighter1_id = wr.fighter_id OR fh.fighter2_id = wr.fighter_id) 
                    AND fh.division = wr.division
                GROUP BY wr.fighter_id, wr.division
            )
            SELECT 
                cr.*,
                f.first_name,
                f.last_name
            FROM calculated_records cr
            JOIN fighters f ON cr.fighter_id = f.id
            WHERE cr.stored_wins != cr.calculated_wins 
                OR cr.stored_losses != cr.calculated_losses
                OR cr.stored_fights != cr.total_fights_history
            LIMIT 10
        `).all();

        console.log('Found mismatches:');
        mismatches.forEach(mismatch => {
            console.log(`\n${mismatch.first_name} ${mismatch.last_name} (${mismatch.division}):`);
            console.log(`  Stored: ${mismatch.stored_wins}-${mismatch.stored_losses} (${mismatch.stored_fights} fights)`);
            console.log(`  Calculated: ${mismatch.calculated_wins}-${mismatch.calculated_losses} (${mismatch.total_fights_history} fights)`);
        });

        return mismatches;
    }

    /**
     * Investigate chronological processing violations
     */
    investigateChronologicalViolations() {
        console.log('\n🔍 Investigating Chronological Processing Violations');
        console.log('─'.repeat(50));

        // Sample some violations to understand the pattern
        const violations = this.db.prepare(`
            WITH fight_dates AS (
                SELECT 
                    fh.fighter1_id as fighter_id,
                    fh.division,
                    fh.fight_id,
                    e.date,
                    fh.calculation_timestamp,
                    ROW_NUMBER() OVER (PARTITION BY fh.fighter1_id, fh.division ORDER BY e.date) as date_sequence,
                    ROW_NUMBER() OVER (PARTITION BY fh.fighter1_id, fh.division ORDER BY fh.calculation_timestamp) as calc_sequence
                FROM whr_fight_history fh
                JOIN fights f ON fh.fight_id = f.id
                JOIN events e ON f.event_id = e.id
                
                UNION ALL
                
                SELECT 
                    fh.fighter2_id as fighter_id,
                    fh.division,
                    fh.fight_id,
                    e.date,
                    fh.calculation_timestamp,
                    ROW_NUMBER() OVER (PARTITION BY fh.fighter2_id, fh.division ORDER BY e.date) as date_sequence,
                    ROW_NUMBER() OVER (PARTITION BY fh.fighter2_id, fh.division ORDER BY fh.calculation_timestamp) as calc_sequence
                FROM whr_fight_history fh
                JOIN fights f ON fh.fight_id = f.id
                JOIN events e ON f.event_id = e.id
            )
            SELECT 
                fd.*,
                f.first_name,
                f.last_name
            FROM fight_dates fd
            JOIN fighters f ON fd.fighter_id = f.id
            WHERE fd.date_sequence != fd.calc_sequence
            LIMIT 10
        `).all();

        console.log('Sample chronological violations:');
        violations.forEach(violation => {
            console.log(`\n${violation.first_name} ${violation.last_name} (${violation.division}):`);
            console.log(`  Fight Date: ${violation.date}`);
            console.log(`  Calculation Time: ${violation.calculation_timestamp}`);
            console.log(`  Date Sequence: ${violation.date_sequence}, Calc Sequence: ${violation.calc_sequence}`);
        });

        return violations;
    }

    /**
     * Check if fights are missing from history
     */
    checkMissingFights() {
        console.log('\n🔍 Checking for Missing Fights in History');
        console.log('─'.repeat(50));

        const missingFights = this.db.prepare(`
            SELECT 
                f.id as fight_id,
                e.date,
                e.event_name,
                f.weight_class,
                f1.first_name || ' ' || f1.last_name as fighter1,
                f2.first_name || ' ' || f2.last_name as fighter2
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE f.result_method NOT LIKE '%No Contest%'
                AND f.weight_class NOT LIKE '%Catch%'
                AND f.fight_status = 'valid'
                AND NOT EXISTS (
                    SELECT 1 FROM whr_fight_history fh 
                    WHERE fh.fight_id = f.id
                )
            ORDER BY e.date DESC
            LIMIT 10
        `).all();

        console.log(`Found ${missingFights.length} fights not in WHR history:`);
        missingFights.forEach(fight => {
            console.log(`  ${fight.fighter1} vs ${fight.fighter2} (${fight.weight_class})`);
            console.log(`    ${fight.event_name} - ${fight.date}`);
        });

        return missingFights;
    }

    /**
     * Analyze Catch Weight division issues
     */
    analyzeCatchWeightIssues() {
        console.log('\n🔍 Analyzing Catch Weight Division Issues');
        console.log('─'.repeat(50));

        const catchWeightData = this.db.prepare(`
            SELECT 
                COUNT(*) as fighter_count,
                AVG(rating) as avg_rating,
                MIN(rating) as min_rating,
                MAX(rating) as max_rating,
                AVG(fight_count) as avg_fights
            FROM whr_ratings
            WHERE division = 'Catch Weight'
        `).get();

        console.log('Catch Weight statistics:');
        console.log(`  Fighters: ${catchWeightData.fighter_count}`);
        console.log(`  Avg Rating: ${catchWeightData.avg_rating?.toFixed(1) || 'N/A'}`);
        console.log(`  Rating Range: ${catchWeightData.min_rating?.toFixed(1) || 'N/A'} - ${catchWeightData.max_rating?.toFixed(1) || 'N/A'}`);
        console.log(`  Avg Fights: ${catchWeightData.avg_fights?.toFixed(1) || 'N/A'}`);

        // Check if Catch Weight should be excluded
        const catchWeightFights = this.db.prepare(`
            SELECT COUNT(*) as fight_count
            FROM whr_fight_history
            WHERE division = 'Catch Weight'
        `).get().fight_count;

        console.log(`  Fights in history: ${catchWeightFights}`);

        if (catchWeightData.fighter_count < 5 || catchWeightFights < 10) {
            console.log('  💡 Recommendation: Consider excluding Catch Weight from WHR calculations');
        }

        return catchWeightData;
    }

    /**
     * Check temporal accuracy implementation
     */
    checkTemporalAccuracy() {
        console.log('\n🔍 Checking Temporal Accuracy Implementation');
        console.log('─'.repeat(50));

        // Check if fights are processed in date order
        const temporalCheck = this.db.prepare(`
            SELECT 
                COUNT(*) as total_fights,
                COUNT(CASE WHEN prev_date <= current_date THEN 1 END) as chronological_fights
            FROM (
                SELECT 
                    fh.fight_id,
                    e.date as current_date,
                    LAG(e.date) OVER (ORDER BY fh.calculation_timestamp) as prev_date
                FROM whr_fight_history fh
                JOIN fights f ON fh.fight_id = f.id
                JOIN events e ON f.event_id = e.id
                ORDER BY fh.calculation_timestamp
            ) ordered_fights
            WHERE prev_date IS NOT NULL
        `).get();

        const chronologicalPercentage = (temporalCheck.chronological_fights / temporalCheck.total_fights) * 100;
        
        console.log(`Temporal accuracy: ${chronologicalPercentage.toFixed(1)}%`);
        console.log(`  Chronological: ${temporalCheck.chronological_fights}/${temporalCheck.total_fights}`);

        if (chronologicalPercentage < 95) {
            console.log('  ⚠️  Major temporal accuracy violation detected!');
        }

        return temporalCheck;
    }

    /**
     * Run all investigations
     */
    async runAllInvestigations() {
        console.log('🔍 Investigating Critical Issues from Audit');
        console.log('═'.repeat(60));

        const recordMismatches = this.investigateRecordMismatches();
        const chronoViolations = this.investigateChronologicalViolations();
        const missingFights = this.checkMissingFights();
        const catchWeightIssues = this.analyzeCatchWeightIssues();
        const temporalAccuracy = this.checkTemporalAccuracy();

        console.log('\n' + '═'.repeat(60));
        console.log('📋 INVESTIGATION SUMMARY');
        console.log('═'.repeat(60));

        console.log(`\n🔢 Record Mismatches: ${recordMismatches.length} fighters affected`);
        if (recordMismatches.length > 0) {
            console.log('   💡 Likely cause: Fight outcome calculation logic issue');
            console.log('   🔧 Fix: Recalculate win/loss counts from fight history');
        }

        console.log(`\n⏰ Chronological Violations: Found in fight processing`);
        console.log(`   Temporal Accuracy: ${temporalAccuracy ? (temporalAccuracy.chronological_fights / temporalAccuracy.total_fights * 100).toFixed(1) : 'N/A'}%`);
        if (temporalAccuracy && (temporalAccuracy.chronological_fights / temporalAccuracy.total_fights) < 0.95) {
            console.log('   💡 Likely cause: calculation_timestamp not following event date order');
            console.log('   🔧 Fix: Reprocess fights in strict chronological order');
        }

        console.log(`\n❌ Missing Fights: ${missingFights.length} fights not in WHR history`);
        if (missingFights.length > 0) {
            console.log('   💡 Likely cause: Recent fights not processed yet');
            console.log('   🔧 Fix: Run update-whr-after-event.js for recent events');
        }

        console.log(`\n⚖️  Catch Weight Issues: Low prediction accuracy (7.1%)`);
        console.log('   💡 Likely cause: Too few fights for reliable ratings');
        console.log('   🔧 Fix: Consider excluding Catch Weight from WHR calculations');

        return {
            recordMismatches,
            chronoViolations,
            missingFights,
            catchWeightIssues,
            temporalAccuracy
        };
    }

    close() {
        if (this.db) this.db.close();
    }
}

// Run investigation if called directly
if (require.main === module) {
    const investigator = new IssueInvestigator();
    
    investigator.runAllInvestigations()
        .then(() => {
            console.log('\n✅ Investigation completed!');
        })
        .catch(error => {
            console.error('❌ Investigation failed:', error);
        })
        .finally(() => {
            investigator.close();
        });
}

module.exports = IssueInvestigator;