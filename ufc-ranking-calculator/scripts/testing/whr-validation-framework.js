const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

/**
 * WHR Validation Framework
 * 
 * Tests the WHR system against historical outcomes to measure predictive accuracy
 * and validates that the system produces sensible rankings
 */

class WHRValidationFramework {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
    }

    /**
     * Test 1: Historical Prediction Accuracy
     * Measures how well WHR ratings predict fight outcomes
     */
    async testPredictionAccuracy() {
        console.log('\n📊 Historical Prediction Accuracy Test');
        console.log('─'.repeat(50));

        const results = this.db.prepare(`
            SELECT 
                fh.division,
                COUNT(*) as total_fights,
                SUM(CASE 
                    WHEN (fh.expected_outcome > 0.5 AND fh.actual_outcome = 1) OR
                         (fh.expected_outcome < 0.5 AND fh.actual_outcome = 0) OR
                         (fh.expected_outcome = 0.5 AND fh.actual_outcome = 0.5)
                    THEN 1 ELSE 0 
                END) as correct_predictions,
                AVG(CASE 
                    WHEN fh.actual_outcome = 1 THEN fh.expected_outcome
                    WHEN fh.actual_outcome = 0 THEN 1 - fh.expected_outcome
                    ELSE 0.5
                END) as avg_confidence_when_correct,
                AVG(ABS(fh.expected_outcome - fh.actual_outcome)) as avg_prediction_error,
                AVG(fh.surprise_factor) as avg_surprise_factor
            FROM whr_fight_history fh
            GROUP BY fh.division
            ORDER BY total_fights DESC
        `).all();

        let totalFights = 0;
        let totalCorrect = 0;

        console.log('Division               | Fights | Accuracy | Avg Error | Confidence');
        console.log('─'.repeat(70));
        
        for (const div of results) {
            const accuracy = (div.correct_predictions / div.total_fights * 100).toFixed(1);
            totalFights += div.total_fights;
            totalCorrect += div.correct_predictions;
            
            console.log(
                `${div.division.padEnd(22)} | ${div.total_fights.toString().padStart(6)} | ` +
                `${accuracy.padStart(7)}% | ${div.avg_prediction_error.toFixed(3).padStart(9)} | ` +
                `${div.avg_confidence_when_correct.toFixed(3).padStart(10)}`
            );
        }

        const overallAccuracy = (totalCorrect / totalFights * 100).toFixed(1);
        console.log('─'.repeat(70));
        console.log(`Overall                | ${totalFights.toString().padStart(6)} | ${overallAccuracy.padStart(7)}%`);

        // Validate accuracy is reasonable (should be > 60% for a good system)
        const passed = parseFloat(overallAccuracy) > 60;
        console.log(`\n${passed ? '✅' : '❌'} Overall prediction accuracy: ${overallAccuracy}% (threshold: 60%)`);

        return { passed, accuracy: overallAccuracy };
    }

    /**
     * Test 2: Upset Detection
     * Validates that the system properly identifies upsets
     */
    async testUpsetDetection() {
        console.log('\n📊 Upset Detection Test');
        console.log('─'.repeat(50));

        const upsets = this.db.prepare(`
            SELECT 
                fh.division,
                COUNT(*) as total_upsets,
                AVG(fh.surprise_factor) as avg_surprise_factor,
                MAX(fh.surprise_factor) as max_surprise_factor,
                AVG(ABS(fh.rating_change_fighter1)) as avg_rating_change
            FROM whr_fight_history fh
            WHERE fh.surprise_factor > 0.3  -- Significant upset threshold
            GROUP BY fh.division
            ORDER BY total_upsets DESC
        `).all();

        console.log('Division               | Upsets | Avg Surprise | Max Surprise | Avg Change');
        console.log('─'.repeat(75));

        for (const div of upsets) {
            console.log(
                `${div.division.padEnd(22)} | ${div.total_upsets.toString().padStart(6)} | ` +
                `${div.avg_surprise_factor.toFixed(3).padStart(12)} | ` +
                `${div.max_surprise_factor.toFixed(3).padStart(12)} | ` +
                `${div.avg_rating_change.toFixed(1).padStart(10)}`
            );
        }

        // Get some notable upsets
        const notableUpsets = this.db.prepare(`
            SELECT 
                f1.first_name || ' ' || f1.last_name as fighter1_name,
                f2.first_name || ' ' || f2.last_name as fighter2_name,
                fh.fighter1_pre_rating,
                fh.fighter2_pre_rating,
                fh.expected_outcome,
                fh.actual_outcome,
                fh.surprise_factor,
                e.event_name,
                e.date
            FROM whr_fight_history fh
            JOIN fighters f1 ON fh.fighter1_id = f1.id
            JOIN fighters f2 ON fh.fighter2_id = f2.id
            JOIN fights f ON fh.fight_id = f.id
            JOIN events e ON f.event_id = e.id
            WHERE fh.surprise_factor > 0.4
            ORDER BY fh.surprise_factor DESC
            LIMIT 5
        `).all();

        console.log('\n🎯 Top 5 Most Surprising Results:');
        notableUpsets.forEach((upset, i) => {
            const winner = upset.actual_outcome > 0.5 ? upset.fighter1_name : upset.fighter2_name;
            const loser = upset.actual_outcome > 0.5 ? upset.fighter2_name : upset.fighter1_name;
            console.log(`${i + 1}. ${winner} defeated ${loser} (${upset.surprise_factor.toFixed(3)} surprise)`);
            console.log(`   Ratings: ${upset.fighter1_pre_rating.toFixed(0)} vs ${upset.fighter2_pre_rating.toFixed(0)}, Expected: ${upset.expected_outcome.toFixed(3)}`);
            console.log(`   ${upset.event_name} (${upset.date})`);
        });

        return { passed: true };
    }

    /**
     * Test 3: Rating Distribution
     * Validates that ratings follow a reasonable distribution
     */
    async testRatingDistribution() {
        console.log('\n📊 Rating Distribution Test');
        console.log('─'.repeat(50));

        const distributions = this.db.prepare(`
            SELECT 
                division,
                COUNT(*) as fighter_count,
                AVG(rating) as avg_rating,
                MIN(rating) as min_rating,
                MAX(rating) as max_rating,
                COUNT(CASE WHEN rating > 1600 THEN 1 END) as elite_count,
                COUNT(CASE WHEN rating < 1400 THEN 1 END) as low_count
            FROM whr_ratings
            WHERE fight_count >= 3  -- Only active fighters
            GROUP BY division
            ORDER BY fighter_count DESC
        `).all();

        // Calculate standard deviation manually
        for (const div of distributions) {
            const ratings = this.db.prepare(`
                SELECT rating FROM whr_ratings 
                WHERE division = ? AND fight_count >= 3
            `).all(div.division);
            
            const mean = div.avg_rating;
            const variance = ratings.reduce((sum, r) => sum + Math.pow(r.rating - mean, 2), 0) / ratings.length;
            div.std_dev = Math.sqrt(variance);
        }

        console.log('Division               | Count | Avg Rating | Min  | Max  | StdDev | Elite | Low');
        console.log('─'.repeat(85));

        let allPassed = true;
        for (const div of distributions) {
            // Validate distribution characteristics
            const avgInRange = div.avg_rating >= 1480 && div.avg_rating <= 1520;
            const spreadReasonable = div.std_dev >= 50 && div.std_dev <= 150;
            const elitePercentage = (div.elite_count / div.fighter_count) * 100;
            const lowPercentage = (div.low_count / div.fighter_count) * 100;
            
            const passed = avgInRange && spreadReasonable && elitePercentage < 20 && lowPercentage < 20;
            allPassed = allPassed && passed;

            console.log(
                `${passed ? '✅' : '❌'} ${div.division.padEnd(20)} | ${div.fighter_count.toString().padStart(5)} | ` +
                `${div.avg_rating.toFixed(0).padStart(10)} | ${div.min_rating.toFixed(0).padStart(4)} | ` +
                `${div.max_rating.toFixed(0).padStart(4)} | ${div.std_dev.toFixed(0).padStart(6)} | ` +
                `${div.elite_count.toString().padStart(5)} | ${div.low_count.toString().padStart(3)}`
            );
        }

        return { passed: allPassed };
    }

    /**
     * Test 4: Temporal Consistency
     * Validates that ratings change smoothly over time
     */
    async testTemporalConsistency() {
        console.log('\n📊 Temporal Consistency Test');
        console.log('─'.repeat(50));

        // Check for unrealistic rating jumps
        const largeChanges = this.db.prepare(`
            SELECT 
                f1.first_name || ' ' || f1.last_name as fighter_name,
                fh.division,
                fh.fighter1_pre_rating,
                fh.fighter1_post_rating,
                fh.rating_change_fighter1 as rating_change,
                fh.expected_outcome,
                fh.actual_outcome,
                e.date,
                e.event_name
            FROM whr_fight_history fh
            JOIN fighters f1 ON fh.fighter1_id = f1.id
            JOIN fights f ON fh.fight_id = f.id
            JOIN events e ON f.event_id = e.id
            WHERE ABS(fh.rating_change_fighter1) > 50
            ORDER BY ABS(fh.rating_change_fighter1) DESC
            LIMIT 10
        `).all();

        if (largeChanges.length > 0) {
            console.log('⚠️  Large Rating Changes Detected:');
            console.log('Fighter                | Division         | Pre  | Post | Change | Expected | Actual');
            console.log('─'.repeat(85));
            
            largeChanges.forEach(change => {
                console.log(
                    `${change.fighter_name.padEnd(22)} | ${change.division.padEnd(16)} | ` +
                    `${change.fighter1_pre_rating.toFixed(0).padStart(4)} | ${change.fighter1_post_rating.toFixed(0).padStart(4)} | ` +
                    `${change.rating_change > 0 ? '+' : ''}${change.rating_change.toFixed(1).padStart(6)} | ` +
                    `${change.expected_outcome.toFixed(3).padStart(8)} | ${change.actual_outcome.toFixed(1).padStart(6)}`
                );
            });
        }

        // Check rating volatility over careers
        const volatility = this.db.prepare(`
            SELECT 
                AVG(avg_change) as overall_avg_change,
                MAX(avg_change) as max_avg_change,
                COUNT(CASE WHEN avg_change > 20 THEN 1 END) as high_volatility_count
            FROM (
                SELECT 
                    fighter_id,
                    AVG(ABS(rating_change)) as avg_change
                FROM (
                    SELECT fighter1_id as fighter_id, rating_change_fighter1 as rating_change
                    FROM whr_fight_history
                    UNION ALL
                    SELECT fighter2_id as fighter_id, rating_change_fighter2 as rating_change
                    FROM whr_fight_history
                )
                GROUP BY fighter_id
                HAVING COUNT(*) >= 5
            )
        `).get();

        console.log('\n📈 Rating Volatility Analysis:');
        console.log(`Average rating change per fight: ${volatility.overall_avg_change.toFixed(1)}`);
        console.log(`Maximum average change: ${volatility.max_avg_change.toFixed(1)}`);
        console.log(`Fighters with high volatility (>20 pts/fight): ${volatility.high_volatility_count}`);

        const passed = volatility.overall_avg_change < 15 && volatility.high_volatility_count < 10;
        console.log(`\n${passed ? '✅' : '❌'} Temporal consistency check: ${passed ? 'PASSED' : 'FAILED'}`);

        return { passed };
    }

    /**
     * Test 5: Cross-Division Validation
     * Validates fighters who compete in multiple divisions
     */
    async testCrossDivisionConsistency() {
        console.log('\n📊 Cross-Division Consistency Test');
        console.log('─'.repeat(50));

        const multiDivisionFighters = this.db.prepare(`
            SELECT 
                f.id,
                f.first_name || ' ' || f.last_name as fighter_name,
                COUNT(DISTINCT wr.division) as division_count,
                GROUP_CONCAT(wr.division || ':' || ROUND(wr.rating)) as ratings
            FROM whr_ratings wr
            JOIN fighters f ON wr.fighter_id = f.id
            GROUP BY f.id
            HAVING division_count > 1
            ORDER BY division_count DESC, fighter_name
            LIMIT 20
        `).all();

        console.log('Fighter                | Divisions | Ratings');
        console.log('─'.repeat(70));

        multiDivisionFighters.forEach(fighter => {
            const ratings = fighter.ratings.split(',').map(r => {
                const [div, rating] = r.split(':');
                return `${div.split(' ').pop()}(${rating})`;
            }).join(', ');
            
            console.log(
                `${fighter.fighter_name.padEnd(22)} | ${fighter.division_count.toString().padCenter(9)} | ${ratings}`
            );
        });

        return { passed: true };
    }

    /**
     * Test 6: Championship Validation
     * Validates that champions are highly rated
     */
    async testChampionshipValidation() {
        console.log('\n📊 Championship Validation Test');
        console.log('─'.repeat(50));

        // Get title fights and check if winners are highly rated
        const titleFights = this.db.prepare(`
            SELECT 
                f.weight_class as division,
                f1.first_name || ' ' || f1.last_name as winner_name,
                fh.fighter1_pre_rating as winner_rating,
                dr.rank as winner_rank,
                e.date,
                e.event_name
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.winner_id = f1.id
            JOIN whr_fight_history fh ON f.id = fh.fight_id AND 
                ((fh.fighter1_id = f.winner_id AND fh.actual_outcome = 1) OR 
                 (fh.fighter2_id = f.winner_id AND fh.actual_outcome = 0))
            LEFT JOIN whr_division_rankings dr ON 
                dr.fighter_id = f.winner_id AND 
                dr.division = f.weight_class
            WHERE f.weight_class LIKE '%Title%'
            ORDER BY e.date DESC
            LIMIT 20
        `).all();

        console.log('Division               | Champion              | Rating | Rank | Date       | Event');
        console.log('─'.repeat(90));

        let validChampions = 0;
        titleFights.forEach(fight => {
            const isValid = fight.winner_rank <= 5 || fight.winner_rating > 1550;
            validChampions += isValid ? 1 : 0;
            
            console.log(
                `${isValid ? '✅' : '❌'} ${fight.division.substring(0, 20).padEnd(20)} | ` +
                `${fight.winner_name.padEnd(20)} | ${fight.winner_rating.toFixed(0).padStart(6)} | ` +
                `${(fight.winner_rank || '-').toString().padStart(4)} | ${fight.date.substring(0, 10)} | ` +
                `${fight.event_name.substring(0, 20)}`
            );
        });

        const validationRate = (validChampions / titleFights.length) * 100;
        const passed = validationRate >= 80;
        
        console.log(`\n${passed ? '✅' : '❌'} Championship validation: ${validationRate.toFixed(1)}% valid (threshold: 80%)`);

        return { passed };
    }

    /**
     * Run all validation tests
     */
    async runAllValidations() {
        console.log('🧪 Running WHR Validation Framework');
        console.log('═'.repeat(60));

        const results = [];
        
        try {
            results.push(await this.testPredictionAccuracy());
            results.push(await this.testUpsetDetection());
            results.push(await this.testRatingDistribution());
            results.push(await this.testTemporalConsistency());
            results.push(await this.testCrossDivisionConsistency());
            results.push(await this.testChampionshipValidation());

            const allPassed = results.every(r => r.passed);
            
            console.log('\n' + '═'.repeat(60));
            console.log(`${allPassed ? '✅' : '❌'} Overall Validation: ${allPassed ? 'PASSED' : 'FAILED'}`);
            console.log('═'.repeat(60));

            // Generate validation report
            await this.generateValidationReport(results);

            return allPassed;
        } catch (error) {
            console.error('\n❌ Validation failed:', error.message);
            console.error(error.stack);
            return false;
        }
    }

    /**
     * Generate validation report
     */
    async generateValidationReport(results) {
        const report = {
            timestamp: new Date().toISOString(),
            summary: {
                totalTests: results.length,
                passed: results.filter(r => r.passed).length,
                failed: results.filter(r => !r.passed).length
            },
            details: results,
            systemHealth: {
                predictionAccuracy: results[0]?.accuracy || 'N/A',
                ratingDistribution: 'Normal',
                temporalConsistency: results[3]?.passed ? 'Stable' : 'Unstable',
                championshipAlignment: results[5]?.passed ? 'Valid' : 'Invalid'
            }
        };

        const reportPath = path.join(__dirname, '..', '..', 'analysis-output', 'whr-validation-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\n📄 Validation report saved to: ${reportPath}`);
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) this.db.close();
    }
}

// String padding helper
String.prototype.padCenter = function(length) {
    if (this.length >= length) return this;
    const leftPad = Math.floor((length - this.length) / 2);
    const rightPad = length - this.length - leftPad;
    return ' '.repeat(leftPad) + this + ' '.repeat(rightPad);
};

// Run validation if called directly
if (require.main === module) {
    const validator = new WHRValidationFramework();
    
    validator.runAllValidations()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Validation runner error:', error);
            process.exit(1);
        })
        .finally(() => {
            validator.close();
        });
}

module.exports = WHRValidationFramework;