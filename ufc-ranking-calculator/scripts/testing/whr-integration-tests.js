const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const assert = require('assert');

// Import all WHR components
const TemporalAccuracy = require('../temporal-accuracy-infrastructure');
const DivisionParameters = require('../division-specific-base-parameters');
const IterativeConvergence = require('../iterative-convergence-system');
const StatisticalWeights = require('../division-specific-statistical-weights');
const TimeDecay = require('../time-decay-optimization');
const FinishingImpact = require('../division-specific-finishing-impact');
const WHRMainAlgorithm = require('../whr-main-algorithm');

/**
 * WHR Integration Tests
 * 
 * End-to-end tests that verify the entire WHR system works correctly
 */

class WHRIntegrationTests {
    constructor() {
        // Create test database
        this.testDbPath = path.join(__dirname, 'test_integration.db');
        this.setupTestDatabase();
    }

    setupTestDatabase() {
        // Remove existing test database
        if (fs.existsSync(this.testDbPath)) {
            fs.unlinkSync(this.testDbPath);
        }

        // Copy production database structure
        const prodDb = new Database(path.join(__dirname, '..', '..', 'data', 'ufc_data.db'));
        this.db = new Database(this.testDbPath);

        // Copy schema
        const schema = prodDb.prepare("SELECT sql FROM sqlite_master WHERE type='table'").all();
        schema.forEach(table => {
            if (table.sql && !table.sql.includes('sqlite_')) {
                this.db.exec(table.sql);
            }
        });

        prodDb.close();

        // Create test data
        this.createTestData();
    }

    createTestData() {
        // Create test fighters
        const fighters = [
            { id: 1, first_name: 'Test', last_name: 'Champion', birthdate: '1990-01-01' },
            { id: 2, first_name: 'Test', last_name: 'Contender', birthdate: '1992-01-01' },
            { id: 3, first_name: 'Test', last_name: 'Gatekeeper', birthdate: '1988-01-01' },
            { id: 4, first_name: 'Test', last_name: 'Prospect', birthdate: '1995-01-01' },
            { id: 5, first_name: 'Test', last_name: 'Veteran', birthdate: '1985-01-01' },
            { id: 6, first_name: 'Test', last_name: 'Rookie', birthdate: '1998-01-01' }
        ];

        const fighterStmt = this.db.prepare(
            'INSERT INTO fighters (id, first_name, last_name, birthdate) VALUES (?, ?, ?, ?)'
        );

        fighters.forEach(f => fighterStmt.run(f.id, f.first_name, f.last_name, f.birthdate));

        // Create test events
        const events = [
            { id: 1, date: '2023-01-01', event_name: 'Test Event 1' },
            { id: 2, date: '2023-03-01', event_name: 'Test Event 2' },
            { id: 3, date: '2023-05-01', event_name: 'Test Event 3' },
            { id: 4, date: '2023-07-01', event_name: 'Test Event 4' },
            { id: 5, date: '2023-09-01', event_name: 'Test Event 5' }
        ];

        const eventStmt = this.db.prepare(
            'INSERT INTO events (id, date, event_name) VALUES (?, ?, ?)'
        );

        events.forEach(e => eventStmt.run(e.id, e.date, e.event_name));

        // Create test fights with expected rating progression
        const fights = [
            // Event 1
            { id: 1, event_id: 1, fighter1_id: 1, fighter2_id: 2, winner_id: 1, 
              result_method: 'Decision - Unanimous', weight_class: 'Lightweight', fight_status: 'valid' },
            { id: 2, event_id: 1, fighter1_id: 3, fighter2_id: 4, winner_id: 3, 
              result_method: 'KO/TKO', weight_class: 'Lightweight', fight_status: 'valid' },
            
            // Event 2
            { id: 3, event_id: 2, fighter1_id: 1, fighter2_id: 3, winner_id: 1, 
              result_method: 'Submission', weight_class: 'Lightweight', fight_status: 'valid' },
            { id: 4, event_id: 2, fighter1_id: 2, fighter2_id: 4, winner_id: 2, 
              result_method: 'Decision - Split', weight_class: 'Lightweight', fight_status: 'valid' },
            
            // Event 3
            { id: 5, event_id: 3, fighter1_id: 5, fighter2_id: 6, winner_id: 5, 
              result_method: 'Decision - Unanimous', weight_class: 'Lightweight', fight_status: 'valid' },
            { id: 6, event_id: 3, fighter1_id: 1, fighter2_id: 5, winner_id: 1, 
              result_method: 'KO/TKO', weight_class: 'Lightweight', fight_status: 'valid' },
            
            // Event 4 - Draw
            { id: 7, event_id: 4, fighter1_id: 2, fighter2_id: 3, winner_id: null, 
              result_method: 'Draw - Split', weight_class: 'Lightweight', fight_status: 'valid' },
            
            // Event 5 - Cross-division
            { id: 8, event_id: 5, fighter1_id: 1, fighter2_id: 2, winner_id: 2, 
              result_method: 'Decision - Unanimous', weight_class: 'Welterweight', fight_status: 'valid' }
        ];

        const fightStmt = this.db.prepare(
            `INSERT INTO fights (id, event_id, fighter1_id, fighter2_id, winner_id, 
             result_method, weight_class, fight_status) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)`
        );

        fights.forEach(f => fightStmt.run(
            f.id, f.event_id, f.fighter1_id, f.fighter2_id, 
            f.winner_id, f.result_method, f.weight_class, f.fight_status
        ));

        // Create test fight stats
        const fightStats = [];
        fights.forEach(fight => {
            // Generate realistic stats for each fighter
            const isWinner1 = fight.winner_id === fight.fighter1_id;
            const isWinner2 = fight.winner_id === fight.fighter2_id;
            
            fightStats.push({
                fight_id: fight.id,
                fighter_id: fight.fighter1_id,
                round: 1,
                sig_strikes_landed: isWinner1 ? 25 : 15,
                sig_strikes_attempted: isWinner1 ? 40 : 35,
                takedowns_landed: isWinner1 ? 2 : 0,
                takedowns_attempted: isWinner1 ? 3 : 1,
                control_time_seconds: isWinner1 ? 120 : 60
            });

            fightStats.push({
                fight_id: fight.id,
                fighter_id: fight.fighter2_id,
                round: 1,
                sig_strikes_landed: isWinner2 ? 25 : 15,
                sig_strikes_attempted: isWinner2 ? 40 : 35,
                takedowns_landed: isWinner2 ? 2 : 0,
                takedowns_attempted: isWinner2 ? 3 : 1,
                control_time_seconds: isWinner2 ? 120 : 60
            });
        });

        const statsStmt = this.db.prepare(
            `INSERT INTO fight_stats (fight_id, fighter_id, round, 
             sig_strikes_landed, sig_strikes_attempted, 
             takedowns_landed, takedowns_attempted, control_time_seconds) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)`
        );

        fightStats.forEach(s => statsStmt.run(
            s.fight_id, s.fighter_id, s.round,
            s.sig_strikes_landed, s.sig_strikes_attempted,
            s.takedowns_landed, s.takedowns_attempted,
            s.control_time_seconds
        ));
    }

    /**
     * Test 1: Full System Initialization
     */
    async testSystemInitialization() {
        console.log('\n📊 Test 1: Full System Initialization');
        
        try {
            // Initialize all components
            const temporal = new TemporalAccuracy(this.testDbPath);
            const divParams = new DivisionParameters(this.testDbPath);
            const convergence = new IterativeConvergence(this.testDbPath);
            const statsWeights = new StatisticalWeights(this.testDbPath);
            const timeDecay = new TimeDecay(this.testDbPath);
            const finishing = new FinishingImpact(this.testDbPath);

            // Run each component
            console.log('  ⏳ Running temporal accuracy...');
            await temporal.run();
            
            console.log('  📊 Calculating division parameters...');
            await divParams.calculateAllDivisions();
            
            console.log('  🔄 Running convergence system...');
            await convergence.run();
            
            console.log('  📈 Calculating statistical weights...');
            await statsWeights.calculateAllDivisions();
            
            console.log('  ⏱️  Optimizing time decay...');
            await timeDecay.optimizeAllDivisions();
            
            console.log('  🎯 Analyzing finishing impact...');
            await finishing.analyzeAllDivisions();

            // Verify tables were created
            const tables = this.db.prepare(
                "SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%parameters%' OR name LIKE '%weights%'"
            ).all();

            assert(tables.length >= 2, 'Parameter tables not created');
            console.log('  ✅ All components initialized successfully');

            // Close connections
            [temporal, divParams, convergence, statsWeights, timeDecay, finishing].forEach(
                component => component.close && component.close()
            );

            return true;
        } catch (error) {
            console.error('  ❌ Initialization failed:', error.message);
            return false;
        }
    }

    /**
     * Test 2: End-to-End Rating Calculation
     */
    async testEndToEndCalculation() {
        console.log('\n📊 Test 2: End-to-End Rating Calculation');
        
        try {
            // Run main algorithm
            const whr = new WHRMainAlgorithm(this.testDbPath);
            await whr.run();

            // Verify ratings were calculated
            const ratings = this.db.prepare(
                'SELECT COUNT(*) as count FROM whr_ratings'
            ).get();

            assert(ratings.count > 0, 'No ratings calculated');
            console.log(`  ✅ Calculated ${ratings.count} fighter ratings`);

            // Verify fight history was recorded
            const history = this.db.prepare(
                'SELECT COUNT(*) as count FROM whr_fight_history'
            ).get();

            assert(history.count > 0, 'No fight history recorded');
            console.log(`  ✅ Recorded ${history.count} fight histories`);

            // Check rating properties
            const ratingCheck = this.db.prepare(`
                SELECT 
                    AVG(rating) as avg_rating,
                    MIN(rating) as min_rating,
                    MAX(rating) as max_rating,
                    AVG(rating_deviation) as avg_deviation
                FROM whr_ratings
            `).get();

            console.log(`  📊 Rating Statistics:`);
            console.log(`     Average: ${ratingCheck.avg_rating.toFixed(1)}`);
            console.log(`     Range: ${ratingCheck.min_rating.toFixed(1)} - ${ratingCheck.max_rating.toFixed(1)}`);
            console.log(`     Avg Deviation: ${ratingCheck.avg_deviation.toFixed(1)}`);

            // Validate reasonable ranges
            assert(ratingCheck.avg_rating >= 1400 && ratingCheck.avg_rating <= 1600, 'Average rating out of range');
            assert(ratingCheck.min_rating >= 1200, 'Minimum rating too low');
            assert(ratingCheck.max_rating <= 1800, 'Maximum rating too high');

            whr.close();
            return true;
        } catch (error) {
            console.error('  ❌ End-to-end calculation failed:', error.message);
            return false;
        }
    }

    /**
     * Test 3: Ranking Generation
     */
    async testRankingGeneration() {
        console.log('\n📊 Test 3: Ranking Generation');
        
        try {
            // Generate rankings
            const whr = new WHRMainAlgorithm(this.testDbPath);
            await whr.generateCurrentRankings();

            // Verify rankings were created
            const rankings = this.db.prepare(`
                SELECT 
                    dr.*,
                    f.first_name || ' ' || f.last_name as fighter_name
                FROM whr_division_rankings dr
                JOIN fighters f ON dr.fighter_id = f.id
                WHERE dr.division = 'Lightweight'
                ORDER BY dr.rank
            `).all();

            assert(rankings.length > 0, 'No rankings generated');
            console.log(`  ✅ Generated ${rankings.length} rankings for Lightweight`);

            // Display rankings
            console.log('\n  📊 Lightweight Rankings:');
            rankings.forEach(r => {
                console.log(`     ${r.rank}. ${r.fighter_name}: ${r.rating.toFixed(1)} (${r.status})`);
            });

            // Verify ranking properties
            let lastRating = Infinity;
            rankings.forEach(r => {
                assert(r.rating <= lastRating, 'Rankings not in correct order');
                lastRating = r.rating;
            });

            whr.close();
            return true;
        } catch (error) {
            console.error('  ❌ Ranking generation failed:', error.message);
            return false;
        }
    }

    /**
     * Test 4: Event Update Process
     */
    async testEventUpdate() {
        console.log('\n📊 Test 4: Event Update Process');
        
        try {
            // Add a new event with fights
            this.db.prepare(
                'INSERT INTO events (id, date, event_name) VALUES (?, ?, ?)'
            ).run(10, '2023-11-01', 'Test Event Update');

            this.db.prepare(
                `INSERT INTO fights (id, event_id, fighter1_id, fighter2_id, winner_id, 
                 result_method, weight_class, fight_status) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`
            ).run(10, 10, 4, 6, 4, 'Decision - Unanimous', 'Lightweight', 'valid');

            // Add fight stats
            this.db.prepare(
                `INSERT INTO fight_stats (fight_id, fighter_id, round, 
                 sig_strikes_landed, sig_strikes_attempted) 
                 VALUES (?, ?, ?, ?, ?)`
            ).run(10, 4, 1, 30, 45);

            this.db.prepare(
                `INSERT INTO fight_stats (fight_id, fighter_id, round, 
                 sig_strikes_landed, sig_strikes_attempted) 
                 VALUES (?, ?, ?, ?, ?)`
            ).run(10, 6, 1, 20, 40);

            // Get ratings before update
            const beforeRatings = this.db.prepare(
                'SELECT fighter_id, rating FROM whr_ratings WHERE fighter_id IN (4, 6) AND division = "Lightweight"'
            ).all();

            // Run update
            const UpdaterClass = require('../update-whr-after-event');
            const updater = new UpdaterClass(this.testDbPath);
            await updater.updateWithNewFights('2023-11-01');

            // Get ratings after update
            const afterRatings = this.db.prepare(
                'SELECT fighter_id, rating FROM whr_ratings WHERE fighter_id IN (4, 6) AND division = "Lightweight"'
            ).all();

            // Verify ratings changed
            console.log('\n  📊 Rating Changes:');
            beforeRatings.forEach(before => {
                const after = afterRatings.find(a => a.fighter_id === before.fighter_id);
                const change = after.rating - before.rating;
                console.log(`     Fighter ${before.fighter_id}: ${before.rating.toFixed(1)} → ${after.rating.toFixed(1)} (${change > 0 ? '+' : ''}${change.toFixed(1)})`);
                
                if (before.fighter_id === 4) {
                    assert(change > 0, 'Winner rating should increase');
                } else {
                    assert(change < 0, 'Loser rating should decrease');
                }
            });

            updater.close();
            return true;
        } catch (error) {
            console.error('  ❌ Event update failed:', error.message);
            return false;
        }
    }

    /**
     * Test 5: API Endpoint Integration
     */
    async testAPIIntegration() {
        console.log('\n📊 Test 5: API Endpoint Integration');
        
        try {
            // Note: This test simulates API behavior without starting a server
            const { query } = require('../../lib/database');
            
            // Test ranking query
            const rankingSQL = `
                SELECT 
                    dr.rank,
                    dr.fighter_id,
                    f.first_name,
                    f.last_name,
                    dr.rating
                FROM whr_division_rankings dr
                JOIN fighters f ON dr.fighter_id = f.id
                WHERE dr.division = 'Lightweight'
                ORDER BY dr.rank
                LIMIT 10
            `;

            // Override query function temporarily
            const originalQuery = global.query;
            global.query = (sql, params) => this.db.prepare(sql).all(...(params || []));

            const rankings = global.query(rankingSQL);
            assert(rankings.length > 0, 'API ranking query failed');
            console.log(`  ✅ API ranking query returned ${rankings.length} results`);

            // Test prediction query
            const fighter1Rating = global.query(
                'SELECT * FROM whr_ratings WHERE fighter_id = ? AND division = ?',
                [1, 'Lightweight']
            )[0];

            assert(fighter1Rating, 'API fighter query failed');
            console.log(`  ✅ API fighter query successful`);

            // Restore original query
            global.query = originalQuery;

            return true;
        } catch (error) {
            console.error('  ❌ API integration failed:', error.message);
            return false;
        }
    }

    /**
     * Test 6: Data Integrity
     */
    async testDataIntegrity() {
        console.log('\n📊 Test 6: Data Integrity');
        
        try {
            // Check for orphaned records
            const orphanedRatings = this.db.prepare(`
                SELECT COUNT(*) as count
                FROM whr_ratings wr
                WHERE NOT EXISTS (SELECT 1 FROM fighters f WHERE f.id = wr.fighter_id)
            `).get();

            assert(orphanedRatings.count === 0, `Found ${orphanedRatings.count} orphaned ratings`);
            console.log('  ✅ No orphaned ratings found');

            // Check for duplicate rankings
            const duplicateRankings = this.db.prepare(`
                SELECT division, rank, COUNT(*) as count
                FROM whr_division_rankings
                GROUP BY division, rank, ranking_date
                HAVING count > 1
            `).all();

            assert(duplicateRankings.length === 0, 'Found duplicate rankings');
            console.log('  ✅ No duplicate rankings found');

            // Check rating consistency
            const inconsistentRatings = this.db.prepare(`
                SELECT COUNT(*) as count
                FROM whr_ratings
                WHERE rating < 1000 OR rating > 2000 OR rating_deviation < 0
            `).get();

            assert(inconsistentRatings.count === 0, 'Found inconsistent ratings');
            console.log('  ✅ All ratings within valid range');

            return true;
        } catch (error) {
            console.error('  ❌ Data integrity check failed:', error.message);
            return false;
        }
    }

    /**
     * Run all integration tests
     */
    async runAllTests() {
        console.log('🧪 Running WHR Integration Tests');
        console.log('═'.repeat(60));

        const results = [];
        
        try {
            results.push(await this.testSystemInitialization());
            results.push(await this.testEndToEndCalculation());
            results.push(await this.testRankingGeneration());
            results.push(await this.testEventUpdate());
            results.push(await this.testAPIIntegration());
            results.push(await this.testDataIntegrity());

            const passed = results.filter(r => r).length;
            const failed = results.length - passed;

            console.log('\n' + '═'.repeat(60));
            console.log(`✅ Passed: ${passed}/${results.length} tests`);
            if (failed > 0) {
                console.log(`❌ Failed: ${failed} tests`);
            }
            console.log('═'.repeat(60));

            return failed === 0;
        } catch (error) {
            console.error('\n❌ Integration test suite failed:', error.message);
            console.error(error.stack);
            return false;
        }
    }

    /**
     * Cleanup
     */
    cleanup() {
        if (this.db) this.db.close();
        if (fs.existsSync(this.testDbPath)) {
            fs.unlinkSync(this.testDbPath);
        }
    }
}

// Run tests if called directly
if (require.main === module) {
    const tester = new WHRIntegrationTests();
    
    tester.runAllTests()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('Integration test runner error:', error);
            process.exit(1);
        })
        .finally(() => {
            tester.cleanup();
        });
}

module.exports = WHRIntegrationTests;