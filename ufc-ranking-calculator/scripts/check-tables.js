const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '..', 'data', 'ufc_data.db');
const db = new Database(dbPath);

console.log('📊 Checking database tables...');

// Get all table names
const tables = db.prepare(`
    SELECT name FROM sqlite_master 
    WHERE type='table' 
    ORDER BY name
`).all();

console.log('\n🗂️  All tables in database:');
tables.forEach(table => {
    console.log(`  - ${table.name}`);
});

// Look for rating-related tables
console.log('\n🎯 Rating-related tables:');
const ratingTables = tables.filter(t => t.name.toLowerCase().includes('rating') || t.name.toLowerCase().includes('whr'));
ratingTables.forEach(table => {
    console.log(`  - ${table.name}`);
    
    // Get schema for this table
    const schema = db.prepare(`PRAGMA table_info(${table.name})`).all();
    schema.forEach(col => {
        console.log(`    ${col.name} (${col.type})`);
    });
    console.log('');
});

db.close();
