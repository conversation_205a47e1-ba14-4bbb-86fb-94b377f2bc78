const Database = require("better-sqlite3");
const path = require("path");

/**
 * Fighter Rating Lookup Script
 * Look up any fighter's current WHR power rating
 */

class FighterRatingLookup {
  constructor() {
    this.dbPath = path.join(__dirname, "..", "data", "ufc_data.db");
    this.db = new Database(this.dbPath);
  }

  lookupFighter(searchName) {
    console.log(`🔍 Looking up fighter: "${searchName}"`);
    console.log("═".repeat(60));

    // First, find the fighter by name (case insensitive)
    const fighters = this.db
      .prepare(
        `
            SELECT id, first_name, last_name, nickname, weight_class
            FROM fighters
            WHERE LOWER(first_name || ' ' || last_name) LIKE LOWER(?)
               OR LOWER(nickname) LIKE LOWER(?)
               OR LOWER(first_name) LIKE LOWER(?)
               OR LOWER(last_name) LIKE LOWER(?)
            ORDER BY
                CASE
                    WHEN LOWER(first_name || ' ' || last_name) = LOWER(?) THEN 1
                    WHEN LOWER(first_name || ' ' || last_name) LIKE LOWER(?) THEN 2
                    ELSE 3
                END
        `
      )
      .all(
        `%${searchName}%`,
        `%${searchName}%`,
        `%${searchName}%`,
        `%${searchName}%`,
        searchName,
        `%${searchName}%`
      );

    if (fighters.length === 0) {
      console.log("❌ No fighters found matching that name");
      return;
    }

    if (fighters.length > 1) {
      console.log("🔍 Multiple fighters found:");
      fighters.forEach((fighter, index) => {
        const nickname = fighter.nickname ? ` "${fighter.nickname}"` : "";
        console.log(
          `  ${index + 1}. ${fighter.first_name} ${
            fighter.last_name
          }${nickname} (${fighter.weight_class})`
        );
      });
      console.log("\nShowing results for the first match...\n");
    }

    const fighter = fighters[0];
    const nickname = fighter.nickname ? ` "${fighter.nickname}"` : "";
    console.log(
      `👤 Fighter: ${fighter.first_name} ${fighter.last_name}${nickname}`
    );
    console.log(`🆔 Fighter ID: ${fighter.id}`);
    console.log(`⚖️  Primary Weight Class: ${fighter.weight_class}`);

    // Get all WHR ratings for this fighter
    const ratings = this.db
      .prepare(
        `
            SELECT
                wr.division,
                wr.rating,
                wr.rating_deviation,
                wr.fight_count,
                wr.win_count,
                wr.loss_count,
                wr.last_fight_date,
                dr.rank,
                dr.streak,
                dr.status
            FROM whr_ratings wr
            LEFT JOIN whr_division_rankings dr ON
                wr.fighter_id = dr.fighter_id AND
                wr.division = dr.division AND
                dr.ranking_date = (
                    SELECT MAX(ranking_date)
                    FROM whr_division_rankings dr2
                    WHERE dr2.division = dr.division
                )
            WHERE wr.fighter_id = ?
            ORDER BY wr.rating DESC
        `
      )
      .all(fighter.id);

    if (ratings.length === 0) {
      console.log("❌ No WHR ratings found for this fighter");
      return;
    }

    console.log("\n🏆 WHR POWER RATINGS:");
    console.log("═".repeat(60));

    ratings.forEach((rating, index) => {
      const isPrimary = index === 0 ? " (PRIMARY)" : "";
      console.log(`\n📊 ${rating.division}${isPrimary}`);
      console.log(
        `   Rating: ${rating.rating.toFixed(
          1
        )} ± ${rating.rating_deviation.toFixed(1)}`
      );
      console.log(
        `   Record: ${rating.win_count}-${rating.loss_count} (${rating.fight_count} fights)`
      );

      if (rating.rank) {
        console.log(`   Division Rank: #${rating.rank}`);
      }

      if (rating.streak !== null) {
        const streakType =
          rating.streak > 0 ? "win" : rating.streak < 0 ? "loss" : "no";
        const streakCount = Math.abs(rating.streak);
        console.log(`   Streak: ${streakCount} ${streakType} streak`);
      }

      if (rating.last_fight_date) {
        console.log(`   Last Fight: ${rating.last_fight_date}`);
      }

      console.log(`   Status: ${rating.status || "Active"}`);
    });

    // Get recent fight history
    console.log("\n📈 RECENT FIGHT HISTORY:");
    console.log("═".repeat(60));

    const recentFights = this.db
      .prepare(
        `
            SELECT
                f.id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                f.weight_class,
                e.date,
                e.event_name,
                CASE
                    WHEN f.fighter1_id = ? THEN f2.first_name || ' ' || f2.last_name
                    ELSE f1.first_name || ' ' || f1.last_name
                END as opponent_name,
                CASE
                    WHEN f.winner_id = ? THEN 'W'
                    WHEN f.winner_id IS NULL THEN 'D'
                    ELSE 'L'
                END as result
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class != 'Catch Weight'
            ORDER BY e.date DESC
            LIMIT 5
        `
      )
      .all(fighter.id, fighter.id, fighter.id, fighter.id);

    if (recentFights.length > 0) {
      recentFights.forEach((fight) => {
        console.log(
          `   ${fight.result} vs ${fight.opponent_name} (${fight.weight_class})`
        );
        console.log(
          `     ${fight.event_name} - ${fight.date} - ${fight.result_method}`
        );
      });
    } else {
      console.log("   No recent fights found");
    }

    // Rating interpretation
    const primaryRating = ratings[0];
    console.log("\n💡 RATING INTERPRETATION:");
    console.log("═".repeat(60));

    let interpretation = "";
    if (primaryRating.rating >= 1700) {
      interpretation = "Elite/Championship Level";
    } else if (primaryRating.rating >= 1600) {
      interpretation = "Top Contender";
    } else if (primaryRating.rating >= 1550) {
      interpretation = "High-Level Fighter";
    } else if (primaryRating.rating >= 1500) {
      interpretation = "Solid UFC Fighter";
    } else if (primaryRating.rating >= 1450) {
      interpretation = "Developing Fighter";
    } else {
      interpretation = "Entry Level";
    }

    console.log(`   ${primaryRating.rating.toFixed(1)} = ${interpretation}`);
    console.log(`   Rating Scale: 1200 (lowest) to 1900+ (elite)`);
    console.log(
      `   Deviation: ±${primaryRating.rating_deviation.toFixed(
        1
      )} (lower = more certain)`
    );
  }

  close() {
    if (this.db) this.db.close();
  }
}

// Run lookup if called directly
if (require.main === module) {
  const lookup = new FighterRatingLookup();

  // Get fighter name from command line argument or default to Dustin Poirier
  const searchName = process.argv[2] || "Dustin Poirier";

  try {
    lookup.lookupFighter(searchName);
  } catch (error) {
    console.error("❌ Error:", error.message);
  } finally {
    lookup.close();
  }
}

module.exports = FighterRatingLookup;
