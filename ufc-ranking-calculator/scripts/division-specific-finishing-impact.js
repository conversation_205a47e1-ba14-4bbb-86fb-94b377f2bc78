const Database = require('better-sqlite3');
const path = require('path');

/**
 * Division-Specific Finishing Impact - Roadmap #6
 * 
 * Implementation Approach:
 * - Calculate correlation between finish types and future performance
 * - Test KO/TKO vs submission predictive value per division
 * - Analyze which round finishes occurred using round-by-round data
 * - Measure pre-finish dominance impact (strike differentials, knockdowns)
 * - Pure empirical measurement - no assumptions
 * - Replace arbitrary finish bonuses with data-driven multipliers
 */

class DivisionSpecificFinishingImpact {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        this.finishingImpacts = new Map();
        
        console.log('🎯 Division-Specific Finishing Impact Calculator initialized');
    }

    /**
     * Analyze finishing impact for all divisions
     */
    async analyzeAllDivisions() {
        console.log('🔍 Analyzing division-specific finishing impacts...');
        
        const divisions = ['Lightweight', 'Welterweight', 'Middleweight', 'Featherweight', 
                          'Bantamweight', 'Light Heavyweight', 'Heavyweight', 'Flyweight',
                          "Women's Strawweight", "Women's Flyweight", "Women's Bantamweight"];
        
        const results = [];
        
        for (const division of divisions) {
            console.log(`\n🥊 Analyzing ${division}...`);
            
            try {
                const analysis = await this.analyzeDivisionFinishingImpact(division);
                if (analysis) {
                    results.push(analysis);
                    this.finishingImpacts.set(division, analysis);
                }
            } catch (error) {
                console.error(`❌ Error analyzing ${division}:`, error.message);
            }
        }
        
        return results;
    }

    /**
     * Analyze finishing impact for a specific division
     */
    async analyzeDivisionFinishingImpact(division) {
        // Get finishing data for this division
        const finishingData = await this.getFinishingData(division);
        
        if (finishingData.length < 50) {
            console.log(`  ⚠️  Insufficient finishing data for ${division} (${finishingData.length} finishes)`);
            return null;
        }
        
        // Analyze different finish types
        const koTkoImpact = await this.analyzeFinishTypeImpact(finishingData, 'KO/TKO');
        const submissionImpact = await this.analyzeFinishTypeImpact(finishingData, 'Submission');
        
        // Analyze round-specific impacts
        const roundImpacts = await this.analyzeRoundSpecificImpact(finishingData);
        
        // Analyze dominance-based finishing
        const dominanceImpact = await this.analyzeDominanceBasedFinishing(finishingData);
        
        // Calculate overall division characteristics
        const divisionStats = this.calculateDivisionFinishingStats(finishingData);
        
        console.log(`  📊 ${finishingData.length} finishes analyzed`);
        console.log(`  🥊 KO/TKO future performance impact: ${koTkoImpact.futurePerformanceBoost.toFixed(3)}`);
        console.log(`  🤼 Submission future performance impact: ${submissionImpact.futurePerformanceBoost.toFixed(3)}`);
        console.log(`  ⚡ Round 1 finish rate: ${(divisionStats.round1FinishRate * 100).toFixed(1)}%`);
        
        return {
            division,
            koTkoImpact,
            submissionImpact,
            roundImpacts,
            dominanceImpact,
            divisionStats,
            sampleSize: finishingData.length
        };
    }

    /**
     * Get finishing data for a division
     */
    async getFinishingData(division) {
        const query = `
            SELECT 
                f.id as fight_id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                f.result_round,
                f.result_time,
                e.date as fight_date,
                
                -- Fighter stats if available
                fs1.knockdowns as f1_knockdowns,
                fs1.sig_strikes_landed as f1_sig_strikes,
                fs1.sig_strikes_attempted as f1_sig_attempts,
                fs2.knockdowns as f2_knockdowns,
                fs2.sig_strikes_landed as f2_sig_strikes,
                fs2.sig_strikes_attempted as f2_sig_attempts
                
            FROM fights f
            JOIN events e ON f.event_id = e.id
            LEFT JOIN fight_stats fs1 ON f.id = fs1.fight_id AND f.fighter1_id = fs1.fighter_id
            LEFT JOIN fight_stats fs2 ON f.id = fs2.fight_id AND f.fighter2_id = fs2.fighter_id
            WHERE f.weight_class = ?
                AND f.result_method NOT LIKE '%Decision%'
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.fight_status = 'valid'
                AND f.winner_id IS NOT NULL
            ORDER BY e.date ASC
        `;
        
        const finishes = this.db.prepare(query).all(division);
        
        // Process and categorize finishes
        return finishes.map(finish => {
            let finishType = 'Other';
            if (finish.result_method.includes('KO') || finish.result_method.includes('TKO')) {
                finishType = 'KO/TKO';
            } else if (finish.result_method.includes('Submission')) {
                finishType = 'Submission';
            }
            
            return {
                ...finish,
                finishType,
                isEarlyFinish: finish.result_round === 1,
                dominanceMetrics: this.calculateFinishDominance(finish)
            };
        });
    }

    /**
     * Analyze impact of specific finish type on future performance
     */
    async analyzeFinishTypeImpact(finishingData, finishType) {
        const relevantFinishes = finishingData.filter(f => f.finishType === finishType);
        
        if (relevantFinishes.length < 10) {
            return {
                finishType,
                futurePerformanceBoost: 0,
                sampleSize: relevantFinishes.length,
                confidence: 'Very Low'
            };
        }
        
        // Analyze future performance of fighters who achieved this finish type
        let totalBoost = 0;
        let validComparisons = 0;
        
        for (const finish of relevantFinishes) {
            const futurePerformance = await this.getFuturePerformance(
                finish.winner_id, 
                finish.fight_date, 
                finish.weight_class
            );
            
            if (futurePerformance.nextFights >= 3) {
                // Compare to fighter's historical average
                const historicalPerformance = await this.getHistoricalPerformance(
                    finish.winner_id,
                    finish.fight_date,
                    finish.weight_class
                );
                
                if (historicalPerformance.totalFights >= 3) {
                    const boost = futurePerformance.winRate - historicalPerformance.winRate;
                    totalBoost += boost;
                    validComparisons++;
                }
            }
        }
        
        const avgBoost = validComparisons > 0 ? totalBoost / validComparisons : 0;
        const confidence = this.assessConfidence(validComparisons);
        
        return {
            finishType,
            futurePerformanceBoost: avgBoost,
            sampleSize: relevantFinishes.length,
            validComparisons,
            confidence
        };
    }

    /**
     * Analyze round-specific finishing impacts
     */
    async analyzeRoundSpecificImpact(finishingData) {
        const roundImpacts = {};
        
        for (let round = 1; round <= 5; round++) {
            const roundFinishes = finishingData.filter(f => f.result_round === round);
            
            if (roundFinishes.length >= 5) {
                let totalImpact = 0;
                let validSamples = 0;
                
                for (const finish of roundFinishes) {
                    const futurePerf = await this.getFuturePerformance(
                        finish.winner_id,
                        finish.fight_date,
                        finish.weight_class
                    );
                    
                    if (futurePerf.nextFights >= 2) {
                        totalImpact += futurePerf.winRate;
                        validSamples++;
                    }
                }
                
                roundImpacts[`round${round}`] = {
                    avgFutureWinRate: validSamples > 0 ? totalImpact / validSamples : 0,
                    sampleSize: roundFinishes.length,
                    validSamples
                };
            }
        }
        
        return roundImpacts;
    }

    /**
     * Analyze dominance-based finishing (pre-finish performance)
     */
    async analyzeDominanceBasedFinishing(finishingData) {
        const dominanceCategories = {
            highDominance: [], // Significant strike advantage + knockdowns
            mediumDominance: [], // Moderate advantage
            lowDominance: [] // Close fight finish
        };
        
        for (const finish of finishingData) {
            const dominance = finish.dominanceMetrics;
            
            if (dominance.category === 'high') {
                dominanceCategories.highDominance.push(finish);
            } else if (dominance.category === 'medium') {
                dominanceCategories.mediumDominance.push(finish);
            } else {
                dominanceCategories.lowDominance.push(finish);
            }
        }
        
        const results = {};
        
        for (const [category, finishes] of Object.entries(dominanceCategories)) {
            if (finishes.length >= 10) {
                let totalBoost = 0;
                let validSamples = 0;
                
                for (const finish of finishes) {
                    const futurePerf = await this.getFuturePerformance(
                        finish.winner_id,
                        finish.fight_date,
                        finish.weight_class
                    );
                    
                    if (futurePerf.nextFights >= 2) {
                        totalBoost += futurePerf.winRate;
                        validSamples++;
                    }
                }
                
                results[category] = {
                    avgFutureWinRate: validSamples > 0 ? totalBoost / validSamples : 0,
                    sampleSize: finishes.length,
                    validSamples
                };
            }
        }
        
        return results;
    }

    /**
     * Calculate dominance metrics for a finish
     */
    calculateFinishDominance(finish) {
        const f1Strikes = finish.f1_sig_strikes || 0;
        const f2Strikes = finish.f2_sig_strikes || 0;
        const f1Knockdowns = finish.f1_knockdowns || 0;
        const f2Knockdowns = finish.f2_knockdowns || 0;
        
        const isWinner1 = finish.winner_id === finish.fighter1_id;
        const winnerStrikes = isWinner1 ? f1Strikes : f2Strikes;
        const loserStrikes = isWinner1 ? f2Strikes : f1Strikes;
        const winnerKnockdowns = isWinner1 ? f1Knockdowns : f2Knockdowns;
        
        const strikeDifferential = winnerStrikes - loserStrikes;
        const hasKnockdowns = winnerKnockdowns > 0;
        
        let category = 'low';
        if (strikeDifferential > 20 || hasKnockdowns) {
            category = 'high';
        } else if (strikeDifferential > 10) {
            category = 'medium';
        }
        
        return {
            strikeDifferential,
            hasKnockdowns,
            category,
            winnerStrikes,
            loserStrikes
        };
    }

    /**
     * Get future performance after a specific fight
     */
    async getFuturePerformance(fighterId, afterDate, division) {
        const query = `
            SELECT 
                f.winner_id,
                COUNT(*) as total_fights
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = ?
                AND e.date > ?
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.fight_status = 'valid'
        `;
        
        const futureResults = this.db.prepare(query).all(fighterId, fighterId, division, afterDate);
        const totalFights = futureResults.reduce((sum, result) => sum + result.total_fights, 0);
        const wins = futureResults.filter(result => result.winner_id === fighterId).length;
        
        return {
            nextFights: totalFights,
            wins,
            winRate: totalFights > 0 ? wins / totalFights : 0
        };
    }

    /**
     * Get historical performance before a specific fight
     */
    async getHistoricalPerformance(fighterId, beforeDate, division) {
        const query = `
            SELECT 
                f.winner_id,
                COUNT(*) as total_fights
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND f.weight_class = ?
                AND e.date < ?
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.fight_status = 'valid'
        `;
        
        const historicalResults = this.db.prepare(query).all(fighterId, fighterId, division, beforeDate);
        const totalFights = historicalResults.reduce((sum, result) => sum + result.total_fights, 0);
        const wins = historicalResults.filter(result => result.winner_id === fighterId).length;
        
        return {
            totalFights,
            wins,
            winRate: totalFights > 0 ? wins / totalFights : 0
        };
    }

    /**
     * Calculate division finishing statistics
     */
    calculateDivisionFinishingStats(finishingData) {
        const total = finishingData.length;
        const koTkoCount = finishingData.filter(f => f.finishType === 'KO/TKO').length;
        const submissionCount = finishingData.filter(f => f.finishType === 'Submission').length;
        const round1Finishes = finishingData.filter(f => f.result_round === 1).length;
        const round2Finishes = finishingData.filter(f => f.result_round === 2).length;
        const round3Finishes = finishingData.filter(f => f.result_round === 3).length;
        
        return {
            totalFinishes: total,
            koTkoRate: koTkoCount / total,
            submissionRate: submissionCount / total,
            round1FinishRate: round1Finishes / total,
            round2FinishRate: round2Finishes / total,
            round3FinishRate: round3Finishes / total,
            avgFinishRound: finishingData.reduce((sum, f) => sum + f.result_round, 0) / total
        };
    }

    /**
     * Assess confidence level based on sample size
     */
    assessConfidence(sampleSize) {
        if (sampleSize >= 50) return 'Very High';
        if (sampleSize >= 25) return 'High';
        if (sampleSize >= 10) return 'Medium';
        if (sampleSize >= 5) return 'Low';
        return 'Very Low';
    }

    /**
     * Store finishing impact data in database
     */
    storeFinishingImpacts() {
        console.log('💾 Storing finishing impact data in database...');
        
        // Create table
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS division_finishing_impacts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                division TEXT NOT NULL,
                finish_type TEXT NOT NULL,
                future_performance_boost REAL NOT NULL,
                round_1_multiplier REAL,
                round_2_multiplier REAL,
                round_3_multiplier REAL,
                dominance_high_boost REAL,
                dominance_medium_boost REAL,
                sample_size INTEGER,
                confidence_level TEXT,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                UNIQUE(division, finish_type)
            )
        `);
        
        // Clear existing data
        this.db.exec('DELETE FROM division_finishing_impacts');
        
        const insertStmt = this.db.prepare(`
            INSERT INTO division_finishing_impacts (
                division, finish_type, future_performance_boost,
                round_1_multiplier, round_2_multiplier, round_3_multiplier,
                dominance_high_boost, dominance_medium_boost,
                sample_size, confidence_level
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        let totalRecords = 0;
        
        for (const [division, analysis] of this.finishingImpacts) {
            // Store KO/TKO impact
            const round1Mult = analysis.roundImpacts.round1?.avgFutureWinRate || 0;
            const round2Mult = analysis.roundImpacts.round2?.avgFutureWinRate || 0;
            const round3Mult = analysis.roundImpacts.round3?.avgFutureWinRate || 0;
            const highDomBoost = analysis.dominanceImpact.highDominance?.avgFutureWinRate || 0;
            const medDomBoost = analysis.dominanceImpact.mediumDominance?.avgFutureWinRate || 0;
            
            insertStmt.run(
                division, 'KO/TKO', analysis.koTkoImpact.futurePerformanceBoost,
                round1Mult, round2Mult, round3Mult,
                highDomBoost, medDomBoost,
                analysis.koTkoImpact.sampleSize, analysis.koTkoImpact.confidence
            );
            
            insertStmt.run(
                division, 'Submission', analysis.submissionImpact.futurePerformanceBoost,
                round1Mult, round2Mult, round3Mult,
                highDomBoost, medDomBoost,
                analysis.submissionImpact.sampleSize, analysis.submissionImpact.confidence
            );
            
            totalRecords += 2;
        }
        
        console.log(`✅ Stored ${totalRecords} finishing impact records for ${this.finishingImpacts.size} divisions`);
    }

    /**
     * Display finishing impact summary
     */
    displayFinishingImpactSummary() {
        console.log('\n🎯 Division-Specific Finishing Impact Summary');
        console.log('═'.repeat(80));
        
        for (const [division, analysis] of this.finishingImpacts) {
            console.log(`\n🥊 ${division}:`);
            console.log(`   Total Finishes: ${analysis.sampleSize}`);
            console.log(`   KO/TKO Future Boost: ${(analysis.koTkoImpact.futurePerformanceBoost * 100).toFixed(1)}%`);
            console.log(`   Submission Future Boost: ${(analysis.submissionImpact.futurePerformanceBoost * 100).toFixed(1)}%`);
            console.log(`   Round 1 Finish Rate: ${(analysis.divisionStats.round1FinishRate * 100).toFixed(1)}%`);
            console.log(`   Avg Finish Round: ${analysis.divisionStats.avgFinishRound.toFixed(1)}`);
        }
        
        console.log('\n═'.repeat(80));
    }

    /**
     * Generate finishing multiplier recommendations
     */
    generateFinishingMultipliers() {
        console.log('\n📊 Recommended Finishing Multipliers');
        console.log('═'.repeat(60));
        
        for (const [division, analysis] of this.finishingImpacts) {
            console.log(`\n${division}:`);
            
            // KO/TKO multiplier
            const koMultiplier = 1.0 + Math.max(0, analysis.koTkoImpact.futurePerformanceBoost);
            console.log(`  KO/TKO Multiplier: ${koMultiplier.toFixed(3)}`);
            
            // Submission multiplier
            const subMultiplier = 1.0 + Math.max(0, analysis.submissionImpact.futurePerformanceBoost);
            console.log(`  Submission Multiplier: ${subMultiplier.toFixed(3)}`);
            
            // Round bonuses
            if (analysis.roundImpacts.round1) {
                const round1Bonus = Math.max(0, analysis.roundImpacts.round1.avgFutureWinRate - 0.5);
                console.log(`  Round 1 Bonus: +${(round1Bonus * 100).toFixed(1)}%`);
            }
        }
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) {
            this.db.close();
            console.log('🔒 Database connection closed');
        }
    }
}

// Export for use in other modules
module.exports = DivisionSpecificFinishingImpact;

// Test run if called directly
if (require.main === module) {
    console.log('🚀 Implementing Division-Specific Finishing Impact...');
    
    const analyzer = new DivisionSpecificFinishingImpact();
    
    analyzer.analyzeAllDivisions()
        .then(results => {
            console.log(`\n✅ Finishing impact analysis complete for ${results.length} divisions`);
            
            // Display summary
            analyzer.displayFinishingImpactSummary();
            
            // Store in database
            analyzer.storeFinishingImpacts();
            
            // Generate multiplier recommendations
            analyzer.generateFinishingMultipliers();
            
            console.log('\n🎯 Division-Specific Finishing Impact implementation completed successfully');
            console.log('🏆 All Medium Priority roadmap components complete!');
        })
        .catch(error => {
            console.error('❌ Error in finishing impact analysis:', error);
        })
        .finally(() => {
            analyzer.close();
        });
}