const WHRMainAlgorithm = require('./whr-main-algorithm');

/**
 * WHR Database Population Script
 * 
 * Run this script to:
 * 1. Calculate all WHR ratings from scratch
 * 2. Generate current rankings for all divisions
 * 3. Display summary statistics
 * 
 * Usage: node populate-whr-database.js
 */

async function populateWHRDatabase() {
    console.log('🚀 WHR Database Population Script');
    console.log('═'.repeat(60));
    console.log('This will recalculate all WHR ratings from scratch.');
    console.log('═'.repeat(60));
    
    const startTime = Date.now();
    const whr = new WHRMainAlgorithm();
    
    try {
        // Calculate complete WHR ratings
        await whr.calculateCompleteWHRRatings();
        
        // Generate current rankings
        await whr.generateCurrentRankings();
        
        // Display summary statistics
        whr.displaySummaryStatistics();
        
        // Show sample of top fighters
        await displayTopFighters(whr);
        
        const elapsedTime = ((Date.now() - startTime) / 1000).toFixed(1);
        console.log(`\n✅ WHR database population completed in ${elapsedTime} seconds!`);
        
    } catch (error) {
        console.error('❌ Error populating WHR database:', error);
        process.exit(1);
    } finally {
        whr.close();
    }
}

/**
 * Display top fighters from each major division
 */
async function displayTopFighters(whr) {
    const Database = require('better-sqlite3');
    const path = require('path');
    const db = new Database(path.join(__dirname, '..', 'data', 'ufc_data.db'));
    
    console.log('\n🏆 Sample Top 3 Fighters by Division');
    console.log('═'.repeat(60));
    
    const majorDivisions = ['Lightweight', 'Welterweight', 'Middleweight', 'Bantamweight'];
    
    for (const division of majorDivisions) {
        console.log(`\n${division}:`);
        
        const topFighters = db.prepare(`
            SELECT 
                f.first_name || ' ' || f.last_name as name,
                dr.rank,
                dr.rating,
                dr.streak,
                dr.activity_score,
                wr.fight_count,
                wr.win_count,
                wr.loss_count
            FROM whr_division_rankings dr
            JOIN fighters f ON dr.fighter_id = f.id
            JOIN whr_ratings wr ON dr.fighter_id = wr.fighter_id AND dr.division = wr.division
            WHERE dr.division = ?
            ORDER BY dr.rank
            LIMIT 3
        `).all(division);
        
        topFighters.forEach(fighter => {
            const record = `${fighter.win_count}-${fighter.loss_count}`;
            const streakStr = fighter.streak > 0 ? `W${fighter.streak}` : 
                            fighter.streak < 0 ? `L${Math.abs(fighter.streak)}` : '-';
            console.log(`  ${fighter.rank}. ${fighter.name}: ${fighter.rating.toFixed(1)} (${record}, ${streakStr} streak)`);
        });
    }
    
    db.close();
}

// Run the population script
if (require.main === module) {
    populateWHRDatabase();
}