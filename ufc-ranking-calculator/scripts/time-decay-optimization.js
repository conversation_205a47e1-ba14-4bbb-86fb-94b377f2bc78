const Database = require('better-sqlite3');
const path = require('path');

/**
 * Time Decay Optimization - Roadmap #5
 * 
 * Implementation Approach:
 * - Calendar-based decay using days elapsed between fights
 * - Smooth exponential decay function for recency weighting
 * - Test different decay rates per division for optimal half-life
 * - Natural multiplication with age curves for age-time interaction
 * - Recent fights weighted more regardless of fight frequency
 * 
 * Key principle: Recent performances are more indicative of current skill level
 */

class TimeDecayOptimization {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        this.decayParameters = new Map();
        
        console.log('⏰ Time Decay Optimization Calculator initialized');
    }

    /**
     * Calculate optimal decay parameters for each division
     */
    async calculateOptimalDecayParameters() {
        console.log('🔍 Calculating optimal time decay parameters...');
        
        const divisions = ['Lightweight', 'Welterweight', 'Middleweight', 'Featherweight', 
                          'Bantamweight', 'Light Heavyweight', 'Heavyweight', 'Flyweight',
                          "Women's Strawweight", "Women's Flyweight", "Women's Bantamweight"];
        
        const results = [];
        
        for (const division of divisions) {
            console.log(`\n⚡ Analyzing ${division}...`);
            
            const analysis = await this.analyzeDivisionDecayPattern(division);
            if (analysis) {
                results.push(analysis);
                this.decayParameters.set(division, analysis);
            }
        }
        
        return results;
    }

    /**
     * Analyze time decay patterns for a specific division
     */
    async analyzeDivisionDecayPattern(division) {
        // Get fighter performance over time in this division
        const performanceData = await this.getFighterPerformanceOverTime(division);
        
        if (performanceData.length < 100) {
            console.log(`  ⚠️  Insufficient data for ${division} (${performanceData.length} data points)`);
            return null;
        }
        
        // Test different decay rates
        const decayRates = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9];
        let bestRate = 0.5;
        let bestAccuracy = 0;
        
        for (const rate of decayRates) {
            const accuracy = await this.testDecayRate(performanceData, rate);
            console.log(`    Testing decay rate ${rate}: ${(accuracy * 100).toFixed(1)}% accuracy`);
            
            if (accuracy > bestAccuracy) {
                bestAccuracy = accuracy;
                bestRate = rate;
            }
        }
        
        // Calculate half-life in days
        const halfLife = Math.log(0.5) / Math.log(1 - bestRate / 365);
        
        // Calculate activity patterns
        const activityMetrics = this.calculateActivityMetrics(performanceData);
        
        console.log(`  📊 Optimal decay rate: ${bestRate} (${(bestAccuracy * 100).toFixed(1)}% accuracy)`);
        console.log(`  ⏱️  Half-life: ${Math.round(Math.abs(halfLife))} days`);
        console.log(`  📈 Average gap between fights: ${activityMetrics.avgDaysBetweenFights.toFixed(1)} days`);
        
        return {
            division,
            optimalDecayRate: bestRate,
            halfLifeDays: Math.abs(halfLife),
            predictionAccuracy: bestAccuracy,
            activityMetrics,
            sampleSize: performanceData.length
        };
    }

    /**
     * Get fighter performance data over time for decay analysis
     */
    async getFighterPerformanceOverTime(division) {
        const query = `
            SELECT 
                f.id as fight_id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                e.date as fight_date,
                f.result_method,
                f.result_round
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class = ?
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.fight_status = 'valid'
                AND f.winner_id IS NOT NULL
            ORDER BY e.date ASC
        `;
        
        const fights = this.db.prepare(query).all(division);
        const performanceData = [];
        
        // Track each fighter's performance over time
        const fighterHistories = new Map();
        
        for (const fight of fights) {
            const fightDate = new Date(fight.fight_date);
            
            // Process both fighters
            for (const fighterId of [fight.fighter1_id, fight.fighter2_id]) {
                if (!fighterHistories.has(fighterId)) {
                    fighterHistories.set(fighterId, []);
                }
                
                const history = fighterHistories.get(fighterId);
                const won = fight.winner_id === fighterId;
                const isFinish = !fight.result_method.includes('Decision');
                
                // If fighter has previous fights, create data point
                if (history.length > 0) {
                    const lastFight = history[history.length - 1];
                    const daysSinceLastFight = (fightDate - new Date(lastFight.date)) / (1000 * 60 * 60 * 24);
                    
                    performanceData.push({
                        fighterId,
                        currentFightDate: fight.fight_date,
                        daysSinceLastFight,
                        previousPerformance: lastFight.won ? 1 : 0,
                        previousFinish: lastFight.isFinish ? 1 : 0,
                        currentResult: won ? 1 : 0,
                        currentFinish: isFinish ? 1 : 0,
                        fighterHistory: history.length
                    });
                }
                
                // Add current fight to history
                history.push({
                    date: fight.fight_date,
                    won,
                    isFinish,
                    fightId: fight.fight_id
                });
            }
        }
        
        return performanceData;
    }

    /**
     * Test a specific decay rate for prediction accuracy
     */
    async testDecayRate(performanceData, decayRate) {
        let correct = 0;
        let total = 0;
        
        for (const data of performanceData) {
            // Skip if not enough history
            if (data.fighterHistory < 2) continue;
            
            // Calculate time-weighted performance
            const daysAgo = data.daysSinceLastFight;
            const timeWeight = Math.exp(-decayRate * daysAgo / 365); // Annual decay rate
            
            // Simple prediction: recent performance weighted by time decay
            const weightedPrevPerformance = data.previousPerformance * timeWeight;
            const prediction = weightedPrevPerformance > 0.5 ? 1 : 0;
            
            if (prediction === data.currentResult) {
                correct++;
            }
            total++;
        }
        
        return total > 0 ? correct / total : 0;
    }

    /**
     * Calculate activity metrics for the division
     */
    calculateActivityMetrics(performanceData) {
        if (performanceData.length === 0) {
            return { avgDaysBetweenFights: 365, medianDaysBetweenFights: 365 };
        }
        
        const gaps = performanceData.map(d => d.daysSinceLastFight).filter(gap => gap > 0);
        
        const avgGap = gaps.reduce((sum, gap) => sum + gap, 0) / gaps.length;
        const sortedGaps = gaps.sort((a, b) => a - b);
        const medianGap = sortedGaps[Math.floor(sortedGaps.length / 2)];
        
        return {
            avgDaysBetweenFights: avgGap,
            medianDaysBetweenFights: medianGap,
            shortestGap: Math.min(...gaps),
            longestGap: Math.max(...gaps),
            totalDataPoints: gaps.length
        };
    }

    /**
     * Calculate time decay weight for a specific time period
     */
    calculateTimeDecayWeight(daysSince, division) {
        const params = this.decayParameters.get(division);
        if (!params) {
            // Default decay parameters if division not analyzed
            return Math.exp(-0.5 * daysSince / 365);
        }
        
        return Math.exp(-params.optimalDecayRate * daysSince / 365);
    }

    /**
     * Apply time decay to performance metrics
     */
    applyTimeDecayToPerformance(performances, currentDate, division) {
        const weightedPerformances = [];
        
        for (const performance of performances) {
            const daysSince = (new Date(currentDate) - new Date(performance.date)) / (1000 * 60 * 60 * 24);
            const timeWeight = this.calculateTimeDecayWeight(daysSince, division);
            
            weightedPerformances.push({
                ...performance,
                timeWeight,
                weightedValue: performance.value * timeWeight,
                daysSince
            });
        }
        
        return weightedPerformances;
    }

    /**
     * Calculate recency-weighted average
     */
    calculateRecencyWeightedAverage(weightedPerformances) {
        if (weightedPerformances.length === 0) return 0;
        
        const totalWeightedValue = weightedPerformances.reduce((sum, p) => sum + p.weightedValue, 0);
        const totalWeight = weightedPerformances.reduce((sum, p) => sum + p.timeWeight, 0);
        
        return totalWeight > 0 ? totalWeightedValue / totalWeight : 0;
    }

    /**
     * Store time decay parameters in database
     */
    storeTimeDecayParameters() {
        console.log('💾 Storing time decay parameters in database...');
        
        // Create table
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS time_decay_parameters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                division TEXT NOT NULL UNIQUE,
                optimal_decay_rate REAL NOT NULL,
                half_life_days REAL NOT NULL,
                prediction_accuracy REAL NOT NULL,
                avg_days_between_fights REAL,
                median_days_between_fights REAL,
                sample_size INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        `);
        
        // Clear existing data
        this.db.exec('DELETE FROM time_decay_parameters');
        
        const insertStmt = this.db.prepare(`
            INSERT INTO time_decay_parameters (
                division, optimal_decay_rate, half_life_days, prediction_accuracy,
                avg_days_between_fights, median_days_between_fights, sample_size
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        `);
        
        for (const [division, params] of this.decayParameters) {
            insertStmt.run(
                division,
                params.optimalDecayRate,
                params.halfLifeDays,
                params.predictionAccuracy,
                params.activityMetrics.avgDaysBetweenFights,
                params.activityMetrics.medianDaysBetweenFights,
                params.sampleSize
            );
        }
        
        console.log(`✅ Stored time decay parameters for ${this.decayParameters.size} divisions`);
    }

    /**
     * Display time decay analysis summary
     */
    displayTimeDecaySummary() {
        console.log('\n⏰ Time Decay Optimization Summary');
        console.log('═'.repeat(80));
        
        for (const [division, params] of this.decayParameters) {
            console.log(`\n🥊 ${division}:`);
            console.log(`   Optimal Decay Rate: ${params.optimalDecayRate}`);
            console.log(`   Half-Life: ${Math.round(params.halfLifeDays)} days`);
            console.log(`   Prediction Accuracy: ${(params.predictionAccuracy * 100).toFixed(1)}%`);
            console.log(`   Avg Fight Frequency: ${Math.round(params.activityMetrics.avgDaysBetweenFights)} days`);
            console.log(`   Sample Size: ${params.sampleSize} performance transitions`);
        }
        
        console.log('\n═'.repeat(80));
    }

    /**
     * Generate time decay usage examples
     */
    generateUsageExamples() {
        console.log('\n📋 Time Decay Usage Examples');
        console.log('═'.repeat(60));
        
        const testDivision = 'Lightweight';
        const testDates = [30, 90, 180, 365, 730]; // Days ago
        
        console.log(`\nTime decay weights for ${testDivision}:`);
        testDates.forEach(days => {
            const weight = this.calculateTimeDecayWeight(days, testDivision);
            console.log(`  ${days} days ago: ${(weight * 100).toFixed(1)}% weight`);
        });
        
        console.log('\n📊 Practical Application:');
        console.log('- Recent fights (< 90 days): High weight, strong influence');
        console.log('- Medium term (90-365 days): Moderate weight, balanced influence'); 
        console.log('- Long term (> 365 days): Low weight, historical context only');
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) {
            this.db.close();
            console.log('🔒 Database connection closed');
        }
    }
}

// Export for use in other modules
module.exports = TimeDecayOptimization;

// Test run if called directly
if (require.main === module) {
    console.log('🚀 Implementing Time Decay Optimization...');
    
    const optimizer = new TimeDecayOptimization();
    
    optimizer.calculateOptimalDecayParameters()
        .then(results => {
            console.log(`\n✅ Time decay analysis complete for ${results.length} divisions`);
            
            // Display summary
            optimizer.displayTimeDecaySummary();
            
            // Store parameters
            optimizer.storeTimeDecayParameters();
            
            // Show usage examples
            optimizer.generateUsageExamples();
            
            console.log('\n🎯 Time Decay Optimization implementation completed successfully');
            console.log('🏁 Ready for Division-Specific Finishing Impact implementation');
        })
        .catch(error => {
            console.error('❌ Error in time decay optimization:', error);
        })
        .finally(() => {
            optimizer.close();
        });
}