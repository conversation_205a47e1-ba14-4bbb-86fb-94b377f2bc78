const Database = require('better-sqlite3');
const path = require('path');

/**
 * Temporal Parameter Optimizer
 * 
 * Optimizes WHR parameters based on TRUE predictive accuracy
 * using temporal backtesting (no future data leakage)
 */

class TemporalParameterOptimizer {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        
        // Parameter search space
        this.parameterSpace = {
            k_factor: [8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56, 60, 64],
            rating_scale_divisor: [200, 250, 300, 350, 400, 450, 500, 550, 600],
            initial_rating: [1500], // Keep fixed
            // New parameters to test
            experience_bonus: [0, 0.1, 0.2, 0.3], // Reduce K for experienced fighters
            momentum_factor: [0, 0.05, 0.1, 0.15], // Bonus for win streaks
            inactivity_penalty: [0, 0.02, 0.05, 0.1] // Rating decay for inactive fighters
        };
        
        this.config = {
            ratingFloor: 800,
            ratingCeiling: 2500,
            excludeDivisions: ['Catch Weight', 'Open Weight'],
            minFightsToTest: 100 // Minimum fights in division to optimize
        };
        
        console.log('🔬 Temporal Parameter Optimizer initialized');
    }

    /**
     * Optimize parameters for all divisions
     */
    async optimizeAllDivisions() {
        console.log('🚀 Starting temporal parameter optimization...');
        console.log('This will take several minutes per division\n');
        
        // Get divisions to optimize
        const divisions = this.db.prepare(`
            SELECT weight_class, COUNT(*) as fight_count
            FROM fights
            WHERE weight_class NOT IN (${this.config.excludeDivisions.map(() => '?').join(',')})
            GROUP BY weight_class
            HAVING fight_count >= ?
            ORDER BY fight_count DESC
        `).all(...this.config.excludeDivisions, this.config.minFightsToTest);
        
        const optimalParameters = {};
        
        for (const div of divisions) {
            console.log(`\n═══ Optimizing ${div.weight_class} (${div.fight_count} fights) ═══`);
            const startTime = Date.now();
            
            const bestParams = await this.optimizeDivision(div.weight_class);
            optimalParameters[div.weight_class] = bestParams;
            
            const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
            console.log(`✅ Completed in ${elapsed}s`);
            console.log(`   Best accuracy: ${(bestParams.accuracy * 100).toFixed(1)}%`);
            console.log(`   K-factor: ${bestParams.k_factor}`);
            console.log(`   Scale divisor: ${bestParams.rating_scale_divisor}`);
            
            // Quick test to limit optimization time for demo
            if (Object.keys(optimalParameters).length >= 3) {
                console.log('\n⚡ Limiting to 3 divisions for demo...');
                break;
            }
        }
        
        // Save optimal parameters
        await this.saveOptimalParameters(optimalParameters);
        
        // Display summary
        this.displayOptimizationSummary(optimalParameters);
        
        return optimalParameters;
    }

    /**
     * Optimize parameters for a single division
     */
    async optimizeDivision(division) {
        // Get all fights for this division
        const fights = this.db.prepare(`
            SELECT 
                f.id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                e.date as event_date
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class = ?
            ORDER BY e.date ASC, f.id ASC
        `).all(division);
        
        console.log(`   Testing ${fights.length} fights temporally...`);
        
        let bestParams = {
            k_factor: 32,
            rating_scale_divisor: 400,
            initial_rating: 1500,
            experience_bonus: 0,
            momentum_factor: 0,
            inactivity_penalty: 0,
            accuracy: 0,
            avgConfidence: 0
        };
        
        let testedCombinations = 0;
        const totalCombinations = 
            this.parameterSpace.k_factor.length * 
            this.parameterSpace.rating_scale_divisor.length;
        
        // Grid search over main parameters
        for (const k of this.parameterSpace.k_factor) {
            for (const scale of this.parameterSpace.rating_scale_divisor) {
                const params = {
                    k_factor: k,
                    rating_scale_divisor: scale,
                    initial_rating: 1500,
                    experience_bonus: 0.2, // Fixed for now
                    momentum_factor: 0.1,  // Fixed for now
                    inactivity_penalty: 0  // Fixed for now
                };
                
                const result = this.testParametersTemporal(fights, params);
                
                if (result.accuracy > bestParams.accuracy || 
                    (result.accuracy === bestParams.accuracy && result.avgConfidence > bestParams.avgConfidence)) {
                    bestParams = { ...params, ...result };
                }
                
                testedCombinations++;
                if (testedCombinations % 20 === 0) {
                    process.stdout.write(`\r   Progress: ${testedCombinations}/${totalCombinations} combinations tested...`);
                }
            }
        }
        
        console.log(`\r   Tested ${testedCombinations} parameter combinations`);
        
        // Fine-tune with advanced parameters
        console.log(`   Fine-tuning advanced parameters...`);
        bestParams = await this.fineTuneAdvancedParams(fights, bestParams);
        
        return bestParams;
    }

    /**
     * Test parameters using temporal backtesting
     */
    testParametersTemporal(fights, params) {
        const ratings = new Map();
        const fightCounts = new Map();
        const lastFightDates = new Map();
        const streaks = new Map();
        
        let correctPredictions = 0;
        let totalPredictions = 0;
        let totalConfidence = 0;
        const predictionsByConfidence = { low: 0, medium: 0, high: 0 };
        const correctByConfidence = { low: 0, medium: 0, high: 0 };
        
        // Process each fight chronologically
        for (const fight of fights) {
            const fighter1Id = fight.fighter1_id;
            const fighter2Id = fight.fighter2_id;
            
            // Initialize if needed
            if (!ratings.has(fighter1Id)) {
                ratings.set(fighter1Id, params.initial_rating);
                fightCounts.set(fighter1Id, 0);
                streaks.set(fighter1Id, 0);
            }
            if (!ratings.has(fighter2Id)) {
                ratings.set(fighter2Id, params.initial_rating);
                fightCounts.set(fighter2Id, 0);
                streaks.set(fighter2Id, 0);
            }
            
            // Apply inactivity penalty if applicable
            if (params.inactivity_penalty > 0) {
                this.applyInactivityPenalty(fighter1Id, fight.event_date, ratings, lastFightDates, params);
                this.applyInactivityPenalty(fighter2Id, fight.event_date, ratings, lastFightDates, params);
            }
            
            // Get current ratings
            let rating1 = ratings.get(fighter1Id);
            let rating2 = ratings.get(fighter2Id);
            
            // Apply momentum bonus
            if (params.momentum_factor > 0) {
                rating1 += streaks.get(fighter1Id) * params.momentum_factor * 10;
                rating2 += streaks.get(fighter2Id) * params.momentum_factor * 10;
            }
            
            // Make prediction
            const ratingDiff = rating1 - rating2;
            const expectedOutcome = 1 / (1 + Math.pow(10, -ratingDiff / params.rating_scale_divisor));
            
            // Determine actual outcome
            let actualOutcome;
            if (fight.result_method && fight.result_method.toLowerCase().includes('draw')) {
                actualOutcome = 0.5;
            } else if (fight.winner_id === fighter1Id) {
                actualOutcome = 1;
            } else {
                actualOutcome = 0;
            }
            
            // Evaluate prediction (skip draws)
            if (actualOutcome !== 0.5) {
                totalPredictions++;
                const confidence = Math.abs(expectedOutcome - 0.5);
                totalConfidence += confidence;
                
                const predictedWinner = expectedOutcome > 0.5 ? fighter1Id : fighter2Id;
                const correct = predictedWinner === fight.winner_id;
                
                if (correct) correctPredictions++;
                
                // Track by confidence level
                if (confidence < 0.1) {
                    predictionsByConfidence.low++;
                    if (correct) correctByConfidence.low++;
                } else if (confidence < 0.2) {
                    predictionsByConfidence.medium++;
                    if (correct) correctByConfidence.medium++;
                } else {
                    predictionsByConfidence.high++;
                    if (correct) correctByConfidence.high++;
                }
            }
            
            // Update ratings AFTER prediction
            const k1 = this.calculateDynamicK(fightCounts.get(fighter1Id), params);
            const k2 = this.calculateDynamicK(fightCounts.get(fighter2Id), params);
            
            const change1 = k1 * (actualOutcome - expectedOutcome);
            const change2 = k2 * (expectedOutcome - actualOutcome);
            
            ratings.set(fighter1Id, Math.max(this.config.ratingFloor, 
                                    Math.min(this.config.ratingCeiling, ratings.get(fighter1Id) + change1)));
            ratings.set(fighter2Id, Math.max(this.config.ratingFloor, 
                                    Math.min(this.config.ratingCeiling, ratings.get(fighter2Id) + change2)));
            
            // Update metadata
            fightCounts.set(fighter1Id, fightCounts.get(fighter1Id) + 1);
            fightCounts.set(fighter2Id, fightCounts.get(fighter2Id) + 1);
            lastFightDates.set(fighter1Id, fight.event_date);
            lastFightDates.set(fighter2Id, fight.event_date);
            
            // Update streaks
            if (actualOutcome === 1) {
                streaks.set(fighter1Id, Math.max(0, streaks.get(fighter1Id)) + 1);
                streaks.set(fighter2Id, Math.min(0, streaks.get(fighter2Id)) - 1);
            } else if (actualOutcome === 0) {
                streaks.set(fighter1Id, Math.min(0, streaks.get(fighter1Id)) - 1);
                streaks.set(fighter2Id, Math.max(0, streaks.get(fighter2Id)) + 1);
            } else {
                // Draw resets streaks
                streaks.set(fighter1Id, 0);
                streaks.set(fighter2Id, 0);
            }
        }
        
        const accuracy = totalPredictions > 0 ? correctPredictions / totalPredictions : 0;
        const avgConfidence = totalPredictions > 0 ? totalConfidence / totalPredictions : 0;
        
        return {
            accuracy,
            avgConfidence,
            totalPredictions,
            correctPredictions,
            predictionsByConfidence,
            correctByConfidence
        };
    }

    /**
     * Calculate dynamic K-factor based on experience
     */
    calculateDynamicK(fightCount, params) {
        const baseK = params.k_factor;
        if (params.experience_bonus === 0) return baseK;
        
        // Reduce K-factor for experienced fighters
        if (fightCount < 5) return baseK;
        if (fightCount < 10) return baseK * (1 - params.experience_bonus * 0.5);
        if (fightCount < 20) return baseK * (1 - params.experience_bonus * 0.75);
        return baseK * (1 - params.experience_bonus);
    }

    /**
     * Apply inactivity penalty
     */
    applyInactivityPenalty(fighterId, currentDate, ratings, lastFightDates, params) {
        if (!lastFightDates.has(fighterId)) return;
        
        const lastFight = new Date(lastFightDates.get(fighterId));
        const current = new Date(currentDate);
        const daysSince = (current - lastFight) / (1000 * 60 * 60 * 24);
        
        if (daysSince > 365) {
            const penalty = params.inactivity_penalty * (daysSince / 365);
            const currentRating = ratings.get(fighterId);
            const newRating = currentRating - (currentRating - 1500) * penalty;
            ratings.set(fighterId, Math.max(1200, newRating));
        }
    }

    /**
     * Fine-tune advanced parameters
     */
    async fineTuneAdvancedParams(fights, baseParams) {
        let bestParams = { ...baseParams };
        
        // Test experience bonus
        for (const expBonus of this.parameterSpace.experience_bonus) {
            const params = { ...baseParams, experience_bonus: expBonus };
            const result = this.testParametersTemporal(fights, params);
            
            if (result.accuracy > bestParams.accuracy) {
                bestParams = { ...params, ...result };
            }
        }
        
        // Test momentum factor
        for (const momentum of this.parameterSpace.momentum_factor) {
            const params = { ...bestParams, momentum_factor: momentum };
            const result = this.testParametersTemporal(fights, params);
            
            if (result.accuracy > bestParams.accuracy) {
                bestParams = { ...params, ...result };
            }
        }
        
        return bestParams;
    }

    /**
     * Save optimal parameters to database
     */
    async saveOptimalParameters(parameters) {
        console.log('\n💾 Saving optimal parameters...');
        
        // Update existing parameters
        const updateStmt = this.db.prepare(`
            UPDATE division_parameters 
            SET k_factor = ?, rating_scale_divisor = ?
            WHERE division = ?
        `);
        
        const updates = this.db.transaction(() => {
            for (const [division, params] of Object.entries(parameters)) {
                updateStmt.run(params.k_factor, params.rating_scale_divisor, division);
            }
        });
        updates();
        
        // Save temporal optimization results
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS temporal_optimization_results (
                division TEXT PRIMARY KEY,
                k_factor REAL,
                rating_scale_divisor REAL,
                experience_bonus REAL,
                momentum_factor REAL,
                inactivity_penalty REAL,
                temporal_accuracy REAL,
                avg_confidence REAL,
                optimization_date TEXT
            )
        `);
        
        const insertStmt = this.db.prepare(`
            INSERT OR REPLACE INTO temporal_optimization_results VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
        `);
        
        const inserts = this.db.transaction(() => {
            for (const [division, params] of Object.entries(parameters)) {
                insertStmt.run(
                    division,
                    params.k_factor,
                    params.rating_scale_divisor,
                    params.experience_bonus,
                    params.momentum_factor,
                    params.inactivity_penalty,
                    params.accuracy,
                    params.avgConfidence
                );
            }
        });
        inserts();
        
        console.log(`  ✅ Saved parameters for ${Object.keys(parameters).length} divisions`);
    }

    /**
     * Display optimization summary
     */
    displayOptimizationSummary(parameters) {
        console.log('\n' + '═'.repeat(80));
        console.log('📊 TEMPORAL OPTIMIZATION RESULTS');
        console.log('═'.repeat(80));
        
        console.log('\nDivision              | K-Factor | Scale | Exp Bonus | Momentum | Accuracy');
        console.log('─'.repeat(80));
        
        for (const [division, params] of Object.entries(parameters)) {
            console.log(
                `${division.padEnd(20)} | ${params.k_factor.toString().padStart(8)} | ` +
                `${params.rating_scale_divisor.toString().padStart(5)} | ${params.experience_bonus.toFixed(1).padStart(9)} | ` +
                `${params.momentum_factor.toFixed(2).padStart(8)} | ${(params.accuracy * 100).toFixed(1).padStart(7)}%`
            );
        }
        
        console.log('\n📈 Key Improvements:');
        console.log('• Parameters now optimized for PREDICTION, not fitting');
        console.log('• Should see improved accuracy on future fights');
        console.log('• Experience and momentum factors can help capture fighter trajectories');
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) this.db.close();
        console.log('\n🔒 Database connection closed');
    }
}

// Export the class
module.exports = TemporalParameterOptimizer;

// Run if called directly
if (require.main === module) {
    const optimizer = new TemporalParameterOptimizer();
    
    optimizer.optimizeAllDivisions()
        .then(params => {
            console.log('\n✅ Temporal optimization completed!');
        })
        .catch(error => {
            console.error('\n❌ Error:', error);
        })
        .finally(() => {
            optimizer.close();
        });
}