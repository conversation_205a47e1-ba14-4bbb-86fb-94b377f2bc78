const Database = require('better-sqlite3');
const path = require('path');

/**
 * Division-Specific Age Curves Analysis
 * 
 * Analyzes real fighter performance vs age data to create
 * division-specific age curves for the WHR system.
 * 
 * Each weight class has different prime ages and decline rates.
 */

class DivisionAgeCurveAnalyzer {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        
        console.log('📈 Division Age Curve Analyzer initialized');
    }

    /**
     * Analyze and create age curves for all divisions
     */
    async analyzeAllDivisionAgeCurves() {
        console.log('\n🔬 Analyzing age curves for all divisions...');
        console.log('═'.repeat(60));
        
        // Get all divisions
        const divisions = this.db.prepare(`
            SELECT DISTINCT weight_class as division
            FROM fights
            WHERE weight_class NOT IN ('Catch Weight', 'Open Weight')
            ORDER BY weight_class
        `).all();
        
        const results = {
            timestamp: new Date().toISOString(),
            divisions: {},
            summary: {}
        };
        
        for (const div of divisions) {
            console.log(`\n📊 Analyzing ${div.division}...`);
            
            const ageCurve = await this.analyzeDivisionAgeCurve(div.division);
            if (ageCurve) {
                results.divisions[div.division] = ageCurve;
                console.log(`  ✅ Peak age: ${ageCurve.peakAge} years`);
                console.log(`  📈 Prime range: ${ageCurve.primeStart}-${ageCurve.primeEnd} years`);
                console.log(`  📉 Decline starts: ${ageCurve.declineStart} years`);
            } else {
                console.log(`  ⚠️  Insufficient data for analysis`);
            }
        }
        
        // Generate summary
        results.summary = this.generateAgeCurveSummary(results.divisions);
        
        // Save results to database
        await this.saveAgeCurves(results);
        
        console.log('\n✅ Age curve analysis complete!');
        return results;
    }

    /**
     * Analyze age curve for a specific division
     */
    async analyzeDivisionAgeCurve(division) {
        // Get fighter performance data with ages
        const performanceData = this.db.prepare(`
            SELECT 
                f.id as fighter_id,
                f.first_name || ' ' || f.last_name as name,
                f.birthdate,
                wr.rating,
                wr.fight_count,
                wr.win_count,
                wr.loss_count,
                CASE 
                    WHEN f.birthdate IS NOT NULL 
                    THEN CAST((julianday('now') - julianday(f.birthdate)) / 365.25 AS INTEGER)
                    ELSE NULL 
                END as current_age
            FROM whr_ratings wr
            JOIN fighters f ON wr.fighter_id = f.id
            WHERE wr.division = ?
                AND f.birthdate IS NOT NULL
                AND wr.fight_count >= 3
            ORDER BY wr.rating DESC
        `).all(division);
        
        if (performanceData.length < 20) {
            console.log(`    ⚠️  Only ${performanceData.length} fighters with age data`);
            return null;
        }
        
        console.log(`    📊 Analyzing ${performanceData.length} fighters with age data`);
        
        // Get historical fight performance by age
        const fightPerformanceByAge = await this.getFightPerformanceByAge(division);
        
        // Analyze age vs performance patterns
        const ageAnalysis = this.analyzeAgePerformancePatterns(performanceData, fightPerformanceByAge);
        
        // Build age curve model
        const ageCurve = this.buildAgeCurveModel(division, ageAnalysis);
        
        return ageCurve;
    }

    /**
     * Get historical fight performance by age
     */
    async getFightPerformanceByAge(division) {
        const fightData = this.db.prepare(`
            SELECT 
                fh.fighter1_id,
                fh.fighter2_id,
                fh.fighter1_pre_rating,
                fh.fighter2_pre_rating,
                fh.fighter1_post_rating,
                fh.fighter2_post_rating,
                fh.rating_change_fighter1,
                fh.rating_change_fighter2,
                fh.actual_outcome,
                e.date as fight_date,
                f.fighter1_age,
                f.fighter2_age
            FROM whr_fight_history fh
            JOIN fights f ON fh.fight_id = f.id
            JOIN events e ON f.event_id = e.id
            WHERE fh.division = ?
                AND f.fighter1_age IS NOT NULL
                AND f.fighter2_age IS NOT NULL
            ORDER BY e.date
        `).all(division);
        
        // Use pre-calculated age data from fights table
        const agePerformanceData = [];
        
        for (const fight of fightData) {
            // Fighter 1 performance with real age data
            agePerformanceData.push({
                fighterId: fight.fighter1_id,
                age: fight.fighter1_age,
                preRating: fight.fighter1_pre_rating,
                postRating: fight.fighter1_post_rating,
                ratingChange: fight.rating_change_fighter1,
                result: fight.actual_outcome,
                fightDate: fight.fight_date
            });
            
            // Fighter 2 performance with real age data
            agePerformanceData.push({
                fighterId: fight.fighter2_id,
                age: fight.fighter2_age,
                preRating: fight.fighter2_pre_rating,
                postRating: fight.fighter2_post_rating,
                ratingChange: fight.rating_change_fighter2,
                result: 1 - fight.actual_outcome,
                fightDate: fight.fight_date
            });
        }
        
        console.log(`    📈 Analyzed ${agePerformanceData.length} fight performances with age data`);
        return agePerformanceData;
    }

    /**
     * Calculate age at specific date
     */
    calculateAgeAtDate(birthdate, eventDate) {
        const birth = new Date(birthdate);
        const event = new Date(eventDate);
        
        let age = event.getFullYear() - birth.getFullYear();
        const monthDiff = event.getMonth() - birth.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && event.getDate() < birth.getDate())) {
            age--;
        }
        
        return age;
    }

    /**
     * Analyze age vs performance patterns
     */
    analyzeAgePerformancePatterns(currentRatings, historicalPerformance) {
        // Group data by age - use age bins for decimal precision
        const ageGroups = {};
        
        // Initialize age groups with 0.5-year bins (more granular than integers)
        for (let age = 18; age <= 50; age += 0.5) {
            const ageKey = age.toFixed(1);
            ageGroups[ageKey] = {
                fighters: [],
                avgRating: 0,
                avgWinRate: 0,
                avgRatingChange: 0,
                sampleSize: 0,
                performances: [],
                minAge: age,
                maxAge: age + 0.5
            };
        }
        
        // Helper function to find age bin for decimal age
        const getAgeBin = (decimalAge) => {
            if (decimalAge < 18 || decimalAge > 50) return null;
            const binAge = Math.floor(decimalAge * 2) / 2; // Round down to nearest 0.5
            return binAge.toFixed(1);
        };
        
        // Add current ratings data
        for (const fighter of currentRatings) {
            if (fighter.current_age && fighter.current_age >= 18 && fighter.current_age <= 50) {
                const ageBin = getAgeBin(fighter.current_age);
                if (ageBin && ageGroups[ageBin]) {
                    ageGroups[ageBin].fighters.push(fighter);
                    ageGroups[ageBin].sampleSize++;
                }
            }
        }
        
        // Add historical performance data - preserve decimal precision
        for (const perf of historicalPerformance) {
            if (perf.age >= 18 && perf.age <= 50) {
                const ageBin = getAgeBin(perf.age);
                if (ageBin && ageGroups[ageBin]) {
                    ageGroups[ageBin].performances.push(perf);
                }
            }
        }
        
        // Calculate statistics for each age bin
        for (const [ageKey, group] of Object.entries(ageGroups)) {
            
            if (group.fighters.length > 0) {
                // Current rating statistics
                group.avgRating = group.fighters.reduce((sum, f) => sum + f.rating, 0) / group.fighters.length;
                group.avgWinRate = group.fighters.reduce((sum, f) => {
                    const totalFights = f.win_count + f.loss_count;
                    return sum + (totalFights > 0 ? f.win_count / totalFights : 0);
                }, 0) / group.fighters.length;
            }
            
            if (group.performances.length > 0) {
                // Historical performance statistics
                group.avgRatingChange = group.performances.reduce((sum, p) => sum + p.ratingChange, 0) / group.performances.length;
                group.avgResult = group.performances.reduce((sum, p) => sum + p.result, 0) / group.performances.length;
                group.totalPerformances = group.performances.length;
            }
        }
        
        return ageGroups;
    }

    /**
     * Build age curve model from analysis
     */
    buildAgeCurveModel(division, ageAnalysis) {
        // Find peak performance age with decimal precision
        let peakAge = 29.0;
        let bestScore = 0;
        
        // Analyze ages 26-36 for peak (realistic prime range) with 0.5-year precision
        // Use weighted score: rating * sqrt(sample_size) to avoid noise
        for (let age = 26; age <= 36; age += 0.5) {
            const ageKey = age.toFixed(1);
            const group = ageAnalysis[ageKey];
            if (group && (group.sampleSize + (group.performances ? group.performances.length : 0)) >= 5) {
                // Weight rating by square root of total sample size
                const totalSamples = group.sampleSize + (group.performances ? group.performances.length : 0);
                const score = group.avgRating * Math.sqrt(totalSamples);
                if (score > bestScore) {
                    bestScore = score;
                    peakAge = age;
                }
            }
        }
        
        // Get peak rating
        const peakGroup = ageAnalysis[peakAge.toFixed(1)];
        const peakRating = peakGroup ? peakGroup.avgRating : 1500;
        
        // Find prime range (within 90% of peak performance) with decimal precision
        const primeThreshold = peakRating * 0.9;
        let primeStart = peakAge;
        let primeEnd = peakAge;
        
        // Expand prime range backwards
        for (let age = peakAge - 0.5; age >= 22; age -= 0.5) {
            const ageKey = age.toFixed(1);
            const group = ageAnalysis[ageKey];
            if (group && (group.sampleSize + (group.performances ? group.performances.length : 0)) >= 2 && group.avgRating >= primeThreshold) {
                primeStart = age;
            } else {
                break;
            }
        }
        
        // Expand prime range forwards
        for (let age = peakAge + 0.5; age <= 38; age += 0.5) {
            const ageKey = age.toFixed(1);
            const group = ageAnalysis[ageKey];
            if (group && (group.sampleSize + (group.performances ? group.performances.length : 0)) >= 2 && group.avgRating >= primeThreshold) {
                primeEnd = age;
            } else {
                break;
            }
        }
        
        // Find decline start (significant drop from peak)
        let declineStart = primeEnd + 0.5;
        const declineThreshold = peakRating * 0.85;
        
        for (let age = primeEnd + 0.5; age <= 40; age += 0.5) {
            const ageKey = age.toFixed(1);
            const group = ageAnalysis[ageKey];
            if (group && (group.sampleSize + (group.performances ? group.performances.length : 0)) >= 2 && group.avgRating < declineThreshold) {
                declineStart = age;
                break;
            }
        }
        
        // Calculate age curve parameters
        const ageCurve = {
            division,
            peakAge,
            peakRating,
            primeStart,
            primeEnd,
            declineStart,
            ageFactors: {},
            sampleSizes: {},
            confidence: this.calculateCurveConfidence(ageAnalysis),
            createdAt: new Date().toISOString()
        };
        
        // Calculate age factors for each age bin with decimal precision
        for (let age = 18; age <= 50; age += 0.5) {
            const ageKey = age.toFixed(1);
            const group = ageAnalysis[ageKey];
            
            if (group) {
                const totalSamples = group.sampleSize + (group.performances ? group.performances.length : 0);
                ageCurve.sampleSizes[ageKey] = totalSamples;
                
                if (group.sampleSize >= 1 || (group.performances ? group.performances.length : 0) >= 3) {
                    // Factor relative to peak
                    const ageFactor = group.avgRating > 0 ? group.avgRating / peakRating : 0.8;
                    ageCurve.ageFactors[ageKey] = Math.max(0.7, Math.min(1.1, ageFactor));
                } else {
                    // Default curve for insufficient data
                    ageCurve.ageFactors[ageKey] = this.getDefaultAgeFactor(age, peakAge);
                }
            }
        }
        
        return ageCurve;
    }

    /**
     * Get default age factor when insufficient data
     */
    getDefaultAgeFactor(age, peakAge) {
        if (age < 22) {
            // Young fighters - still developing
            return 0.85 + (0.1 * (age - 18) / 4);
        } else if (age >= 22 && age <= peakAge + 3) {
            // Prime years
            const distanceFromPeak = Math.abs(age - peakAge);
            return 1.0 - (distanceFromPeak * 0.02);
        } else {
            // Decline years
            const yearsAfterPrime = age - (peakAge + 3);
            return Math.max(0.7, 0.98 - (yearsAfterPrime * 0.04));
        }
    }

    /**
     * Calculate confidence in the age curve
     */
    calculateCurveConfidence(ageAnalysis) {
        let totalSamples = 0;
        let agesWithData = 0;
        
        for (let age = 22; age <= 38; age += 0.5) {
            const ageKey = age.toFixed(1);
            const group = ageAnalysis[ageKey];
            if (group) {
                const samples = group.sampleSize + (group.performances ? group.performances.length : 0);
                totalSamples += samples;
                if (samples >= 2) {
                    agesWithData++;
                }
            }
        }
        
        if (totalSamples >= 50 && agesWithData >= 10) return 'High';
        if (totalSamples >= 30 && agesWithData >= 8) return 'Medium';
        if (totalSamples >= 15 && agesWithData >= 5) return 'Low';
        return 'Very Low';
    }

    /**
     * Generate summary across all divisions
     */
    generateAgeCurveSummary(divisionCurves) {
        const summary = {
            totalDivisions: Object.keys(divisionCurves).length,
            avgPeakAge: 0,
            peakAgeRange: { min: 45, max: 18 },
            divisionVariance: {},
            confidence: {}
        };
        
        const peakAges = [];
        
        for (const [division, curve] of Object.entries(divisionCurves)) {
            peakAges.push(curve.peakAge);
            summary.peakAgeRange.min = Math.min(summary.peakAgeRange.min, curve.peakAge);
            summary.peakAgeRange.max = Math.max(summary.peakAgeRange.max, curve.peakAge);
            summary.confidence[division] = curve.confidence;
        }
        
        if (peakAges.length > 0) {
            summary.avgPeakAge = peakAges.reduce((a, b) => a + b, 0) / peakAges.length;
        }
        
        return summary;
    }

    /**
     * Save age curves to database
     */
    async saveAgeCurves(results) {
        console.log('\n💾 Saving age curves to database...');
        
        // Create age curves table
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS division_age_curves (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                division VARCHAR(50) UNIQUE,
                peak_age INTEGER,
                peak_rating DECIMAL(10,2),
                prime_start INTEGER,
                prime_end INTEGER,
                decline_start INTEGER,
                age_factors TEXT,
                sample_sizes TEXT,
                confidence VARCHAR(20),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);
        
        // Clear existing data
        this.db.exec('DELETE FROM division_age_curves');
        
        // Insert new age curves
        const insertStmt = this.db.prepare(`
            INSERT INTO division_age_curves (
                division, peak_age, peak_rating, prime_start, prime_end, 
                decline_start, age_factors, sample_sizes, confidence
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        const inserts = this.db.transaction(() => {
            for (const [division, curve] of Object.entries(results.divisions)) {
                insertStmt.run(
                    division,
                    curve.peakAge,
                    curve.peakRating,
                    curve.primeStart,
                    curve.primeEnd,
                    curve.declineStart,
                    JSON.stringify(curve.ageFactors),
                    JSON.stringify(curve.sampleSizes),
                    curve.confidence
                );
            }
        });
        
        inserts();
        
        // Save full results to file
        const fs = require('fs');
        const outputPath = path.join(__dirname, '..', 'analysis-output', 'division-age-curves-analysis.json');
        
        // Ensure directory exists
        const outputDir = path.dirname(outputPath);
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        fs.writeFileSync(outputPath, JSON.stringify(results, null, 2));
        
        console.log(`  ✅ Saved ${Object.keys(results.divisions).length} division age curves`);
        console.log(`  📄 Full analysis saved to: ${outputPath}`);
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) this.db.close();
        console.log('🔒 Database connection closed');
    }
}

// Export the class
module.exports = DivisionAgeCurveAnalyzer;

// Run if called directly
if (require.main === module) {
    console.log('📈 Division Age Curve Analysis - Complete Analysis');
    console.log('═'.repeat(60));
    
    const analyzer = new DivisionAgeCurveAnalyzer();
    
    analyzer.analyzeAllDivisionAgeCurves()
        .then((results) => {
            console.log('\n📋 Age Curve Analysis Summary:');
            console.log(`  Total Divisions: ${results.summary.totalDivisions}`);
            console.log(`  Average Peak Age: ${results.summary.avgPeakAge.toFixed(1)} years`);
            console.log(`  Peak Age Range: ${results.summary.peakAgeRange.min}-${results.summary.peakAgeRange.max} years`);
            
            console.log('\n📊 Division-Specific Results:');
            for (const [division, curve] of Object.entries(results.divisions)) {
                console.log(`  ${division}: Peak ${curve.peakAge}yr, Prime ${curve.primeStart}-${curve.primeEnd}yr (${curve.confidence} confidence)`);
            }
            
            console.log('\n✅ Age curve analysis completed successfully!');
        })
        .catch(error => {
            console.error('❌ Error:', error);
        })
        .finally(() => {
            analyzer.close();
        });
}