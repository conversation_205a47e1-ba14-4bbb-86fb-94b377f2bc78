const Database = require('better-sqlite3');
const path = require('path');

const db = new Database(path.join(__dirname, '..', 'data', 'ufc_data.db'));

console.log('Checking database schema...\n');

// Get all tables
const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name").all();
console.log('Tables in database:');
tables.forEach(table => console.log(`  - ${table.name}`));

// Check fights table schema
console.log('\n\nFIGHTS table schema:');
const fightsSchema = db.prepare("PRAGMA table_info(fights)").all();
fightsSchema.forEach(col => {
    console.log(`  ${col.name} (${col.type})`);
});

// Check whr_ratings table schema
console.log('\n\nWHR_RATINGS table schema:');
const whrRatingsSchema = db.prepare("PRAGMA table_info(whr_ratings)").all();
whrRatingsSchema.forEach(col => {
    console.log(`  ${col.name} (${col.type})`);
});

// Check whr_fight_results table schema
console.log('\n\nWHR_FIGHT_RESULTS table schema:');
const whrFightResultsSchema = db.prepare("PRAGMA table_info(whr_fight_results)").all();
whrFightResultsSchema.forEach(col => {
    console.log(`  ${col.name} (${col.type})`);
});

db.close();