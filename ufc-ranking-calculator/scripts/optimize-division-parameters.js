const Database = require('better-sqlite3');
const path = require('path');

/**
 * Optimize Division-Specific Parameters
 * 
 * This script finds the best K-factor and rating scale divisor for each division
 * by testing different values and measuring prediction accuracy
 */

class DivisionParameterOptimizer {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        
        // Parameter search ranges
        this.kFactorRange = [16, 20, 24, 28, 32, 36, 40, 44, 48];
        this.scaleDivisorRange = [300, 350, 400, 450, 500];
        
        // Keep initial rating at 1500 for all divisions
        this.initialRating = 1500;
        
        console.log('🔧 Division Parameter Optimizer initialized');
    }

    /**
     * Main optimization method
     */
    async optimizeAllDivisions() {
        console.log('🚀 Starting division parameter optimization...');
        console.log('═'.repeat(60));
        
        // Get all divisions (excluding Catch Weight)
        const divisions = this.db.prepare(`
            SELECT DISTINCT weight_class 
            FROM fights 
            WHERE weight_class NOT IN ('Catch Weight', 'Open Weight')
            ORDER BY weight_class
        `).all().map(r => r.weight_class);
        
        const optimalParameters = {};
        
        for (const division of divisions) {
            console.log(`\n📊 Optimizing ${division}...`);
            const bestParams = await this.optimizeDivision(division);
            optimalParameters[division] = bestParams;
            
            console.log(`  ✅ Best parameters:`);
            console.log(`     K-factor: ${bestParams.k_factor}`);
            console.log(`     Scale divisor: ${bestParams.rating_scale_divisor}`);
            console.log(`     Accuracy: ${(bestParams.accuracy * 100).toFixed(1)}%`);
        }
        
        // Save optimal parameters
        await this.saveOptimalParameters(optimalParameters);
        
        console.log('\n✅ Optimization complete!');
        return optimalParameters;
    }

    /**
     * Optimize parameters for a single division
     */
    async optimizeDivision(division) {
        // Get all fights for this division
        const fights = this.db.prepare(`
            SELECT 
                f.id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                e.date as event_date
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class = ?
            ORDER BY e.date ASC
        `).all(division);
        
        console.log(`  Found ${fights.length} fights`);
        
        let bestParams = {
            k_factor: 32,
            rating_scale_divisor: 400,
            accuracy: 0
        };
        
        // Test different parameter combinations
        for (const kFactor of this.kFactorRange) {
            for (const scaleDivisor of this.scaleDivisorRange) {
                const accuracy = this.testParameters(fights, kFactor, scaleDivisor);
                
                if (accuracy > bestParams.accuracy) {
                    bestParams = {
                        k_factor: kFactor,
                        rating_scale_divisor: scaleDivisor,
                        accuracy: accuracy
                    };
                }
            }
        }
        
        return bestParams;
    }

    /**
     * Test specific parameters on a set of fights
     */
    testParameters(fights, kFactor, scaleDivisor) {
        // Initialize ratings
        const ratings = new Map();
        
        let correctPredictions = 0;
        let totalPredictions = 0;
        
        // Process each fight
        for (const fight of fights) {
            const fighter1Key = fight.fighter1_id;
            const fighter2Key = fight.fighter2_id;
            
            // Get or initialize ratings
            if (!ratings.has(fighter1Key)) {
                ratings.set(fighter1Key, this.initialRating);
            }
            if (!ratings.has(fighter2Key)) {
                ratings.set(fighter2Key, this.initialRating);
            }
            
            const rating1 = ratings.get(fighter1Key);
            const rating2 = ratings.get(fighter2Key);
            
            // Calculate expected outcome
            const ratingDiff = rating1 - rating2;
            const expectedOutcome = 1 / (1 + Math.pow(10, -ratingDiff / scaleDivisor));
            
            // Check prediction accuracy (skip draws)
            if (!fight.result_method || !fight.result_method.toLowerCase().includes('draw')) {
                totalPredictions++;
                
                const predictedWinner = expectedOutcome > 0.5 ? fight.fighter1_id : fight.fighter2_id;
                if (predictedWinner === fight.winner_id) {
                    correctPredictions++;
                }
            }
            
            // Update ratings
            let actualOutcome;
            if (fight.result_method && fight.result_method.toLowerCase().includes('draw')) {
                actualOutcome = 0.5;
            } else if (fight.winner_id === fight.fighter1_id) {
                actualOutcome = 1;
            } else {
                actualOutcome = 0;
            }
            
            const change1 = kFactor * (actualOutcome - expectedOutcome);
            const change2 = kFactor * (expectedOutcome - actualOutcome);
            
            ratings.set(fighter1Key, Math.max(800, Math.min(2500, rating1 + change1)));
            ratings.set(fighter2Key, Math.max(800, Math.min(2500, rating2 + change2)));
        }
        
        return totalPredictions > 0 ? correctPredictions / totalPredictions : 0;
    }

    /**
     * Save optimal parameters to database
     */
    async saveOptimalParameters(parameters) {
        console.log('\n💾 Saving optimal parameters...');
        
        // Clear existing parameters
        this.db.exec('DELETE FROM division_parameters');
        
        // Insert new parameters
        const stmt = this.db.prepare(`
            INSERT INTO division_parameters (
                division, k_factor, initial_rating, rating_scale_divisor,
                created_at
            ) VALUES (?, ?, ?, ?, datetime('now'))
        `);
        
        const inserts = this.db.transaction(() => {
            for (const [division, params] of Object.entries(parameters)) {
                stmt.run(
                    division,
                    params.k_factor,
                    this.initialRating,
                    params.rating_scale_divisor
                );
            }
        });
        inserts();
        
        console.log(`  ✅ Saved parameters for ${Object.keys(parameters).length} divisions`);
    }

    /**
     * Display summary
     */
    displaySummary(parameters) {
        console.log('\n📊 Optimal Parameters Summary');
        console.log('═'.repeat(60));
        console.log('Division              | K-Factor | Scale | Accuracy');
        console.log('─'.repeat(60));
        
        for (const [division, params] of Object.entries(parameters)) {
            console.log(
                `${division.padEnd(20)} | ${String(params.k_factor).padStart(8)} | ` +
                `${String(params.rating_scale_divisor).padStart(5)} | ${(params.accuracy * 100).toFixed(1)}%`
            );
        }
        
        console.log('═'.repeat(60));
    }

    /**
     * Close database connection
     */
    close() {
        if (this.db) this.db.close();
        console.log('🔒 Database connection closed');
    }
}

// Export the class
module.exports = DivisionParameterOptimizer;

// Run if called directly
if (require.main === module) {
    console.log('🎯 Division Parameter Optimization');
    console.log('═'.repeat(60));
    
    const optimizer = new DivisionParameterOptimizer();
    
    optimizer.optimizeAllDivisions()
        .then(parameters => {
            optimizer.displaySummary(parameters);
            console.log('\n✅ Optimization completed successfully!');
        })
        .catch(error => {
            console.error('❌ Error:', error);
        })
        .finally(() => {
            optimizer.close();
        });
}