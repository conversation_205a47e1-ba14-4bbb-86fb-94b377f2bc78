const Database = require('better-sqlite3');
const path = require('path');

const db = new Database(path.join(__dirname, '..', 'data', 'ufc_data.db'));

// First, let's check what fights were NOT processed by WHR
console.log('Investigating unprocessed fights...\n');

// Get fights that don't have WHR ratings
const unprocessedQuery = `
    SELECT 
        f.id as fight_id,
        f.event_id,
        e.event_name,
        e.date as event_date,
        f.fighter1_id,
        f1.first_name || ' ' || f1.last_name as fighter1_name,
        f.fighter2_id,
        f2.first_name || ' ' || f2.last_name as fighter2_name,
        f.weight_class,
        f.fight_status,
        CASE 
            WHEN f.winner_id = f.fighter1_id THEN 'Fighter1'
            WHEN f.winner_id = f.fighter2_id THEN 'Fighter2'
            WHEN f.winner_id IS NULL THEN 'Draw/NC'
            ELSE 'Unknown'
        END as result,
        f.result_method,
        f.result_round,
        f.result_time
    FROM fights f
    JOIN events e ON f.event_id = e.id
    JOIN fighters f1 ON f.fighter1_id = f1.id
    JOIN fighters f2 ON f.fighter2_id = f2.id
    LEFT JOIN whr_ratings wr1 ON f.fighter1_id = wr1.fighter_id
    LEFT JOIN whr_ratings wr2 ON f.fighter2_id = wr2.fighter_id
    WHERE wr1.fighter_id IS NULL OR wr2.fighter_id IS NULL
    ORDER BY e.date DESC
    LIMIT 50;
`;

const unprocessedFights = db.prepare(unprocessedQuery).all();

console.log(`Found ${unprocessedFights.length} unprocessed fights (showing up to 50)\n`);

// Analyze patterns in unprocessed fights
const patterns = {
    weight_class: {},
    fight_status: {},
    result_method: {},
    result: {}
};

unprocessedFights.forEach(fight => {
    patterns.weight_class[fight.weight_class] = (patterns.weight_class[fight.weight_class] || 0) + 1;
    patterns.fight_status[fight.fight_status] = (patterns.fight_status[fight.fight_status] || 0) + 1;
    patterns.result_method[fight.result_method] = (patterns.result_method[fight.result_method] || 0) + 1;
    patterns.result[fight.result] = (patterns.result[fight.result] || 0) + 1;
});

console.log('PATTERNS IN UNPROCESSED FIGHTS:');
console.log('\nWeight Classes:');
Object.entries(patterns.weight_class).sort((a,b) => b[1] - a[1]).forEach(([key, count]) => {
    console.log(`  ${key}: ${count} fights`);
});

console.log('\nFight Status:');
Object.entries(patterns.fight_status).sort((a,b) => b[1] - a[1]).forEach(([key, count]) => {
    console.log(`  ${key}: ${count} fights`);
});

console.log('\nResult Method:');
Object.entries(patterns.result_method).sort((a,b) => b[1] - a[1]).forEach(([key, count]) => {
    console.log(`  ${key}: ${count} fights`);
});

console.log('\nResult:');
Object.entries(patterns.result).sort((a,b) => b[1] - a[1]).forEach(([key, count]) => {
    console.log(`  ${key}: ${count} fights`);
});

// Show sample of unprocessed fights
console.log('\n\nSAMPLE UNPROCESSED FIGHTS:');
unprocessedFights.slice(0, 20).forEach(fight => {
    console.log(`\n${fight.event_name} (${fight.event_date})`);
    console.log(`  ${fight.fighter1_name} vs ${fight.fighter2_name}`);
    console.log(`  Weight Class: ${fight.weight_class}`);
    console.log(`  Status: ${fight.fight_status}`);
    console.log(`  Result: ${fight.result} via ${fight.result_method}`);
});

// Check WHR processing criteria
console.log('\n\nCHECKING WHR PROCESSING CRITERIA:');

// Look at the WHR algorithm to understand filtering
const whrFilterQuery = `
    SELECT DISTINCT weight_class, fight_status, result_method
    FROM fights
    WHERE weight_class LIKE '%Catch%' 
       OR result_method = 'No Contest'
       OR fight_status != 'Completed'
    ORDER BY weight_class, fight_status, result_method;
`;

const filteredFights = db.prepare(whrFilterQuery).all();
console.log('\nFights that might be filtered out by WHR:');
filteredFights.forEach(fight => {
    console.log(`  ${fight.weight_class} | ${fight.fight_status} | ${fight.result_method}`);
});

// Count total fights vs processed fights
const totalFightsQuery = `SELECT COUNT(*) as count FROM fights`;
const processedFightsQuery = `SELECT COUNT(DISTINCT fight_id) as count FROM whr_fight_results`;

const totalFights = db.prepare(totalFightsQuery).get().count;
const processedFights = db.prepare(processedFightsQuery).get().count;

console.log(`\n\nSUMMARY:`);
console.log(`Total fights in database: ${totalFights}`);
console.log(`Fights processed by WHR: ${processedFights}`);
console.log(`Unprocessed fights: ${totalFights - processedFights}`);

db.close();