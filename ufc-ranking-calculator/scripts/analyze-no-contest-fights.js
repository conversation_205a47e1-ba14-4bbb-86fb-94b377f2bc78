const Database = require('better-sqlite3');
const path = require('path');

/**
 * Analyze No Contest fights to identify marijuana-related cases
 * These should be excluded from ratings but included in performance statistics
 */

const dbPath = path.join(__dirname, '..', 'data', 'ufc_data.db');
const db = new Database(dbPath);

console.log('🔍 Analyzing No Contest and Disqualification fights...');

try {
    // Get all No Contest fights with detailed information
    const noContests = db.prepare(`
        SELECT 
            f.id,
            f.result_method,
            f.weight_class,
            e.date,
            e.event_name,
            f1.first_name || ' ' || f1.last_name as fighter1,
            f2.first_name || ' ' || f2.last_name as fighter2
        FROM fights f
        JOIN events e ON f.event_id = e.id
        JOIN fighters f1 ON f.fighter1_id = f1.id
        JOIN fighters f2 ON f.fighter2_id = f2.id
        WHERE f.result_method LIKE '%No Contest%' 
           OR f.result_method LIKE '%NC%'
        ORDER BY e.date DESC
    `).all();
    
    console.log(`\n📊 Found ${noContests.length} No Contest fights`);
    
    // Analyze patterns in No Contest fights
    let marijuanaRelated = 0;
    let eyePokes = 0;
    let other = 0;
    
    console.log('\n📋 Recent No Contest fights:');
    noContests.slice(0, 10).forEach(fight => {
        console.log(`  ${fight.date}: ${fight.fighter1} vs ${fight.fighter2}`);
        console.log(`    Method: ${fight.result_method}`);
        console.log(`    Event: ${fight.event_name}`);
        console.log();
        
        // Categorize (this would need manual review or better data)
        const method = fight.result_method.toLowerCase();
        
        if (method.includes('marijuana') || method.includes('drug')) {
            marijuanaRelated++;
        } else if (method.includes('eye')) {
            eyePokes++;
        } else {
            other++;
        }
    });
    
    // Get DQ fights
    const dqFights = db.prepare(`
        SELECT 
            f.id,
            f.result_method,
            f.weight_class,
            e.date,
            e.event_name,
            f1.first_name || ' ' || f1.last_name as fighter1,
            f2.first_name || ' ' || f2.last_name as fighter2
        FROM fights f
        JOIN events e ON f.event_id = e.id
        JOIN fighters f1 ON f.fighter1_id = f1.id
        JOIN fighters f2 ON f.fighter2_id = f2.id
        WHERE f.result_method LIKE '%DQ%' 
           OR f.result_method LIKE '%Disqualif%'
        ORDER BY e.date DESC
        LIMIT 10
    `).all();
    
    console.log(`\n⚠️  Recent Disqualification fights (${dqFights.length} total):`);
    dqFights.forEach(fight => {
        console.log(`  ${fight.date}: ${fight.fighter1} vs ${fight.fighter2} - ${fight.result_method}`);
    });
    
    // Check current WHR processing of these fights
    const ncInWHR = db.prepare(`
        SELECT COUNT(*) as count
        FROM whr_fight_history fh
        JOIN fights f ON fh.fight_id = f.id
        WHERE f.result_method LIKE '%No Contest%' 
           OR f.result_method LIKE '%NC%'
           OR f.result_method LIKE '%DQ%'
    `).get();
    
    console.log(`\n🔍 Edge case analysis:`);
    console.log(`  No Contest fights: ${noContests.length}`);
    console.log(`  DQ fights: ${dqFights.length}`);
    console.log(`  Currently in WHR history: ${ncInWHR.count}`);
    
    if (ncInWHR.count > 0) {
        console.log(`  ⚠️  WARNING: ${ncInWHR.count} edge case fights are in WHR calculations!`);
    } else {
        console.log(`  ✅ Edge case fights are properly excluded from WHR calculations`);
    }
    
    // Recommendations
    console.log(`\n💡 Recommendations:`);
    console.log(`  1. All ${noContests.length + dqFights.length} NC/DQ fights should be excluded from ratings`);
    console.log(`  2. Consider creating separate table for performance stats from excluded fights`);
    console.log(`  3. Manual review needed to identify marijuana-related NCs for stats inclusion`);
    
    // Check if fights stats table exists for these
    const statsFromExcluded = db.prepare(`
        SELECT COUNT(*) as count
        FROM ufc_fight_stats ufs
        JOIN fights f ON ufs.fight_id = f.id
        WHERE f.result_method LIKE '%No Contest%' 
           OR f.result_method LIKE '%NC%'
           OR f.result_method LIKE '%DQ%'
    `).get();
    
    if (statsFromExcluded.count > 0) {
        console.log(`  4. ${statsFromExcluded.count} excluded fights have performance stats available`);
    }
    
} catch (error) {
    console.error('❌ Error analyzing fights:', error);
} finally {
    db.close();
    console.log('\n🔒 Database connection closed');
}