const Database = require('better-sqlite3');
const path = require('path');

/**
 * Investigate Poor Performing Divisions
 * Analyze why certain divisions have terrible prediction accuracy
 */

class PoorDivisionInvestigator {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
    }

    async investigate() {
        console.log('🔍 INVESTIGATING POOR PERFORMING DIVISIONS');
        console.log('═'.repeat(80));
        
        const poorDivisions = ['Women\'s Strawweight', 'Light Heavyweight', 'Bantamweight'];
        
        for (const division of poorDivisions) {
            console.log(`\n📊 ${division.toUpperCase()}`);
            console.log('─'.repeat(60));
            
            await this.analyzeDivision(division);
        }
        
        // Compare with a good division
        console.log('\n📊 FLYWEIGHT (Good Division for Comparison)');
        console.log('─'.repeat(60));
        await this.analyzeDivision('Flyweight');
    }

    async analyzeDivision(division) {
        // Get division parameters
        const params = this.db.prepare(`
            SELECT * FROM division_parameters WHERE division = ?
        `).get(division);
        
        console.log(`Parameters: K=${params.k_factor}, Scale=${params.rating_scale_divisor}`);
        
        // Get recent fights with details
        const recentFights = this.db.prepare(`
            SELECT 
                f.id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f1.first_name || ' ' || f1.last_name as fighter1_name,
                f2.first_name || ' ' || f2.last_name as fighter2_name,
                e.date,
                e.event_name
            FROM fights f
            JOIN events e ON f.event_id = e.id
            JOIN fighters f1 ON f.fighter1_id = f1.id
            JOIN fighters f2 ON f.fighter2_id = f2.id
            WHERE f.weight_class = ?
            AND e.date >= '2024-01-01'
            AND f.result_method NOT LIKE '%Draw%'
            ORDER BY e.date DESC
            LIMIT 10
        `).all(division);
        
        // Get all fights to build ratings
        const allFights = this.db.prepare(`
            SELECT 
                f.id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                e.date as event_date
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class = ?
            ORDER BY e.date ASC
        `).all(division);
        
        // Build ratings up to recent fights
        const ratings = new Map();
        const fightCounts = new Map();
        
        for (const fight of allFights) {
            // Initialize if needed
            if (!ratings.has(fight.fighter1_id)) {
                ratings.set(fight.fighter1_id, params.initial_rating);
                fightCounts.set(fight.fighter1_id, 0);
            }
            if (!ratings.has(fight.fighter2_id)) {
                ratings.set(fight.fighter2_id, params.initial_rating);
                fightCounts.set(fight.fighter2_id, 0);
            }
            
            const rating1 = ratings.get(fight.fighter1_id);
            const rating2 = ratings.get(fight.fighter2_id);
            
            // Update ratings
            const ratingDiff = rating1 - rating2;
            const expectedOutcome = 1 / (1 + Math.pow(10, -ratingDiff / params.rating_scale_divisor));
            
            let actualOutcome;
            if (fight.result_method && fight.result_method.toLowerCase().includes('draw')) {
                actualOutcome = 0.5;
            } else if (fight.winner_id === fight.fighter1_id) {
                actualOutcome = 1;
            } else {
                actualOutcome = 0;
            }
            
            const k = params.k_factor;
            const change1 = k * (actualOutcome - expectedOutcome);
            const change2 = k * (expectedOutcome - actualOutcome);
            
            ratings.set(fight.fighter1_id, rating1 + change1);
            ratings.set(fight.fighter2_id, rating2 + change2);
            
            fightCounts.set(fight.fighter1_id, fightCounts.get(fight.fighter1_id) + 1);
            fightCounts.set(fight.fighter2_id, fightCounts.get(fight.fighter2_id) + 1);
        }
        
        // Analyze recent fight predictions
        console.log('\nRecent Fight Analysis:');
        let wrongPredictions = 0;
        let closeDecisions = 0;
        let bigUpsets = 0;
        
        for (const fight of recentFights.slice(0, 5)) {
            const rating1 = ratings.get(fight.fighter1_id) || params.initial_rating;
            const rating2 = ratings.get(fight.fighter2_id) || params.initial_rating;
            const count1 = fightCounts.get(fight.fighter1_id) || 0;
            const count2 = fightCounts.get(fight.fighter2_id) || 0;
            
            const ratingDiff = rating1 - rating2;
            const expectedOutcome = 1 / (1 + Math.pow(10, -ratingDiff / params.rating_scale_divisor));
            const predictedWinner = expectedOutcome > 0.5 ? fight.fighter1_id : fight.fighter2_id;
            const actualWinner = fight.winner_id;
            const correct = predictedWinner === actualWinner;
            
            if (!correct) wrongPredictions++;
            if (Math.abs(expectedOutcome - 0.5) < 0.1) closeDecisions++;
            if (!correct && Math.abs(expectedOutcome - 0.5) > 0.2) bigUpsets++;
            
            console.log(`\n${fight.date}: ${fight.fighter1_name} vs ${fight.fighter2_name}`);
            console.log(`  Ratings: ${rating1.toFixed(0)} (${count1} fights) vs ${rating2.toFixed(0)} (${count2} fights)`);
            console.log(`  Win prob: ${(expectedOutcome * 100).toFixed(1)}% vs ${((1-expectedOutcome) * 100).toFixed(1)}%`);
            console.log(`  Predicted: ${predictedWinner === fight.fighter1_id ? fight.fighter1_name : fight.fighter2_name}`);
            console.log(`  Actual: ${actualWinner === fight.fighter1_id ? fight.fighter1_name : fight.fighter2_name}`);
            console.log(`  Result: ${correct ? '✅' : '❌'}`);
        }
        
        // Division statistics
        console.log('\nDivision Characteristics:');
        const ratingsList = Array.from(ratings.values());
        const avgRating = ratingsList.reduce((a, b) => a + b, 0) / ratingsList.length;
        const minRating = Math.min(...ratingsList);
        const maxRating = Math.max(...ratingsList);
        const stdDev = Math.sqrt(ratingsList.reduce((sum, r) => sum + Math.pow(r - avgRating, 2), 0) / ratingsList.length);
        
        console.log(`  Total fighters: ${ratings.size}`);
        console.log(`  Avg rating: ${avgRating.toFixed(0)}`);
        console.log(`  Rating range: ${minRating.toFixed(0)} - ${maxRating.toFixed(0)}`);
        console.log(`  Std deviation: ${stdDev.toFixed(0)}`);
        console.log(`  Rating spread: ${(maxRating - minRating).toFixed(0)}`);
        
        // Problem indicators
        console.log('\nPotential Issues:');
        if (stdDev < 100) {
            console.log('  ⚠️  Low rating variance - ratings too compressed');
        }
        if (params.k_factor < 20) {
            console.log('  ⚠️  K-factor too low - ratings change too slowly');
        }
        if (params.k_factor > 50) {
            console.log('  ⚠️  K-factor too high - ratings too volatile');
        }
        if (closeDecisions > 2) {
            console.log('  ⚠️  Many close decisions - division has parity');
        }
        if (bigUpsets > 1) {
            console.log('  ⚠️  Multiple big upsets - ratings not capturing skill well');
        }
        
        // Fighter turnover analysis
        const activeFighters = Array.from(fightCounts.entries())
            .filter(([id, count]) => count > 2)
            .length;
        const inactiveFighters = ratings.size - activeFighters;
        
        console.log(`\nFighter Activity:`);
        console.log(`  Active (3+ fights): ${activeFighters}`);
        console.log(`  Inactive (<3 fights): ${inactiveFighters}`);
        
        if (inactiveFighters / ratings.size > 0.5) {
            console.log('  ⚠️  High fighter turnover - many one-time fighters');
        }
    }

    close() {
        if (this.db) this.db.close();
    }
}

// Run investigation
if (require.main === module) {
    const investigator = new PoorDivisionInvestigator();
    
    investigator.investigate()
        .then(() => {
            console.log('\n✅ Investigation completed!');
        })
        .catch(error => {
            console.error('❌ Error:', error);
        })
        .finally(() => {
            investigator.close();
        });
}