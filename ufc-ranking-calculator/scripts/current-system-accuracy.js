const Database = require('better-sqlite3');
const path = require('path');

/**
 * Current System Accuracy
 * Tests how well the system predicts RECENT fights (last 20% of each division)
 * This represents the system's current predictive ability
 */

class CurrentSystemAccuracy {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
    }

    async testCurrentAccuracy() {
        console.log('🎯 CURRENT SYSTEM PREDICTIVE ACCURACY');
        console.log('Testing on recent fights (last 20% per division)');
        console.log('═'.repeat(80));
        
        // Get all divisions with parameters
        const divisions = this.db.prepare(`
            SELECT DISTINCT dp.division, dp.k_factor, dp.rating_scale_divisor, dp.initial_rating
            FROM division_parameters dp
            WHERE dp.division NOT IN ('Catch Weight', 'Open Weight')
            ORDER BY dp.division
        `).all();
        
        const results = [];
        let totalRecentCorrect = 0;
        let totalRecentPredictions = 0;
        
        for (const divParams of divisions) {
            const divisionResult = await this.testDivision(divParams);
            results.push(divisionResult);
            totalRecentCorrect += divisionResult.recentCorrect;
            totalRecentPredictions += divisionResult.recentTotal;
        }
        
        // Display results
        console.log('\nDivision              | Total | Recent | Accuracy | Confident | High Conf');
        console.log('─'.repeat(80));
        
        for (const result of results) {
            console.log(
                `${result.division.padEnd(20)} | ${result.totalFights.toString().padStart(5)} | ` +
                `${result.recentTotal.toString().padStart(6)} | ${result.recentAccuracy.padStart(7)} | ` +
                `${result.confidentAccuracy.padStart(9)} | ${result.highConfAccuracy.padStart(9)}`
            );
        }
        
        console.log('─'.repeat(80));
        
        const overallCurrentAccuracy = (totalRecentCorrect / totalRecentPredictions * 100).toFixed(1);
        console.log(`${'OVERALL CURRENT'.padEnd(20)} |       | ${totalRecentPredictions.toString().padStart(6)} | ${overallCurrentAccuracy.padStart(7)}%`);
        
        console.log('\n📊 WHAT THIS MEANS:');
        console.log(`• Your system currently predicts fights with ${overallCurrentAccuracy}% accuracy`);
        console.log('• This is based on the most recent 20% of fights per division');
        console.log('• This represents how well it would predict TODAY\'s fights');
        
        // Test on very recent fights (2024-2025)
        await this.testVeryRecentFights();
    }

    async testDivision(params) {
        // Get all fights for this division
        const fights = this.db.prepare(`
            SELECT 
                f.id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                e.date as event_date
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class = ?
            ORDER BY e.date ASC, f.id ASC
        `).all(params.division);
        
        // Calculate cutoff for "recent" fights (last 20%)
        const cutoffIndex = Math.floor(fights.length * 0.8);
        
        // Initialize ratings
        const ratings = new Map();
        const fightCounts = new Map();
        
        let recentCorrect = 0;
        let recentTotal = 0;
        let confidentCorrect = 0;
        let confidentTotal = 0;
        let highConfCorrect = 0;
        let highConfTotal = 0;
        
        // Process all fights
        for (let i = 0; i < fights.length; i++) {
            const fight = fights[i];
            const isRecent = i >= cutoffIndex;
            
            // Initialize if needed
            if (!ratings.has(fight.fighter1_id)) {
                ratings.set(fight.fighter1_id, params.initial_rating);
                fightCounts.set(fight.fighter1_id, 0);
            }
            if (!ratings.has(fight.fighter2_id)) {
                ratings.set(fight.fighter2_id, params.initial_rating);
                fightCounts.set(fight.fighter2_id, 0);
            }
            
            // Get current ratings
            const rating1 = ratings.get(fight.fighter1_id);
            const rating2 = ratings.get(fight.fighter2_id);
            
            // Make prediction
            const ratingDiff = rating1 - rating2;
            const expectedOutcome = 1 / (1 + Math.pow(10, -ratingDiff / params.rating_scale_divisor));
            
            // Determine actual outcome
            let actualOutcome;
            if (fight.result_method && fight.result_method.toLowerCase().includes('draw')) {
                actualOutcome = 0.5;
            } else if (fight.winner_id === fight.fighter1_id) {
                actualOutcome = 1;
            } else {
                actualOutcome = 0;
            }
            
            // Evaluate prediction (skip draws)
            if (actualOutcome !== 0.5 && isRecent) {
                recentTotal++;
                const predictedWinner = expectedOutcome > 0.5 ? fight.fighter1_id : fight.fighter2_id;
                const correct = predictedWinner === fight.winner_id;
                
                if (correct) recentCorrect++;
                
                // Track confident predictions
                const winProb = Math.max(expectedOutcome, 1 - expectedOutcome);
                if (winProb >= 0.6) {
                    confidentTotal++;
                    if (correct) confidentCorrect++;
                }
                if (winProb >= 0.7) {
                    highConfTotal++;
                    if (correct) highConfCorrect++;
                }
            }
            
            // Update ratings
            const k = params.k_factor;
            const change1 = k * (actualOutcome - expectedOutcome);
            const change2 = k * (expectedOutcome - actualOutcome);
            
            ratings.set(fight.fighter1_id, rating1 + change1);
            ratings.set(fight.fighter2_id, rating2 + change2);
            
            fightCounts.set(fight.fighter1_id, fightCounts.get(fight.fighter1_id) + 1);
            fightCounts.set(fight.fighter2_id, fightCounts.get(fight.fighter2_id) + 1);
        }
        
        return {
            division: params.division,
            totalFights: fights.length,
            recentTotal,
            recentCorrect,
            recentAccuracy: recentTotal > 0 ? (recentCorrect / recentTotal * 100).toFixed(1) + '%' : 'N/A',
            confidentAccuracy: confidentTotal > 0 ? (confidentCorrect / confidentTotal * 100).toFixed(1) + '%' : 'N/A',
            highConfAccuracy: highConfTotal > 0 ? (highConfCorrect / highConfTotal * 100).toFixed(1) + '%' : 'N/A'
        };
    }

    async testVeryRecentFights() {
        console.log('\n🔥 VERY RECENT FIGHTS (2024-2025 only):');
        console.log('─'.repeat(50));
        
        const recentStats = this.db.prepare(`
            SELECT 
                f.weight_class as division,
                COUNT(*) as total_fights,
                SUM(CASE WHEN fh.expected_outcome > 0.5 AND fh.actual_outcome = 1 THEN 1
                         WHEN fh.expected_outcome < 0.5 AND fh.actual_outcome = 0 THEN 1
                         ELSE 0 END) as correct,
                AVG(ABS(fh.expected_outcome - 0.5)) as avg_confidence
            FROM whr_fight_history fh
            JOIN fights f ON fh.fight_id = f.id
            JOIN events e ON f.event_id = e.id
            WHERE e.date >= '2024-01-01'
            AND fh.actual_outcome != 0.5
            GROUP BY f.weight_class
            ORDER BY total_fights DESC
        `).all();
        
        let totalCorrect = 0;
        let totalFights = 0;
        
        for (const stat of recentStats) {
            const accuracy = (stat.correct / stat.total_fights * 100).toFixed(1);
            totalCorrect += stat.correct;
            totalFights += stat.total_fights;
            
            console.log(
                `${stat.division.padEnd(20)} | ${stat.total_fights.toString().padStart(3)} fights | ` +
                `${accuracy.padStart(5)}% accuracy`
            );
        }
        
        console.log('─'.repeat(50));
        const veryRecentAccuracy = (totalCorrect / totalFights * 100).toFixed(1);
        console.log(`${'2024-2025 OVERALL'.padEnd(20)} | ${totalFights.toString().padStart(3)} fights | ${veryRecentAccuracy.padStart(5)}% accuracy`);
    }

    close() {
        if (this.db) this.db.close();
    }
}

// Run test
if (require.main === module) {
    const tester = new CurrentSystemAccuracy();
    
    tester.testCurrentAccuracy()
        .then(() => {
            console.log('\n✅ Current accuracy test completed!');
        })
        .catch(error => {
            console.error('❌ Error:', error);
        })
        .finally(() => {
            tester.close();
        });
}