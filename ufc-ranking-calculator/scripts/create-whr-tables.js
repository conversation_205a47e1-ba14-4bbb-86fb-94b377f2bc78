const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '..', 'data', 'ufc_data.db');
const db = new Database(dbPath);

console.log('Creating WHR output tables...');

try {
    // Create whr_ratings table
    db.exec(`
        CREATE TABLE IF NOT EXISTS whr_ratings (
            fighter_id INTEGER,
            division TEXT NOT NULL,
            rating REAL NOT NULL DEFAULT 1500.0,
            rating_deviation REAL NOT NULL DEFAULT 350.0,
            last_fight_date TEXT,
            fight_count INTEGER DEFAULT 0,
            win_count INTEGER DEFAULT 0,
            loss_count INTEGER DEFAULT 0,
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (fighter_id, division),
            FOREIGN KEY (fighter_id) REFERENCES fighters(id)
        )
    `);

    // Create whr_fight_history table
    db.exec(`
        CREATE TABLE IF NOT EXISTS whr_fight_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            fight_id INTEGER NOT NULL,
            division TEXT NOT NULL,
            fighter1_id INTEGER NOT NULL,
            fighter2_id INTEGER NOT NULL,
            fighter1_pre_rating REAL NOT NULL,
            fighter2_pre_rating REAL NOT NULL,
            fighter1_post_rating REAL NOT NULL,
            fighter2_post_rating REAL NOT NULL,
            rating_change_fighter1 REAL NOT NULL,
            rating_change_fighter2 REAL NOT NULL,
            expected_outcome REAL NOT NULL,
            actual_outcome INTEGER NOT NULL,
            surprise_factor REAL NOT NULL,
            k_factor REAL NOT NULL,
            performance_multiplier REAL DEFAULT 1.0,
            calculation_timestamp TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (fight_id) REFERENCES fights(id),
            FOREIGN KEY (fighter1_id) REFERENCES fighters(id),
            FOREIGN KEY (fighter2_id) REFERENCES fighters(id)
        )
    `);

    // Create whr_division_rankings table
    db.exec(`
        CREATE TABLE IF NOT EXISTS whr_division_rankings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            fighter_id INTEGER NOT NULL,
            division TEXT NOT NULL,
            rank INTEGER NOT NULL,
            rating REAL NOT NULL,
            rating_deviation REAL NOT NULL,
            points REAL,
            streak INTEGER DEFAULT 0,
            dominance_score REAL,
            activity_score REAL,
            status TEXT DEFAULT 'active',
            ranking_date TEXT DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (fighter_id) REFERENCES fighters(id)
        )
    `);

    // Create indexes
    db.exec(`CREATE INDEX IF NOT EXISTS idx_whr_ratings_division ON whr_ratings(division, rating DESC)`);
    db.exec(`CREATE INDEX IF NOT EXISTS idx_whr_fight_history_fight ON whr_fight_history(fight_id)`);
    db.exec(`CREATE INDEX IF NOT EXISTS idx_whr_division_rankings_division ON whr_division_rankings(division, rank)`);

    console.log('✅ WHR tables created successfully!');

} catch (error) {
    console.error('❌ Error creating WHR tables:', error);
} finally {
    db.close();
}