const Database = require('better-sqlite3');
const path = require('path');

/**
 * Create tables needed for the unified WHR algorithm
 */

const dbPath = path.join(__dirname, '..', 'data', 'ufc_data.db');
const db = new Database(dbPath);

console.log('🔨 Creating tables for Unified WHR Algorithm...');

try {
    // Add confidence and variance columns to whr_ratings if they don't exist
    console.log('\n📋 Updating whr_ratings table...');
    
    // Check if columns exist
    const columns = db.prepare("PRAGMA table_info(whr_ratings)").all();
    const columnNames = columns.map(c => c.name);
    
    if (!columnNames.includes('confidence')) {
        db.exec(`
            ALTER TABLE whr_ratings 
            ADD COLUMN confidence VARCHAR(20) DEFAULT 'established'
        `);
        console.log('  ✅ Added confidence column');
    }
    
    if (!columnNames.includes('performance_variance')) {
        db.exec(`
            ALTER TABLE whr_ratings 
            ADD COLUMN performance_variance DECIMAL(10,4) DEFAULT 0
        `);
        console.log('  ✅ Added performance_variance column');
    }

    // Create calculation log table for audit trail
    console.log('\n📋 Creating whr_calculation_log table...');
    db.exec(`
        CREATE TABLE IF NOT EXISTS whr_calculation_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            algorithm_version VARCHAR(50),
            total_fighters INTEGER,
            total_fights INTEGER,
            convergence_iterations INTEGER,
            parameters_used TEXT,
            execution_time_seconds DECIMAL(10,2),
            notes TEXT
        )
    `);
    console.log('  ✅ Created whr_calculation_log table');

    // Create calculation events table for detailed audit trail
    console.log('\n📋 Creating whr_calculation_events table...');
    db.exec(`
        CREATE TABLE IF NOT EXISTS whr_calculation_events (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            calculation_id INTEGER,
            fighter_id INTEGER,
            opponent_id INTEGER,
            fight_id INTEGER,
            fight_date DATE,
            division VARCHAR(50),
            rating_before DECIMAL(10,2),
            rating_after DECIMAL(10,2),
            rating_change DECIMAL(10,2),
            k_factor DECIMAL(10,2),
            performance_multiplier DECIMAL(10,2),
            parameters_version INTEGER,
            calculation_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (fighter_id) REFERENCES fighters(id),
            FOREIGN KEY (opponent_id) REFERENCES fighters(id),
            FOREIGN KEY (fight_id) REFERENCES fights(id)
        )
    `);
    console.log('  ✅ Created whr_calculation_events table');

    // Create parameter versions table
    console.log('\n📋 Creating whr_parameter_versions table...');
    db.exec(`
        CREATE TABLE IF NOT EXISTS whr_parameter_versions (
            version INTEGER PRIMARY KEY,
            division VARCHAR(50),
            parameters TEXT,
            effective_date DATE,
            accuracy_score DECIMAL(5,2),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_by VARCHAR(100),
            notes TEXT
        )
    `);
    console.log('  ✅ Created whr_parameter_versions table');

    // Add confidence column to whr_division_rankings if it doesn't exist
    console.log('\n📋 Updating whr_division_rankings table...');
    const rankingColumns = db.prepare("PRAGMA table_info(whr_division_rankings)").all();
    const rankingColumnNames = rankingColumns.map(c => c.name);
    
    if (!rankingColumnNames.includes('confidence')) {
        db.exec(`
            ALTER TABLE whr_division_rankings 
            ADD COLUMN confidence VARCHAR(20) DEFAULT 'established'
        `);
        console.log('  ✅ Added confidence column to rankings');
    }

    // Create indices for performance
    console.log('\n📋 Creating indices...');
    
    db.exec(`
        CREATE INDEX IF NOT EXISTS idx_whr_events_fighter 
        ON whr_calculation_events(fighter_id, fight_date);
        
        CREATE INDEX IF NOT EXISTS idx_whr_events_division 
        ON whr_calculation_events(division, fight_date);
        
        CREATE INDEX IF NOT EXISTS idx_whr_log_timestamp 
        ON whr_calculation_log(timestamp);
    `);
    console.log('  ✅ Created performance indices');

    // Insert initial parameter version
    console.log('\n📋 Creating initial parameter version...');
    const versionCheck = db.prepare('SELECT COUNT(*) as count FROM whr_parameter_versions').get();
    
    if (versionCheck.count === 0) {
        db.prepare(`
            INSERT INTO whr_parameter_versions (version, division, parameters, effective_date, notes)
            VALUES (1, 'all', ?, date('now'), 'Initial unified algorithm parameters')
        `).run(JSON.stringify({
            convergenceThreshold: 1.0,
            sosDampening: 0.85,
            ageDampening: 0.7,
            maxIterations: 10,
            provisionalFightThreshold: 3
        }));
        console.log('  ✅ Created initial parameter version');
    }

    console.log('\n✅ All tables created/updated successfully!');
    
    // Display table summary
    console.log('\n📊 Database Summary:');
    const tables = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name LIKE 'whr_%'
        ORDER BY name
    `).all();
    
    for (const table of tables) {
        const count = db.prepare(`SELECT COUNT(*) as count FROM ${table.name}`).get();
        console.log(`  ${table.name}: ${count.count} records`);
    }

} catch (error) {
    console.error('❌ Error creating tables:', error);
    process.exit(1);
} finally {
    db.close();
    console.log('\n🔒 Database connection closed');
}