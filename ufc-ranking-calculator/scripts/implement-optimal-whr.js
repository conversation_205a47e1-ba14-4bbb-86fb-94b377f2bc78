const Database = require('better-sqlite3');
const path = require('path');

/**
 * Implement Optimal WHR Configuration
 * Based on deep analysis results
 */

class OptimalWHRImplementation {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        
        // Optimal configuration from analysis
        this.optimalConfig = {
            // Base parameters
            initialRating: 1500,
            ratingFloor: 800,
            ratingCeiling: 2500,
            baseScaleDivisor: 400,
            
            // Division-specific K-factors and adjustments
            divisions: {
                // High volatility divisions
                'Heavyweight': { 
                    kBase: 40, 
                    kNew: 52,      // 0-5 fights
                    kDeveloping: 44, // 6-15 fights
                    kEstablished: 32, // 16+ fights
                    scale: 400
                },
                'Light Heavyweight': { 
                    kBase: 36,
                    kNew: 48,
                    kDeveloping: 40,
                    kEstablished: 28,
                    scale: 400
                },
                
                // Standard divisions
                'Middleweight': { 
                    kBase: 32,
                    kNew: 44,
                    kDeveloping: 36,
                    kEstablished: 26,
                    scale: 400
                },
                'Welterweight': { 
                    kBase: 32,
                    kNew: 44,
                    kDeveloping: 36,
                    kEstablished: 26,
                    scale: 400
                },
                'Lightweight': { 
                    kBase: 32,
                    kNew: 44,
                    kDeveloping: 36,
                    kEstablished: 26,
                    scale: 400
                },
                'Featherweight': { 
                    kBase: 32,
                    kNew: 44,
                    kDeveloping: 36,
                    kEstablished: 26,
                    scale: 400
                },
                'Bantamweight': { 
                    kBase: 32,
                    kNew: 44,
                    kDeveloping: 36,
                    kEstablished: 26,
                    scale: 400
                },
                'Flyweight': { 
                    kBase: 30,
                    kNew: 42,
                    kDeveloping: 34,
                    kEstablished: 24,
                    scale: 400
                },
                
                // Women's divisions (smaller talent pool)
                'Women\'s Bantamweight': { 
                    kBase: 28,
                    kNew: 40,
                    kDeveloping: 32,
                    kEstablished: 24,
                    scale: 400
                },
                'Women\'s Flyweight': { 
                    kBase: 28,
                    kNew: 40,
                    kDeveloping: 32,
                    kEstablished: 24,
                    scale: 400
                },
                'Women\'s Strawweight': { 
                    kBase: 28,
                    kNew: 40,
                    kDeveloping: 32,
                    kEstablished: 24,
                    scale: 400
                }
            },
            
            // Additional factors
            inactivityPenalty: 0.05, // 5% rating decay per year after 1 year
            inactivityThreshold: 365, // days before penalty applies
            
            // Confidence thresholds for analysis
            confidenceThresholds: {
                low: 0.55,      // 55% = slight favorite
                medium: 0.65,   // 65% = clear favorite
                high: 0.75      // 75% = heavy favorite
            }
        };
    }

    async implementOptimalParameters() {
        console.log('🚀 IMPLEMENTING OPTIMAL WHR CONFIGURATION');
        console.log('═'.repeat(80));
        
        // Step 1: Update division parameters
        console.log('\n📊 Updating division parameters...');
        await this.updateDivisionParameters();
        
        // Step 2: Recalculate all ratings with new system
        console.log('\n📊 Recalculating all ratings with optimal parameters...');
        await this.recalculateAllRatings();
        
        // Step 3: Validate improvements
        console.log('\n📊 Validating improvements...');
        await this.validateImprovements();
    }

    async updateDivisionParameters() {
        const updateStmt = this.db.prepare(`
            UPDATE division_parameters 
            SET k_factor = ?, rating_scale_divisor = ?
            WHERE division = ?
        `);
        
        const updates = this.db.transaction(() => {
            for (const [division, config] of Object.entries(this.optimalConfig.divisions)) {
                updateStmt.run(config.kBase, config.scale, division);
                console.log(`  ✅ ${division}: K=${config.kBase}, Scale=${config.scale}`);
            }
        });
        updates();
        
        console.log('\n✅ Division parameters updated!');
    }

    async recalculateAllRatings() {
        // Get all fights
        const fights = this.db.prepare(`
            SELECT 
                f.id,
                f.event_id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                f.result_round,
                f.weight_class,
                e.date as event_date
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class NOT IN ('Catch Weight', 'Open Weight')
            ORDER BY e.date ASC, f.id ASC
        `).all();
        
        console.log(`  Processing ${fights.length} fights...`);
        
        // Initialize tracking
        const ratings = new Map();
        const fightCounts = new Map();
        const lastFightDates = new Map();
        const fightHistory = [];
        
        // Process each fight
        let processed = 0;
        for (const fight of fights) {
            const divConfig = this.optimalConfig.divisions[fight.weight_class];
            if (!divConfig) continue;
            
            // Initialize fighters
            const key1 = `${fight.fighter1_id}_${fight.weight_class}`;
            const key2 = `${fight.fighter2_id}_${fight.weight_class}`;
            
            if (!ratings.has(key1)) {
                ratings.set(key1, {
                    fighterId: fight.fighter1_id,
                    division: fight.weight_class,
                    rating: this.optimalConfig.initialRating,
                    fightCount: 0,
                    wins: 0,
                    losses: 0,
                    draws: 0
                });
                fightCounts.set(key1, 0);
            }
            if (!ratings.has(key2)) {
                ratings.set(key2, {
                    fighterId: fight.fighter2_id,
                    division: fight.weight_class,
                    rating: this.optimalConfig.initialRating,
                    fightCount: 0,
                    wins: 0,
                    losses: 0,
                    draws: 0
                });
                fightCounts.set(key2, 0);
            }
            
            const fighter1 = ratings.get(key1);
            const fighter2 = ratings.get(key2);
            
            // Apply inactivity penalty
            this.applyInactivityPenalty(key1, fight.event_date, ratings, lastFightDates);
            this.applyInactivityPenalty(key2, fight.event_date, ratings, lastFightDates);
            
            // Store pre-fight ratings
            const preRatings = {
                fighter1: { ...fighter1 },
                fighter2: { ...fighter2 }
            };
            
            // Calculate expected outcome
            const ratingDiff = fighter1.rating - fighter2.rating;
            const expectedOutcome = 1 / (1 + Math.pow(10, -ratingDiff / divConfig.scale));
            
            // Determine actual outcome
            let actualOutcome;
            if (fight.result_method && fight.result_method.toLowerCase().includes('draw')) {
                actualOutcome = 0.5;
                fighter1.draws++;
                fighter2.draws++;
            } else if (fight.winner_id === fight.fighter1_id) {
                actualOutcome = 1;
                fighter1.wins++;
                fighter2.losses++;
            } else {
                actualOutcome = 0;
                fighter1.losses++;
                fighter2.wins++;
            }
            
            // Calculate dynamic K-factors
            const k1 = this.getDynamicK(fighter1.fightCount, divConfig);
            const k2 = this.getDynamicK(fighter2.fightCount, divConfig);
            
            // Calculate rating changes
            const change1 = k1 * (actualOutcome - expectedOutcome);
            const change2 = k2 * (expectedOutcome - actualOutcome);
            
            // Apply changes with bounds
            fighter1.rating = Math.max(this.optimalConfig.ratingFloor,
                            Math.min(this.optimalConfig.ratingCeiling, fighter1.rating + change1));
            fighter2.rating = Math.max(this.optimalConfig.ratingFloor,
                            Math.min(this.optimalConfig.ratingCeiling, fighter2.rating + change2));
            
            // Update fight counts
            fighter1.fightCount++;
            fighter2.fightCount++;
            fightCounts.set(key1, fighter1.fightCount);
            fightCounts.set(key2, fighter2.fightCount);
            
            // Update last fight dates
            lastFightDates.set(key1, fight.event_date);
            lastFightDates.set(key2, fight.event_date);
            
            // Store history
            fightHistory.push({
                fightId: fight.id,
                division: fight.weight_class,
                fighter1Id: fight.fighter1_id,
                fighter2Id: fight.fighter2_id,
                preRatings,
                postRatings: {
                    fighter1: { ...fighter1 },
                    fighter2: { ...fighter2 }
                },
                expectedOutcome,
                actualOutcome,
                k1,
                k2
            });
            
            processed++;
            if (processed % 1000 === 0) {
                process.stdout.write(`\r  Processed ${processed}/${fights.length} fights...`);
            }
        }
        
        console.log(`\r  ✅ Processed all ${processed} fights!`);
        
        // Save to database
        await this.saveRatings(ratings, fightHistory);
    }

    getDynamicK(fightCount, divConfig) {
        if (fightCount <= 5) return divConfig.kNew;
        if (fightCount <= 15) return divConfig.kDeveloping;
        return divConfig.kEstablished;
    }

    applyInactivityPenalty(fighterKey, currentDate, ratings, lastDates) {
        if (!lastDates.has(fighterKey)) return;
        
        const fighter = ratings.get(fighterKey);
        const lastDate = new Date(lastDates.get(fighterKey));
        const current = new Date(currentDate);
        const daysSince = (current - lastDate) / (1000 * 60 * 60 * 24);
        
        if (daysSince > this.optimalConfig.inactivityThreshold) {
            const yearsInactive = (daysSince - this.optimalConfig.inactivityThreshold) / 365;
            const penalty = this.optimalConfig.inactivityPenalty * yearsInactive;
            const ratingDecay = (fighter.rating - this.optimalConfig.initialRating) * penalty;
            fighter.rating = Math.max(fighter.rating - ratingDecay, this.optimalConfig.ratingFloor);
        }
    }

    async saveRatings(ratings, fightHistory) {
        console.log('\n💾 Saving optimized ratings...');
        
        // Clear existing data
        this.db.exec('DELETE FROM whr_ratings');
        this.db.exec('DELETE FROM whr_fight_history');
        
        // Save ratings
        const ratingStmt = this.db.prepare(`
            INSERT INTO whr_ratings (
                fighter_id, division, rating, rating_deviation,
                last_fight_date, fight_count, win_count, loss_count
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        const ratingInserts = this.db.transaction(() => {
            for (const [key, fighter] of ratings) {
                const lastDate = fightHistory
                    .filter(h => h.fighter1Id === fighter.fighterId || h.fighter2Id === fighter.fighterId)
                    .slice(-1)[0]?.postRatings.fighter1.rating ? 
                    fightHistory.filter(h => h.fighter1Id === fighter.fighterId || h.fighter2Id === fighter.fighterId).slice(-1)[0] : null;
                
                ratingStmt.run(
                    fighter.fighterId,
                    fighter.division,
                    fighter.rating,
                    50, // Fixed low deviation for established system
                    lastDate ? '2025-05-17' : null, // Latest fight date
                    fighter.fightCount,
                    fighter.wins,
                    fighter.losses
                );
            }
        });
        ratingInserts();
        
        // Save fight history
        const historyStmt = this.db.prepare(`
            INSERT INTO whr_fight_history (
                fight_id, division, fighter1_id, fighter2_id,
                fighter1_pre_rating, fighter2_pre_rating,
                fighter1_post_rating, fighter2_post_rating,
                rating_change_fighter1, rating_change_fighter2,
                expected_outcome, actual_outcome, surprise_factor,
                k_factor, performance_multiplier
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        const historyInserts = this.db.transaction(() => {
            for (const history of fightHistory) {
                historyStmt.run(
                    history.fightId,
                    history.division,
                    history.fighter1Id,
                    history.fighter2Id,
                    history.preRatings.fighter1.rating,
                    history.preRatings.fighter2.rating,
                    history.postRatings.fighter1.rating,
                    history.postRatings.fighter2.rating,
                    history.postRatings.fighter1.rating - history.preRatings.fighter1.rating,
                    history.postRatings.fighter2.rating - history.preRatings.fighter2.rating,
                    history.expectedOutcome,
                    history.actualOutcome,
                    Math.abs(history.actualOutcome - history.expectedOutcome),
                    history.k1,
                    1.0
                );
            }
        });
        historyInserts();
        
        console.log(`  ✅ Saved ${ratings.size} ratings and ${fightHistory.length} fight records`);
    }

    async validateImprovements() {
        // Check rating distribution
        const distribution = this.db.prepare(`
            SELECT 
                division,
                COUNT(*) as fighters,
                MIN(rating) as min_rating,
                MAX(rating) as max_rating,
                AVG(rating) as avg_rating,
                MAX(rating) - MIN(rating) as spread
            FROM whr_ratings
            WHERE fight_count > 3
            GROUP BY division
            ORDER BY spread DESC
        `).all();
        
        console.log('\nRating Distribution (fighters with 3+ fights):');
        console.log('Division              | Fighters | Range       | Spread | Average');
        console.log('─'.repeat(70));
        
        for (const div of distribution) {
            console.log(
                `${div.division.padEnd(20)} | ${div.fighters.toString().padStart(8)} | ` +
                `${div.min_rating.toFixed(0)}-${div.max_rating.toFixed(0)}`.padStart(11) + ' | ' +
                `${div.spread.toFixed(0).padStart(6)} | ${div.avg_rating.toFixed(0).padStart(7)}`
            );
        }
        
        // Check prediction confidence
        const confidence = this.db.prepare(`
            SELECT 
                AVG(ABS(expected_outcome - 0.5)) as avg_confidence,
                COUNT(CASE WHEN ABS(expected_outcome - 0.5) > 0.15 THEN 1 END) as confident_fights,
                COUNT(*) as total_fights
            FROM whr_fight_history
            WHERE actual_outcome != 0.5
        `).get();
        
        console.log('\nPrediction Confidence:');
        console.log(`  Average confidence: ${(confidence.avg_confidence * 100).toFixed(1)}%`);
        console.log(`  Confident predictions (>65%): ${(confidence.confident_fights / confidence.total_fights * 100).toFixed(1)}%`);
        
        console.log('\n✅ Optimal WHR implementation complete!');
    }

    close() {
        if (this.db) this.db.close();
    }
}

// Run implementation
if (require.main === module) {
    const implementation = new OptimalWHRImplementation();
    
    implementation.implementOptimalParameters()
        .then(() => {
            console.log('\n✅ Optimal WHR system implemented!');
        })
        .catch(error => {
            console.error('❌ Error:', error);
        })
        .finally(() => {
            implementation.close();
        });
}