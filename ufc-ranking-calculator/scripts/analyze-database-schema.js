const Database = require('better-sqlite3');
const path = require('path');

const dbPath = path.join(__dirname, '../data/ufc_data.db');
const db = new Database(dbPath, { readonly: true });

console.log('=== UFC WHR DATABASE SCHEMA ANALYSIS ===\n');

// Get all tables
const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' ORDER BY name").all();

console.log(`Total tables: ${tables.length}\n`);

// For each table, get detailed schema
tables.forEach(table => {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`TABLE: ${table.name}`);
  console.log(`${'='.repeat(60)}`);
  
  // Get table info
  const columns = db.prepare(`PRAGMA table_info("${table.name}")`).all();
  console.log('\nColumns:');
  columns.forEach(col => {
    const pk = col.pk ? ' [PRIMARY KEY]' : '';
    const nn = col.notnull ? ' NOT NULL' : '';
    const def = col.dflt_value ? ` DEFAULT ${col.dflt_value}` : '';
    console.log(`  ${col.cid}. ${col.name} - ${col.type}${pk}${nn}${def}`);
  });
  
  // Get indexes
  const indexes = db.prepare(`PRAGMA index_list("${table.name}")`).all();
  if (indexes.length > 0) {
    console.log('\nIndexes:');
    indexes.forEach(idx => {
      const indexInfo = db.prepare(`PRAGMA index_info("${idx.name}")`).all();
      const columns = indexInfo.map(i => i.name).join(', ');
      const unique = idx.unique ? ' [UNIQUE]' : '';
      console.log(`  - ${idx.name} (${columns})${unique}`);
    });
  }
  
  // Get row count
  try {
    const count = db.prepare(`SELECT COUNT(*) as count FROM "${table.name}"`).get();
    console.log(`\nRow count: ${count.count.toLocaleString()}`);
  } catch (e) {
    console.log(`\nRow count: Error - ${e.message}`);
  }
  
  // Sample data for key tables
  if (['fighters', 'fights', 'whr_ratings', 'whr_fight_history', 'whr_performance_metrics'].includes(table.name)) {
    console.log('\nSample data:');
    try {
      const sample = db.prepare(`SELECT * FROM "${table.name}" LIMIT 2`).all();
      sample.forEach((row, idx) => {
        console.log(`  Row ${idx + 1}:`, JSON.stringify(row, null, 2).split('\n').map((line, i) => i === 0 ? line : '    ' + line).join('\n'));
      });
    } catch (e) {
      console.log(`  Error getting sample: ${e.message}`);
    }
  }
});

// Check foreign keys
console.log('\n\n=== FOREIGN KEY RELATIONSHIPS ===');
tables.forEach(table => {
  const fks = db.prepare(`PRAGMA foreign_key_list("${table.name}")`).all();
  if (fks.length > 0) {
    console.log(`\n${table.name}:`);
    fks.forEach(fk => {
      console.log(`  - ${fk.from} -> ${fk.table}(${fk.to})`);
    });
  }
});

// Check for age-related columns
console.log('\n\n=== AGE ANALYSIS CAPABILITY CHECK ===');
console.log('\nFighters table birthdate info:');
const fighterBirthdates = db.prepare("SELECT COUNT(*) as total, COUNT(birthdate) as with_birthdate FROM fighters").get();
console.log(`  Total fighters: ${fighterBirthdates.total}`);
console.log(`  With birthdate: ${fighterBirthdates.with_birthdate} (${(fighterBirthdates.with_birthdate/fighterBirthdates.total*100).toFixed(1)}%)`);

console.log('\nFights table date info:');
// First check what columns exist in fights table
const fightsCols = db.prepare("PRAGMA table_info('fights')").all();
const dateCol = fightsCols.find(col => col.name.toLowerCase().includes('date'));
if (dateCol) {
  const fightDates = db.prepare(`SELECT COUNT(*) as total, COUNT(${dateCol.name}) as with_date FROM fights`).get();
  console.log(`  Total fights: ${fightDates.total}`);
  console.log(`  With date: ${fightDates.with_date} (${(fightDates.with_date/fightDates.total*100).toFixed(1)}%)`);
  console.log(`  Date column: ${dateCol.name}`);
} else {
  console.log('  No date column found in fights table');
}

// Check for performance metrics
console.log('\n\n=== PERFORMANCE METRICS AVAILABILITY ===');
const perfMetrics = db.prepare("SELECT COUNT(*) as count FROM whr_performance_metrics").get();
console.log(`Performance metrics records: ${perfMetrics.count}`);

// Check whr_fight_history for dates
console.log('\nWHR Fight History date info:');
const whrFightDates = db.prepare("SELECT COUNT(*) as total, COUNT(fight_date) as with_date FROM whr_fight_history").get();
console.log(`  Total WHR fights: ${whrFightDates.total}`);
console.log(`  With date: ${whrFightDates.with_date} (${(whrFightDates.with_date/whrFightDates.total*100).toFixed(1)}%)`);

// Sample fighter birthdates
console.log('\n\nSample fighter birthdates:');
const sampleFighters = db.prepare("SELECT id, name, birthdate FROM fighters WHERE birthdate IS NOT NULL LIMIT 5").all();
sampleFighters.forEach(f => {
  console.log(`  ${f.name} (ID: ${f.id}): ${f.birthdate}`);
});

db.close();