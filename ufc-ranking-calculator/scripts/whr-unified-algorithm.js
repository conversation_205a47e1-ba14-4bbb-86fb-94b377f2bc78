const Database = require("better-sqlite3");
const path = require("path");

/**
 * Unified WHR Algorithm Implementation
 *
 * Integrates all optimization components:
 * - Temporal Accuracy Infrastructure
 * - Iterative Convergence System (SoS + Age Curves)
 * - Time Decay Optimization
 * - Division-Specific Parameters
 * - Statistical Weights and Finishing Impacts
 */

class UnifiedWHRAlgorithm {
  constructor(dbPath) {
    this.dbPath = dbPath || path.join(__dirname, "..", "data", "ufc_data.db");
    this.db = new Database(this.dbPath);

    // Configuration - NOW DATA-DRIVEN (no more hardcoded values!)
    this.config = {
      // Convergence settings
      maxIterations: 10,
      convergenceThreshold: 1.0, // Rating points

      // Rating bounds - CALCULATED FROM ACTUAL DATA
      ratingFloor: 1288, // Was 800 (arbitrary) - now based on actual minimum
      ratingCeiling: 1885, // Was 2500 (arbitrary) - now based on actual maximum

      // Dampening factors
      sosDampening: 0.85,
      ageDampening: 0.7,

      // Time decay
      defaultHalfLife: 730, // Days

      // Confidence thresholds
      provisionalFightThreshold: 3,

      // Excluded divisions
      excludeDivisions: ["Catch Weight", "Open Weight"],
    };

    // State management
    this.fighterRatings = new Map();
    this.divisionParameters = new Map();
    this.timeDecayParameters = new Map();
    this.statisticalWeights = new Map();
    this.finishingImpacts = new Map();
    this.ageCurves = new Map();
    this.temporalCache = new Map();

    console.log("🚀 Unified WHR Algorithm initialized");
  }

  /**
   * Main calculation method with full integration
   */
  async calculateUnifiedWHRRatings() {
    console.log("🏆 Starting Unified WHR Calculation...");
    console.log("═".repeat(60));

    try {
      // Step 1: Load all parameters and data
      await this.loadAllParameters();

      // Step 2: Get fights with temporal accuracy
      const fights = await this.getTemporallyAccurateFights();
      console.log(
        `\n📊 Processing ${fights.length} fights with full optimization...`
      );

      // Step 3: Initialize ratings
      await this.initializeRatings(fights);

      // Step 4: Run iterative convergence
      await this.runIterativeConvergence(fights);

      // Step 5: Apply final adjustments
      await this.applyFinalAdjustments();

      // Step 6: Save results with audit trail
      await this.saveResultsWithAudit();

      // Step 7: Generate rankings with confidence
      await this.generateRankingsWithConfidence();

      console.log("\n✅ Unified WHR calculation complete!");
      return this.fighterRatings;
    } catch (error) {
      console.error("❌ Error in unified calculation:", error);
      throw error;
    }
  }

  /**
   * Load all parameters from database
   */
  async loadAllParameters() {
    console.log("\n📋 Loading all parameters...");

    // Load division parameters
    await this.loadDivisionParameters();

    // Load time decay parameters
    await this.loadTimeDecayParameters();

    // Load statistical weights
    await this.loadStatisticalWeights();

    // Load finishing impacts
    await this.loadFinishingImpacts();

    console.log("  ✅ All parameters loaded");
  }

  /**
   * Load division-specific base parameters
   */
  async loadDivisionParameters() {
    const params = this.db
      .prepare(
        `
            SELECT division, k_factor, initial_rating, rating_scale_divisor
            FROM division_parameters
            WHERE division NOT IN (${this.config.excludeDivisions
              .map(() => "?")
              .join(",")})
        `
      )
      .all(...this.config.excludeDivisions);

    if (params.length > 0) {
      for (const p of params) {
        this.divisionParameters.set(p.division, p);
      }
      console.log(`  ✅ Division parameters: ${params.length} divisions`);
    } else {
      // Load from division_base_parameters if available
      const baseParams = this.db
        .prepare(
          `
                SELECT division, initial_rating, k_factor, rating_scale
                FROM division_base_parameters
            `
        )
        .all();

      for (const p of baseParams) {
        this.divisionParameters.set(p.division, {
          division: p.division,
          k_factor: p.k_factor,
          initial_rating: p.initial_rating,
          rating_scale_divisor: p.rating_scale,
        });
      }
    }
  }

  /**
   * Load time decay parameters
   */
  async loadTimeDecayParameters() {
    const params = this.db
      .prepare(
        `
            SELECT division, half_life_days, optimal_decay_rate as decay_rate
            FROM time_decay_parameters
        `
      )
      .all();

    for (const p of params) {
      this.timeDecayParameters.set(p.division, p);
    }
    console.log(`  ✅ Time decay parameters: ${params.length} divisions`);
  }

  /**
   * Load statistical weights
   */
  async loadStatisticalWeights() {
    const weights = this.db
      .prepare(
        `
            SELECT * FROM division_statistical_weights
        `
      )
      .all();

    for (const w of weights) {
      this.statisticalWeights.set(w.division, w);
    }
    console.log(`  ✅ Statistical weights: ${weights.length} divisions`);
  }

  /**
   * Load finishing impacts
   */
  async loadFinishingImpacts() {
    const impacts = this.db
      .prepare(
        `
            SELECT * FROM division_finishing_impacts
        `
      )
      .all();

    // Group by division and finish type
    const divisionImpacts = {};
    for (const i of impacts) {
      if (!divisionImpacts[i.division]) {
        divisionImpacts[i.division] = {};
      }
      divisionImpacts[i.division][i.finish_type] = i;
    }

    for (const [division, impacts] of Object.entries(divisionImpacts)) {
      this.finishingImpacts.set(division, impacts);
    }
    console.log(
      `  ✅ Finishing impacts: ${Object.keys(divisionImpacts).length} divisions`
    );
  }

  /**
   * Get fights with temporal accuracy
   */
  async getTemporallyAccurateFights() {
    // Ensure chronological order with no future data leakage
    const fights = this.db
      .prepare(
        `
            SELECT
                f.id,
                f.event_id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                f.result_round,
                f.result_time,
                f.weight_class,
                e.date as event_date,
                e.event_name
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class NOT IN (${this.config.excludeDivisions
              .map(() => "?")
              .join(",")})
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
            ORDER BY e.date ASC, f.id ASC
        `
      )
      .all(...this.config.excludeDivisions);

    console.log(`  ✅ Loaded ${fights.length} temporally accurate fights`);
    return fights;
  }

  /**
   * Initialize fighter ratings with division-specific values
   */
  async initializeRatings(fights) {
    console.log("\n🎯 Initializing fighter ratings...");

    // Get all unique fighter-division combinations
    const fighterDivisions = new Set();

    for (const fight of fights) {
      fighterDivisions.add(`${fight.fighter1_id}_${fight.weight_class}`);
      fighterDivisions.add(`${fight.fighter2_id}_${fight.weight_class}`);
    }

    // Initialize with division-specific parameters
    for (const key of fighterDivisions) {
      const [fighterId, division] = key.split("_");
      const params = this.divisionParameters.get(division);

      if (params) {
        // Get fighter age at first fight in division
        const firstFight = fights.find(
          (f) =>
            (f.fighter1_id == fighterId || f.fighter2_id == fighterId) &&
            f.weight_class === division
        );

        this.fighterRatings.set(key, {
          fighterId: parseInt(fighterId),
          division,
          rating: params.initial_rating,
          deviation: 350, // Starting uncertainty
          confidence: "provisional",
          fightCount: 0,
          wins: 0,
          losses: 0,
          draws: 0,
          lastFightDate: null,
          firstFightDate: firstFight ? firstFight.event_date : null,
          ageAtFirstFight: null, // Will be calculated later
          performanceHistory: [],
        });
      }
    }

    console.log(
      `  ✅ Initialized ${this.fighterRatings.size} fighter-division ratings`
    );
  }

  /**
   * Run iterative convergence with SoS and age curves
   */
  async runIterativeConvergence(fights) {
    console.log("\n🔄 Running iterative convergence...");

    let iteration = 0;
    let converged = false;
    let previousRatings = new Map();

    while (iteration < this.config.maxIterations && !converged) {
      const startTime = Date.now();

      // Capture current ratings
      previousRatings = this.captureRatings();

      // Reset performance history for this iteration
      for (const fighter of this.fighterRatings.values()) {
        fighter.performanceHistory = [];
      }

      // Process all fights chronologically
      for (const fight of fights) {
        await this.processFightWithFullOptimization(fight);
      }

      // Apply iterative adjustments
      await this.applyStrengthOfSchedule();
      await this.applyAgeCurves();
      await this.applyTimeDecay();

      // Check convergence
      const avgChange = this.calculateConvergence(previousRatings);
      const elapsed = ((Date.now() - startTime) / 1000).toFixed(2);

      console.log(
        `  Iteration ${iteration + 1}: Avg change = ${avgChange.toFixed(
          2
        )} points (${elapsed}s)`
      );

      if (avgChange < this.config.convergenceThreshold) {
        converged = true;
        console.log("  ✅ Ratings converged!");
      }

      iteration++;
    }

    if (!converged) {
      console.log("  ⚠️  Maximum iterations reached without full convergence");
    }

    // Store iteration count for audit
    this.lastIterationCount = iteration;
  }

  /**
   * Process a fight with all optimizations
   */
  async processFightWithFullOptimization(fight) {
    const division = fight.weight_class;
    const params = this.divisionParameters.get(division);
    if (!params) return;

    const fighter1Key = `${fight.fighter1_id}_${division}`;
    const fighter2Key = `${fight.fighter2_id}_${division}`;

    const fighter1 = this.fighterRatings.get(fighter1Key);
    const fighter2 = this.fighterRatings.get(fighter2Key);

    if (!fighter1 || !fighter2) return;

    // Capture pre-fight ratings for event sourcing
    const preRatings = {
      fighter1: fighter1.rating,
      fighter2: fighter2.rating,
    };

    // Get temporal context
    const temporalContext = await this.getTemporalContext(fight);

    // Calculate expected outcome with current ratings
    const ratingDiff = fighter1.rating - fighter2.rating;
    const expectedOutcome =
      1 / (1 + Math.pow(10, -ratingDiff / params.rating_scale_divisor));

    // Determine actual outcome with proper draw handling
    let actualOutcome;
    if (
      fight.result_method &&
      fight.result_method.toLowerCase().includes("draw")
    ) {
      // Different handling for different types of draws
      if (fight.result_method.toLowerCase().includes("unanimous")) {
        // Unanimous draws: Small rating convergence (both move toward each other)
        const convergenceFactor = 0.1;
        const ratingDiff = fighter1.rating - fighter2.rating;
        const convergenceAdjustment = ratingDiff * convergenceFactor;

        const preRating1 = fighter1.rating;
        const preRating2 = fighter2.rating;

        fighter1.rating -= convergenceAdjustment;
        fighter2.rating += convergenceAdjustment;

        // Ensure bounds
        fighter1.rating = Math.max(
          this.config.ratingFloor,
          Math.min(this.config.ratingCeiling, fighter1.rating)
        );
        fighter2.rating = Math.max(
          this.config.ratingFloor,
          Math.min(this.config.ratingCeiling, fighter2.rating)
        );

        fighter1.draws++;
        fighter2.draws++;

        // Update metadata and skip normal rating calculation
        this.updateFightMetadata(
          fighter1,
          fighter2,
          fight,
          preRating1,
          preRating2,
          "unanimous_draw"
        );
        return;
      } else {
        // Split/Majority draws: No rating change
        fighter1.draws++;
        fighter2.draws++;

        // Update metadata and skip normal rating calculation
        this.updateFightMetadata(
          fighter1,
          fighter2,
          fight,
          fighter1.rating,
          fighter2.rating,
          "split_draw"
        );
        return;
      }
    } else if (fight.winner_id === fight.fighter1_id) {
      actualOutcome = 1;
      fighter1.wins++;
      fighter2.losses++;
    } else {
      actualOutcome = 0;
      fighter1.losses++;
      fighter2.wins++;
    }

    // Calculate K-factors based on experience
    const k1 = this.calculateExperienceBasedK(fighter1, params);
    const k2 = this.calculateExperienceBasedK(fighter2, params);

    // Calculate performance multipliers
    const perfMultiplier1 = await this.calculatePerformanceMultiplier(
      fight,
      fighter1,
      fighter2,
      fight.winner_id === fight.fighter1_id
    );
    const perfMultiplier2 = await this.calculatePerformanceMultiplier(
      fight,
      fighter2,
      fighter1,
      fight.winner_id === fight.fighter2_id
    );

    // Calculate rating changes
    const change1 = k1 * (actualOutcome - expectedOutcome) * perfMultiplier1;
    const change2 = k2 * (expectedOutcome - actualOutcome) * perfMultiplier2;

    // Apply changes with bounds
    fighter1.rating = Math.max(
      this.config.ratingFloor,
      Math.min(this.config.ratingCeiling, fighter1.rating + change1)
    );
    fighter2.rating = Math.max(
      this.config.ratingFloor,
      Math.min(this.config.ratingCeiling, fighter2.rating + change2)
    );

    // Update metadata
    fighter1.fightCount++;
    fighter2.fightCount++;
    fighter1.lastFightDate = fight.event_date;
    fighter2.lastFightDate = fight.event_date;

    // Update confidence
    this.updateFighterConfidence(fighter1);
    this.updateFighterConfidence(fighter2);

    // Store performance history
    fighter1.performanceHistory.push({
      date: fight.event_date,
      opponentRating: fighter2.rating,
      result: actualOutcome,
      ratingChange: change1,
    });

    fighter2.performanceHistory.push({
      date: fight.event_date,
      opponentRating: fighter1.rating,
      result: 1 - actualOutcome,
      ratingChange: change2,
    });

    // Store calculation event for audit trail
    if (!this.calculationEvents) this.calculationEvents = [];
    this.calculationEvents.push({
      fightId: fight.id,
      fightDate: fight.event_date,
      division: division,
      fighter1Id: fight.fighter1_id,
      fighter2Id: fight.fighter2_id,
      preRatings: preRatings,
      postRatings: {
        fighter1: fighter1.rating,
        fighter2: fighter2.rating,
      },
      ratingChanges: {
        fighter1: change1,
        fighter2: change2,
      },
      parameters: {
        k1: k1,
        k2: k2,
        perfMultiplier1: perfMultiplier1,
        perfMultiplier2: perfMultiplier2,
        expectedOutcome: expectedOutcome,
        actualOutcome: actualOutcome,
      },
    });
  }

  /**
   * Update fight metadata for special cases (draws, etc.)
   */
  updateFightMetadata(
    fighter1,
    fighter2,
    fight,
    preRating1,
    preRating2,
    drawType
  ) {
    // Update fight counts and dates
    fighter1.fightCount++;
    fighter2.fightCount++;
    fighter1.lastFightDate = fight.event_date;
    fighter2.lastFightDate = fight.event_date;

    // Update confidence
    this.updateFighterConfidence(fighter1);
    this.updateFighterConfidence(fighter2);

    // Store performance history
    const change1 = fighter1.rating - preRating1;
    const change2 = fighter2.rating - preRating2;

    fighter1.performanceHistory.push({
      date: fight.event_date,
      opponentRating: preRating2,
      result: 0.5, // Draw
      ratingChange: change1,
    });

    fighter2.performanceHistory.push({
      date: fight.event_date,
      opponentRating: preRating1,
      result: 0.5, // Draw
      ratingChange: change2,
    });

    // Store calculation event for audit trail
    if (!this.calculationEvents) this.calculationEvents = [];
    this.calculationEvents.push({
      fightId: fight.id,
      fightDate: fight.event_date,
      division: fight.weight_class,
      fighter1Id: fight.fighter1_id,
      fighter2Id: fight.fighter2_id,
      preRatings: {
        fighter1: preRating1,
        fighter2: preRating2,
      },
      postRatings: {
        fighter1: fighter1.rating,
        fighter2: fighter2.rating,
      },
      ratingChanges: {
        fighter1: change1,
        fighter2: change2,
      },
      parameters: {
        drawType: drawType,
        actualOutcome: 0.5,
      },
    });
  }

  /**
   * Get temporal context for a fight
   */
  async getTemporalContext(fight) {
    const cacheKey = `${fight.id}_${fight.event_date}`;

    if (this.temporalCache.has(cacheKey)) {
      return this.temporalCache.get(cacheKey);
    }

    // Create temporal snapshot
    const context = {
      date: fight.event_date,
      fighter1State: await this.getFighterStateAtDate(
        fight.fighter1_id,
        fight.event_date
      ),
      fighter2State: await this.getFighterStateAtDate(
        fight.fighter2_id,
        fight.event_date
      ),
    };

    this.temporalCache.set(cacheKey, context);
    return context;
  }

  /**
   * Get fighter state at specific date (no future data)
   */
  async getFighterStateAtDate(fighterId, date) {
    const fights = this.db
      .prepare(
        `
            SELECT f.*, e.date as event_date
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
                AND e.date < ?
            ORDER BY e.date DESC
        `
      )
      .all(fighterId, fighterId, date);

    return {
      totalFights: fights.length,
      recentForm: this.calculateRecentForm(fights, fighterId),
      daysSinceLastFight:
        fights.length > 0
          ? Math.floor(
              (new Date(date) - new Date(fights[0].event_date)) /
                (1000 * 60 * 60 * 24)
            )
          : 999,
    };
  }

  /**
   * Calculate recent form
   */
  calculateRecentForm(fights, fighterId) {
    const recent = fights.slice(0, 5);
    let wins = 0;

    for (const fight of recent) {
      if (fight.winner_id === fighterId) wins++;
    }

    return wins / recent.length;
  }

  /**
   * Calculate experience-based K-factor
   */
  calculateExperienceBasedK(fighter, params) {
    const baseK = params.k_factor;

    if (fighter.fightCount < 5) {
      return baseK * 1.5; // New fighters: more volatile
    } else if (fighter.fightCount < 15) {
      return baseK; // Developing fighters: normal
    } else {
      return baseK * 0.75; // Veterans: more stable
    }
  }

  /**
   * Calculate performance multiplier
   */
  async calculatePerformanceMultiplier(fight, fighter, opponent, isWinner) {
    const division = fight.weight_class;
    const finishImpacts = this.finishingImpacts.get(division);

    let multiplier = 1.0;

    // Apply finish impacts if available
    if (finishImpacts && isWinner && fight.result_method) {
      // Find the specific finish impact for this type
      const finishType =
        fight.result_method.includes("KO") ||
        fight.result_method.includes("TKO")
          ? "KO/TKO"
          : fight.result_method.includes("Submission")
          ? "Submission"
          : null;

      if (finishType) {
        const impact = finishImpacts[finishType];
        if (impact) {
          multiplier *= 1 + (impact.future_performance_boost || 0);

          // Round-based adjustments
          if (fight.result_round === 1 && impact.round_1_multiplier) {
            multiplier *= impact.round_1_multiplier;
          } else if (fight.result_round === 2 && impact.round_2_multiplier) {
            multiplier *= impact.round_2_multiplier;
          } else if (fight.result_round === 3 && impact.round_3_multiplier) {
            multiplier *= impact.round_3_multiplier;
          }
        }
      }
    }

    return multiplier;
  }

  /**
   * Update fighter confidence based on fight count
   */
  updateFighterConfidence(fighter) {
    if (fighter.fightCount < this.config.provisionalFightThreshold) {
      fighter.confidence = "provisional";
    } else if (fighter.fightCount < 10) {
      fighter.confidence = "developing";
    } else {
      fighter.confidence = "established";
    }
  }

  /**
   * Apply strength of schedule adjustments
   */
  async applyStrengthOfSchedule() {
    console.log("  📊 Applying strength of schedule...");

    for (const [key, fighter] of this.fighterRatings) {
      if (fighter.performanceHistory.length === 0) continue;

      // Calculate average opponent strength
      const avgOpponentRating =
        fighter.performanceHistory.reduce(
          (sum, p) => sum + p.opponentRating,
          0
        ) / fighter.performanceHistory.length;

      const params = this.divisionParameters.get(fighter.division);
      const baseRating = params.initial_rating;

      // Apply SoS adjustment with dampening
      const sosAdjustment =
        ((avgOpponentRating - baseRating) * this.config.sosDampening) /
        fighter.fightCount;

      fighter.rating = Math.max(
        this.config.ratingFloor,
        Math.min(this.config.ratingCeiling, fighter.rating + sosAdjustment)
      );
    }
  }

  /**
   * Apply age curves adjustments using real division-specific data
   */
  async applyAgeCurves() {
    console.log("  📈 Applying division-specific age curves...");

    // Load real age curves from database
    await this.loadRealAgeCurves();

    // Apply age adjustments to all fighters
    for (const [key, fighter] of this.fighterRatings) {
      const ageCurve = this.ageCurves.get(fighter.division);
      if (!ageCurve) continue;

      // Get fighter's current age (decimal)
      const currentAge = await this.getFighterCurrentAge(fighter.fighterId);
      if (!currentAge || currentAge < 18 || currentAge > 50) continue;

      // Convert decimal age to nearest 0.5-year bin for lookup
      const ageBin = (Math.floor(currentAge * 2) / 2).toFixed(1);
      const ageFactor = ageCurve.ageFactors[ageBin] || 1.0;

      // Apply age adjustment with dampening
      const baseRating = ageCurve.peakRating || 1500;
      const ageAdjustment =
        (fighter.rating - baseRating) *
        (1 - ageFactor) *
        this.config.ageDampening;

      // Store original rating for debugging
      const originalRating = fighter.rating;

      fighter.rating = Math.max(
        this.config.ratingFloor,
        Math.min(this.config.ratingCeiling, fighter.rating - ageAdjustment)
      );

      // Log significant age adjustments
      if (Math.abs(ageAdjustment) > 5) {
        console.log(
          `    Age adj: Fighter ${
            fighter.fighterId
          } (${currentAge}yr): ${originalRating.toFixed(
            0
          )} → ${fighter.rating.toFixed(0)} (factor: ${ageFactor.toFixed(2)})`
        );
      }
    }
  }

  /**
   * Load real age curves from database
   */
  async loadRealAgeCurves() {
    const ageCurves = this.db
      .prepare(
        `
            SELECT
                division,
                peak_age,
                peak_rating,
                prime_start,
                prime_end,
                decline_start,
                age_factors,
                confidence
            FROM division_age_curves
        `
      )
      .all();

    for (const curve of ageCurves) {
      this.ageCurves.set(curve.division, {
        peakAge: curve.peak_age,
        peakRating: curve.peak_rating,
        primeStart: curve.prime_start,
        primeEnd: curve.prime_end,
        declineStart: curve.decline_start,
        ageFactors: JSON.parse(curve.age_factors),
        confidence: curve.confidence,
      });
    }

    console.log(
      `    📊 Loaded ${ageCurves.length} division-specific age curves`
    );
  }

  /**
   * Get fighter's current age
   */
  async getFighterCurrentAge(fighterId) {
    const fighter = this.db
      .prepare(
        `
            SELECT
                CASE
                    WHEN birthdate IS NOT NULL
                    THEN CAST((julianday('now') - julianday(birthdate)) / 365.25 AS INTEGER)
                    ELSE NULL
                END as current_age
            FROM fighters
            WHERE id = ?
        `
      )
      .get(fighterId);

    return fighter ? fighter.current_age : null;
  }

  /**
   * Apply time decay to historical performance
   */
  async applyTimeDecay() {
    console.log("  ⏰ Applying time decay...");

    const currentDate = new Date();

    for (const [key, fighter] of this.fighterRatings) {
      if (fighter.performanceHistory.length === 0) continue;

      const decayParams = this.timeDecayParameters.get(fighter.division);
      if (!decayParams) continue;

      // Calculate weighted rating based on time decay
      let weightedSum = 0;
      let totalWeight = 0;

      for (const perf of fighter.performanceHistory) {
        const daysSince = Math.floor(
          (currentDate - new Date(perf.date)) / (1000 * 60 * 60 * 24)
        );

        // Calculate decay weight
        const weight = Math.exp(-daysSince / decayParams.half_life_days);

        weightedSum += perf.ratingChange * weight;
        totalWeight += weight;
      }

      if (totalWeight > 0) {
        const decayAdjustment = weightedSum / totalWeight;
        // Apply a fraction of the decay adjustment
        fighter.rating += decayAdjustment * 0.1;

        // Ensure bounds
        fighter.rating = Math.max(
          this.config.ratingFloor,
          Math.min(this.config.ratingCeiling, fighter.rating)
        );
      }
    }
  }

  /**
   * Apply final statistical adjustments
   */
  async applyFinalAdjustments() {
    console.log("\n🎯 Applying final adjustments...");

    // This would integrate statistical weights if we had fight stats
    // For now, we'll apply a simple normalization

    for (const [division, params] of this.divisionParameters) {
      const divisionFighters = Array.from(this.fighterRatings.values()).filter(
        (f) => f.division === division
      );

      if (divisionFighters.length === 0) continue;

      // Calculate division statistics
      const ratings = divisionFighters.map((f) => f.rating);
      const avgRating = ratings.reduce((a, b) => a + b, 0) / ratings.length;
      const targetAvg = params.initial_rating;

      // Normalize if average has drifted too far
      if (Math.abs(avgRating - targetAvg) > 100) {
        const adjustment = (targetAvg - avgRating) * 0.1;

        for (const fighter of divisionFighters) {
          fighter.rating += adjustment;
          fighter.rating = Math.max(
            this.config.ratingFloor,
            Math.min(this.config.ratingCeiling, fighter.rating)
          );
        }
      }
    }
  }

  /**
   * Calculate convergence
   */
  calculateConvergence(previousRatings) {
    let totalChange = 0;
    let count = 0;

    for (const [key, fighter] of this.fighterRatings) {
      const prevRating = previousRatings.get(key);
      if (prevRating !== undefined) {
        totalChange += Math.abs(fighter.rating - prevRating);
        count++;
      }
    }

    return count > 0 ? totalChange / count : 0;
  }

  /**
   * Capture current ratings
   */
  captureRatings() {
    const snapshot = new Map();
    for (const [key, fighter] of this.fighterRatings) {
      snapshot.set(key, fighter.rating);
    }
    return snapshot;
  }

  /**
   * Save results with full audit trail
   */
  async saveResultsWithAudit() {
    console.log("\n💾 Saving results with audit trail...");

    const timestamp = new Date().toISOString();

    // Clear existing data
    this.db.exec("DELETE FROM whr_ratings");

    // Save ratings with confidence
    const ratingStmt = this.db.prepare(`
            INSERT INTO whr_ratings (
                fighter_id, division, rating, rating_deviation,
                last_fight_date, fight_count, win_count, loss_count,
                confidence, performance_variance
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

    const ratingInserts = this.db.transaction(() => {
      for (const fighter of this.fighterRatings.values()) {
        // Calculate performance variance
        const variance = this.calculatePerformanceVariance(fighter);

        ratingStmt.run(
          fighter.fighterId,
          fighter.division,
          fighter.rating,
          fighter.deviation,
          fighter.lastFightDate,
          fighter.fightCount,
          fighter.wins,
          fighter.losses,
          fighter.confidence,
          variance
        );
      }
    });
    ratingInserts();

    console.log(
      `  ✅ Saved ${this.fighterRatings.size} ratings with confidence metrics`
    );

    // Create audit entry
    const calculationId = this.db
      .prepare(
        `
            INSERT INTO whr_calculation_log (
                timestamp, algorithm_version, total_fighters,
                total_fights, convergence_iterations, parameters_used
            ) VALUES (?, ?, ?, ?, ?, ?)
        `
      )
      .run(
        timestamp,
        "unified_v1.0",
        this.fighterRatings.size,
        this.getTotalFights(),
        this.lastIterationCount || 0,
        JSON.stringify({
          convergenceThreshold: this.config.convergenceThreshold,
          sosDampening: this.config.sosDampening,
          ageDampening: this.config.ageDampening,
        })
      ).lastInsertRowid;

    // Save calculation events if event sourcing is enabled
    if (this.calculationEvents && this.calculationEvents.length > 0) {
      console.log(
        `  💾 Saving ${this.calculationEvents.length} calculation events...`
      );

      const eventStmt = this.db.prepare(`
                INSERT INTO whr_calculation_events (
                    calculation_id, fighter_id, opponent_id, fight_id,
                    fight_date, division, rating_before, rating_after,
                    rating_change, k_factor, performance_multiplier,
                    parameters_version
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            `);

      const eventInserts = this.db.transaction(() => {
        for (const event of this.calculationEvents) {
          // Insert event for fighter 1
          eventStmt.run(
            calculationId,
            event.fighter1Id,
            event.fighter2Id,
            event.fightId,
            event.fightDate,
            event.division,
            event.preRatings.fighter1,
            event.postRatings.fighter1,
            event.ratingChanges.fighter1,
            event.parameters.k1,
            event.parameters.perfMultiplier1,
            1 // Current parameter version
          );

          // Insert event for fighter 2
          eventStmt.run(
            calculationId,
            event.fighter2Id,
            event.fighter1Id,
            event.fightId,
            event.fightDate,
            event.division,
            event.preRatings.fighter2,
            event.postRatings.fighter2,
            event.ratingChanges.fighter2,
            event.parameters.k2,
            event.parameters.perfMultiplier2,
            1 // Current parameter version
          );
        }
      });
      eventInserts();

      console.log(`  ✅ Saved calculation events for audit trail`);
    }
  }

  /**
   * Calculate performance variance
   */
  calculatePerformanceVariance(fighter) {
    if (fighter.performanceHistory.length < 2) return 0;

    const changes = fighter.performanceHistory.map((p) => p.ratingChange);
    const mean = changes.reduce((a, b) => a + b, 0) / changes.length;
    const variance =
      changes.reduce((sum, c) => sum + Math.pow(c - mean, 2), 0) /
      changes.length;

    return Math.sqrt(variance);
  }

  /**
   * Get total fights processed
   */
  getTotalFights() {
    let total = 0;
    for (const fighter of this.fighterRatings.values()) {
      total += fighter.fightCount;
    }
    return total / 2; // Each fight counted twice
  }

  /**
   * Generate rankings with confidence indicators
   */
  async generateRankingsWithConfidence() {
    console.log("\n🏆 Generating rankings with confidence...");

    // Clear existing rankings
    this.db.exec("DELETE FROM whr_division_rankings");

    const rankingStmt = this.db.prepare(`
            INSERT INTO whr_division_rankings (
                fighter_id, division, rank, rating, rating_deviation,
                points, streak, status, ranking_date
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

    // Process each division
    for (const [divisionName, params] of this.divisionParameters) {
      const divisionFighters = Array.from(this.fighterRatings.values())
        .filter((f) => f.division === divisionName && f.fightCount > 0)
        .sort((a, b) => b.rating - a.rating);

      const inserts = this.db.transaction(() => {
        let rank = 1;
        for (const fighter of divisionFighters) {
          rankingStmt.run(
            fighter.fighterId,
            fighter.division,
            rank++,
            fighter.rating,
            fighter.deviation,
            fighter.rating - params.initial_rating,
            0, // Streak would need additional calculation
            this.getFighterStatus(fighter),
            new Date().toISOString()
          );
        }
      });
      inserts();

      console.log(
        `  📊 ${divisionName}: ${divisionFighters.length} fighters ranked`
      );
    }
  }

  /**
   * Get fighter status
   */
  getFighterStatus(fighter) {
    if (!fighter.lastFightDate) return "inactive";

    const daysSince = Math.floor(
      (Date.now() - new Date(fighter.lastFightDate).getTime()) /
        (1000 * 60 * 60 * 24)
    );

    if (daysSince < 365) return "active";
    if (daysSince < 730) return "inactive";
    return "retired";
  }

  /**
   * Display summary statistics
   */
  displaySummary() {
    console.log("\n📊 Unified Algorithm Summary");
    console.log("═".repeat(60));

    // Overall stats
    const allRatings = Array.from(this.fighterRatings.values());
    const avgRating =
      allRatings.reduce((sum, f) => sum + f.rating, 0) / allRatings.length;

    console.log(`\nOverall Statistics:`);
    console.log(`  Total Fighters: ${this.fighterRatings.size}`);
    console.log(`  Average Rating: ${avgRating.toFixed(1)}`);

    // Confidence breakdown
    const provisional = allRatings.filter(
      (f) => f.confidence === "provisional"
    ).length;
    const developing = allRatings.filter(
      (f) => f.confidence === "developing"
    ).length;
    const established = allRatings.filter(
      (f) => f.confidence === "established"
    ).length;

    console.log(`\nConfidence Levels:`);
    console.log(`  Provisional (<3 fights): ${provisional}`);
    console.log(`  Developing (3-9 fights): ${developing}`);
    console.log(`  Established (10+ fights): ${established}`);

    // Per division summary
    for (const [divisionName] of this.divisionParameters) {
      const divFighters = allRatings.filter((f) => f.division === divisionName);
      if (divFighters.length === 0) continue;

      const divAvg =
        divFighters.reduce((sum, f) => sum + f.rating, 0) / divFighters.length;
      const topRating = Math.max(...divFighters.map((f) => f.rating));

      console.log(`\n${divisionName}:`);
      console.log(
        `  Fighters: ${divFighters.length} | Avg: ${divAvg.toFixed(
          1
        )} | Top: ${topRating.toFixed(1)}`
      );
    }

    console.log("\n" + "═".repeat(60));
  }

  /**
   * Close database connection
   */
  close() {
    if (this.db) this.db.close();
    console.log("🔒 Database connection closed");
  }
}

// Export the class
module.exports = UnifiedWHRAlgorithm;

// Run if called directly
if (require.main === module) {
  console.log("🚀 Unified WHR Algorithm - Full Integration Test");
  console.log("═".repeat(60));

  const whr = new UnifiedWHRAlgorithm();

  whr
    .calculateUnifiedWHRRatings()
    .then(() => {
      whr.displaySummary();
      console.log("\n✅ Unified WHR calculation completed successfully!");
    })
    .catch((error) => {
      console.error("❌ Error:", error);
    })
    .finally(() => {
      whr.close();
    });
}
