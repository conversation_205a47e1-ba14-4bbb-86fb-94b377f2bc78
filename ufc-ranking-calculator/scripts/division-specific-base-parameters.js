const Database = require('better-sqlite3');
const path = require('path');
const TemporalAccuracyFramework = require('./temporal-accuracy-infrastructure');

/**
 * Division-Specific Base Parameters - Roadmap #2
 * 
 * Calculate optimal parameters for each division based on empirical data:
 * - Initial ratings (expected range: 1500-1620)
 * - Division-specific K-factors for rating volatility  
 * - 200-point rating scale for improved prediction accuracy
 * - Division characterization (finish rates, volatility patterns)
 * - No arbitrary caps on rating gains - let mathematics determine outcomes
 */

class DivisionSpecificParameters {
    constructor(dbPath) {
        this.dbPath = dbPath || path.join(__dirname, '..', 'data', 'ufc_data.db');
        this.db = new Database(this.dbPath);
        this.temporal = new TemporalAccuracyFramework(this.dbPath);
        this.divisionParams = {};
        
        console.log('📊 Division-Specific Parameters Calculator initialized');
    }

    /**
     * Analyze division characteristics to calculate optimal parameters
     */
    async analyzeDivisionCharacteristics() {
        console.log('🔍 Analyzing division characteristics...');
        
        const divisions = this.temporal.getDivisionDateRanges();
        const results = [];

        for (const division of divisions) {
            console.log(`\n📈 Analyzing ${division.division}...`);
            
            const characteristics = await this.calculateDivisionCharacteristics(division.division);
            const optimalRating = await this.calculateOptimalInitialRating(division.division);
            const optimalKFactor = await this.calculateOptimalKFactor(division.division);
            
            const params = {
                division: division.division,
                characteristics,
                optimalInitialRating: optimalRating,
                optimalKFactor: optimalKFactor,
                recommendedScale: 200, // Per roadmap for improved accuracy
                confidence: this.assessConfidenceLevel(characteristics.totalFights)
            };
            
            results.push(params);
            this.divisionParams[division.division] = params;
        }

        return results;
    }

    /**
     * Calculate comprehensive division characteristics
     */
    async calculateDivisionCharacteristics(division) {
        const query = `
            SELECT 
                f.id,
                f.fighter1_id,
                f.fighter2_id,
                f.winner_id,
                f.result_method,
                f.result_round,
                e.date
            FROM fights f
            JOIN events e ON f.event_id = e.id
            WHERE f.weight_class = ?
                AND f.result_method NOT LIKE '%No Contest%'
                AND f.result_method NOT LIKE '%DQ%'
                AND f.fight_status = 'valid'
            ORDER BY e.date ASC
        `;

        const fights = this.db.prepare(query).all(division);
        
        // Calculate finish rates and patterns
        let koTkoCount = 0;
        let submissionCount = 0;
        let decisionCount = 0;
        let round1Finishes = 0;
        const uniqueFighters = new Set();

        for (const fight of fights) {
            uniqueFighters.add(fight.fighter1_id);
            uniqueFighters.add(fight.fighter2_id);

            if (fight.result_method.includes('KO') || fight.result_method.includes('TKO')) {
                koTkoCount++;
                if (fight.result_round === 1) round1Finishes++;
            } else if (fight.result_method.includes('Submission')) {
                submissionCount++;
                if (fight.result_round === 1) round1Finishes++;
            } else if (fight.result_method.includes('Decision')) {
                decisionCount++;
            }
        }

        const totalFights = fights.length;
        const finishRate = (koTkoCount + submissionCount) / totalFights;
        const koTkoRate = koTkoCount / totalFights;
        const submissionRate = submissionCount / totalFights;
        const decisionRate = decisionCount / totalFights;
        const earlyFinishRate = round1Finishes / totalFights;

        return {
            totalFights,
            uniqueFighters: uniqueFighters.size,
            finishRate,
            koTkoRate,
            submissionRate,
            decisionRate,
            earlyFinishRate,
            avgFightersPerEvent: uniqueFighters.size / this.getUniqueEvents(fights).length,
            dataSpanYears: this.calculateDataSpan(fights)
        };
    }

    /**
     * Calculate optimal initial rating for new fighters in division
     * Based on rookie performance against existing fighters
     */
    async calculateOptimalInitialRating(division) {
        console.log(`  🎯 Calculating optimal initial rating for ${division}...`);
        
        // Get all fighters and their debut fights in this division
        const rookieAnalysis = await this.analyzeRookiePerformance(division);
        
        if (rookieAnalysis.sampleSize < 20) {
            console.log(`    ⚠️  Small sample size (${rookieAnalysis.sampleSize}), using conservative estimate`);
            return 1500; // Conservative default for low data
        }

        // Calculate based on rookie win rate against established opposition
        const baseRating = 1500;
        const adjustment = (rookieAnalysis.winRate - 0.5) * 100; // Adjust based on performance vs 50%
        const recommendedRating = Math.round(baseRating + adjustment);

        console.log(`    📊 Rookie analysis: ${rookieAnalysis.sampleSize} debuts, ${(rookieAnalysis.winRate * 100).toFixed(1)}% win rate`);
        console.log(`    🎯 Recommended initial rating: ${recommendedRating}`);

        return Math.max(1400, Math.min(1700, recommendedRating)); // Reasonable bounds
    }

    /**
     * Analyze rookie performance in division
     */
    async analyzeRookiePerformance(division) {
        const fights = this.temporal.getChronologicalFights().filter(f => f.weight_class === division);
        const rookieDebuts = [];

        for (const fight of fights) {
            // Check if this is fighter1's debut in division
            const fighter1Prior = await this.temporal.getFighterStateAtDate(
                fight.fighter1_id, 
                division, 
                this.getPreviousDay(fight.event_date)
            );
            
            if (fighter1Prior.totalFights === 0) {
                rookieDebuts.push({
                    fighterId: fight.fighter1_id,
                    result: fight.winner_id === fight.fighter1_id ? 'W' : 'L',
                    opponentId: fight.fighter2_id
                });
            }

            // Check if this is fighter2's debut in division  
            const fighter2Prior = await this.temporal.getFighterStateAtDate(
                fight.fighter2_id, 
                division, 
                this.getPreviousDay(fight.event_date)
            );
            
            if (fighter2Prior.totalFights === 0) {
                rookieDebuts.push({
                    fighterId: fight.fighter2_id,
                    result: fight.winner_id === fight.fighter2_id ? 'W' : 'L',
                    opponentId: fight.fighter1_id
                });
            }
        }

        const wins = rookieDebuts.filter(d => d.result === 'W').length;
        const winRate = rookieDebuts.length > 0 ? wins / rookieDebuts.length : 0.5;

        return {
            sampleSize: rookieDebuts.length,
            wins,
            winRate
        };
    }

    /**
     * Calculate optimal K-factor based on division volatility
     */
    async calculateOptimalKFactor(division) {
        console.log(`  ⚡ Calculating optimal K-factor for ${division}...`);
        
        const volatilityMetrics = await this.calculateDivisionVolatility(division);
        
        // Base K-factor of 32, adjusted for division characteristics
        let kFactor = 32;
        
        // Adjust based on finish rate (higher finish rate = more volatility)
        if (volatilityMetrics.finishRate > 0.6) {
            kFactor += 2; // High finish divisions need higher K
        } else if (volatilityMetrics.finishRate < 0.4) {
            kFactor -= 1; // Decision-heavy divisions can use lower K
        }
        
        // Adjust based on upset rate
        if (volatilityMetrics.upsetRate > 0.3) {
            kFactor += 3; // High upset rate = high volatility
        } else if (volatilityMetrics.upsetRate < 0.2) {
            kFactor -= 2; // Predictable divisions can use lower K
        }

        // Bounds checking
        kFactor = Math.max(26, Math.min(40, kFactor));
        
        console.log(`    📊 Volatility metrics: ${(volatilityMetrics.finishRate * 100).toFixed(1)}% finish rate, ${(volatilityMetrics.upsetRate * 100).toFixed(1)}% upset rate`);
        console.log(`    ⚡ Recommended K-factor: ${kFactor}`);

        return kFactor;
    }

    /**
     * Calculate division volatility metrics
     */
    async calculateDivisionVolatility(division) {
        const fights = this.temporal.getChronologicalFights().filter(f => f.weight_class === division);
        
        let finishes = 0;
        let upsets = 0; // Will define as wins by less experienced fighter
        let totalComparisons = 0;

        for (const fight of fights) {
            // Count finishes
            if (!fight.result_method.includes('Decision')) {
                finishes++;
            }

            // Count experience-based upsets
            const fighter1State = await this.temporal.getFighterStateAtDate(
                fight.fighter1_id, 
                division, 
                fight.event_date
            );
            const fighter2State = await this.temporal.getFighterStateAtDate(
                fight.fighter2_id, 
                division, 
                fight.event_date
            );

            if (fighter1State.totalFights !== fighter2State.totalFights) {
                totalComparisons++;
                const lessExperiencedWon = 
                    (fighter1State.totalFights < fighter2State.totalFights && fight.winner_id === fight.fighter1_id) ||
                    (fighter2State.totalFights < fighter1State.totalFights && fight.winner_id === fight.fighter2_id);
                
                if (lessExperiencedWon) upsets++;
            }
        }

        return {
            finishRate: finishes / fights.length,
            upsetRate: totalComparisons > 0 ? upsets / totalComparisons : 0
        };
    }

    /**
     * Assess confidence level based on data size
     */
    assessConfidenceLevel(totalFights) {
        if (totalFights >= 1000) return 'Very High';
        if (totalFights >= 500) return 'High';
        if (totalFights >= 200) return 'Medium';
        if (totalFights >= 50) return 'Low';
        return 'Very Low';
    }

    /**
     * Helper: Get unique events from fights
     */
    getUniqueEvents(fights) {
        const events = new Set();
        fights.forEach(f => {
            const eventKey = f.date; // Group by date
            events.add(eventKey);
        });
        return Array.from(events);
    }

    /**
     * Helper: Calculate data span in years
     */
    calculateDataSpan(fights) {
        if (fights.length === 0) return 0;
        
        const dates = fights.map(f => new Date(f.date)).sort();
        const earliest = dates[0];
        const latest = dates[dates.length - 1];
        
        return Math.round((latest - earliest) / (1000 * 60 * 60 * 24 * 365.25));
    }

    /**
     * Helper: Get previous day for temporal queries
     */
    getPreviousDay(dateString) {
        const date = new Date(dateString);
        date.setDate(date.getDate() - 1);
        return date.toISOString().split('T')[0];
    }

    /**
     * Store calculated parameters in database
     */
    async storeDivisionParameters() {
        console.log('💾 Storing division parameters in database...');
        
        // Create table if it doesn't exist
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS division_parameters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                division TEXT NOT NULL UNIQUE,
                initial_rating REAL NOT NULL,
                k_factor REAL NOT NULL,
                rating_scale_divisor REAL NOT NULL DEFAULT 200,
                finish_rate REAL,
                volatility_metrics TEXT,
                confidence_level TEXT,
                total_fights INTEGER,
                unique_fighters INTEGER,
                created_at TEXT DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // Clear existing data
        this.db.exec('DELETE FROM division_parameters');

        const insertStmt = this.db.prepare(`
            INSERT INTO division_parameters (
                division, initial_rating, k_factor, rating_scale_divisor,
                finish_rate, volatility_metrics, confidence_level,
                total_fights, unique_fighters
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        for (const [division, params] of Object.entries(this.divisionParams)) {
            insertStmt.run(
                division,
                params.optimalInitialRating,
                params.optimalKFactor,
                params.recommendedScale,
                params.characteristics.finishRate,
                JSON.stringify({
                    koTkoRate: params.characteristics.koTkoRate,
                    submissionRate: params.characteristics.submissionRate,
                    earlyFinishRate: params.characteristics.earlyFinishRate
                }),
                params.confidence,
                params.characteristics.totalFights,
                params.characteristics.uniqueFighters
            );
        }

        console.log(`✅ Stored parameters for ${Object.keys(this.divisionParams).length} divisions`);
    }

    /**
     * Display summary of calculated parameters
     */
    displayParametersSummary() {
        console.log('\n📊 Division-Specific Base Parameters Summary');
        console.log('═'.repeat(80));
        
        for (const [division, params] of Object.entries(this.divisionParams)) {
            console.log(`\n🥊 ${division}:`);
            console.log(`   Initial Rating: ${params.optimalInitialRating}`);
            console.log(`   K-Factor: ${params.optimalKFactor}`);
            console.log(`   Rating Scale: ${params.recommendedScale}`);
            console.log(`   Total Fights: ${params.characteristics.totalFights}`);
            console.log(`   Finish Rate: ${(params.characteristics.finishRate * 100).toFixed(1)}%`);
            console.log(`   Confidence: ${params.confidence}`);
        }
        
        console.log('\n═'.repeat(80));
    }

    /**
     * Close connections
     */
    close() {
        if (this.temporal) this.temporal.close();
        if (this.db) this.db.close();
        console.log('🔒 Database connections closed');
    }
}

// Export for use in other modules
module.exports = DivisionSpecificParameters;

// Test run if called directly
if (require.main === module) {
    console.log('🚀 Calculating Division-Specific Base Parameters...');
    
    const calculator = new DivisionSpecificParameters();
    
    calculator.analyzeDivisionCharacteristics()
        .then(results => {
            console.log(`\n✅ Analysis complete for ${results.length} divisions`);
            
            // Display summary
            calculator.displayParametersSummary();
            
            // Store in database
            return calculator.storeDivisionParameters();
        })
        .then(() => {
            console.log('\n🎯 Division-Specific Base Parameters implementation completed successfully');
            console.log('📈 Ready for Iterative Convergence System implementation');
        })
        .catch(error => {
            console.error('❌ Error calculating division parameters:', error);
        })
        .finally(() => {
            calculator.close();
        });
}