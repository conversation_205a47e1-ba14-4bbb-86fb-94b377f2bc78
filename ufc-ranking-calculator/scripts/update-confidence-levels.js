const Database = require('better-sqlite3');
const path = require('path');

/**
 * Update confidence levels based on fight counts
 * This ensures fighters with <3 fights are marked as provisional
 */

const dbPath = path.join(__dirname, '..', 'data', 'ufc_data.db');
const db = new Database(dbPath);

console.log('🔄 Updating confidence levels based on fight counts...');

try {
    // First, let's see the current state
    const currentState = db.prepare(`
        SELECT 
            CASE 
                WHEN fight_count < 3 THEN 'should be provisional'
                WHEN fight_count < 10 THEN 'should be developing'
                ELSE 'should be established'
            END as expected_confidence,
            confidence as actual_confidence,
            COUNT(*) as count
        FROM whr_ratings
        GROUP BY expected_confidence, actual_confidence
    `).all();
    
    console.log('\n📊 Current state:');
    currentState.forEach(s => {
        console.log(`  ${s.expected_confidence} / actual: ${s.actual_confidence}: ${s.count} fighters`);
    });
    
    // Update confidence levels based on fight count
    const updateConfidence = db.transaction(() => {
        // Update provisional fighters (< 3 fights)
        const provisional = db.prepare(`
            UPDATE whr_ratings 
            SET confidence = 'provisional' 
            WHERE fight_count < 3
        `).run();
        console.log(`\n✅ Updated ${provisional.changes} fighters to provisional status`);
        
        // Update developing fighters (3-9 fights)
        const developing = db.prepare(`
            UPDATE whr_ratings 
            SET confidence = 'developing' 
            WHERE fight_count >= 3 AND fight_count < 10
        `).run();
        console.log(`✅ Updated ${developing.changes} fighters to developing status`);
        
        // Update established fighters (10+ fights)
        const established = db.prepare(`
            UPDATE whr_ratings 
            SET confidence = 'established' 
            WHERE fight_count >= 10
        `).run();
        console.log(`✅ Updated ${established.changes} fighters to established status`);
        
        // Also update in division rankings
        const rankingsUpdate = db.prepare(`
            UPDATE whr_division_rankings
            SET confidence = (
                SELECT confidence 
                FROM whr_ratings wr 
                WHERE wr.fighter_id = whr_division_rankings.fighter_id 
                AND wr.division = whr_division_rankings.division
            )
            WHERE EXISTS (
                SELECT 1 
                FROM whr_ratings wr 
                WHERE wr.fighter_id = whr_division_rankings.fighter_id 
                AND wr.division = whr_division_rankings.division
            )
        `).run();
        console.log(`✅ Updated ${rankingsUpdate.changes} rankings with confidence levels`);
    });
    
    updateConfidence();
    
    // Verify the update
    const newState = db.prepare(`
        SELECT 
            confidence,
            COUNT(*) as count,
            MIN(fight_count) as min_fights,
            MAX(fight_count) as max_fights,
            AVG(fight_count) as avg_fights
        FROM whr_ratings
        GROUP BY confidence
        ORDER BY 
            CASE confidence 
                WHEN 'provisional' THEN 1
                WHEN 'developing' THEN 2
                WHEN 'established' THEN 3
            END
    `).all();
    
    console.log('\n📊 New state:');
    newState.forEach(s => {
        console.log(`  ${s.confidence}: ${s.count} fighters (${s.min_fights}-${s.max_fights} fights, avg: ${s.avg_fights.toFixed(1)})`);
    });
    
    // Show some examples of provisional fighters
    const provisionalExamples = db.prepare(`
        SELECT 
            wr.fighter_id,
            f.first_name || ' ' || f.last_name as name,
            wr.division,
            wr.fight_count,
            wr.rating,
            wr.confidence
        FROM whr_ratings wr
        JOIN fighters f ON wr.fighter_id = f.id
        WHERE wr.confidence = 'provisional'
        LIMIT 10
    `).all();
    
    if (provisionalExamples.length > 0) {
        console.log('\n📌 Example provisional fighters:');
        provisionalExamples.forEach(f => {
            console.log(`  ${f.name} (${f.division}): ${f.fight_count} fights, rating: ${f.rating.toFixed(0)}`);
        });
    }
    
    console.log('\n✅ Confidence levels updated successfully!');
    
} catch (error) {
    console.error('❌ Error updating confidence levels:', error);
    process.exit(1);
} finally {
    db.close();
    console.log('\n🔒 Database connection closed');
}