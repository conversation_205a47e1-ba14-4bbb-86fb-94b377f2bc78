# UFC Fighter Ranking Calculator

An advanced MMA ranking system that implements sophisticated performance metrics, age weighting, and finish type multipliers to create accurate fighter rankings.

## Features

### Core Algorithm Components
- **Asymmetric Age Weighting**: Different decline rates for pre-prime vs post-prime fighters
- **8 Performance Metrics**: Weighted scoring system with different point values
- **Finish Type Multipliers**: Division-specific bonuses for different finish types
- **Historical Context**: Only uses stats available at time of each fight
- **Dynamic Opponent Quality**: Recursive quality assessment based on opposition faced

### Performance Metrics & Weights
- **Striking Ratio** (SLpM/SApM): 5 points
- **Striking Defense**: 4 points
- **Takedown Defense**: 4 points
- **Knockdowns**: 4 points
- **Submission Attempts**: 3 points
- **Takedown Average**: 2 points
- **Striking Accuracy**: 2 points
- **Takedown Accuracy**: 1 point

### Division-Specific Features
- Custom prime age ranges for each weight class
- Finish type multipliers that reflect statistical rarity
- Age weighting parameters tailored to each division

## Getting Started

### 1. Load Test Data
Click the **"Load Test Data"** button to populate the system with realistic fighter data including:
- 20 fighters across 4 divisions (Lightweight, Welterweight, Featherweight, Women's Strawweight)
- 20+ realistic fight results with actual fight statistics
- Proper opponent relationships and fight histories

### 2. View Rankings
Navigate to the **Rankings** tab to see the calculated rankings for each division. The system shows:
- Current Elo ratings
- Fighter records (W-L-D)
- Key performance metrics
- Age information

### 3. Add Your Own Data
- **Add Fighter**: Input fighter name, birthdate, and weight class
- **Add Fight**: Record detailed fight statistics between any two fighters
- **Manage Data**: Export, import, or clear data as needed

## Test Data Details

The test dataset includes realistic fighters and fights such as:

**Men's Lightweight:**
- Islam Makhachev vs Charles Oliveira (SUB, R2)
- Charles Oliveira vs Justin Gaethje (SUB, R1)
- Dustin Poirier vs Justin Gaethje (KO, R2)

**Men's Welterweight:**
- Leon Edwards vs Kamaru Usman (KO, R5)
- Khamzat Chimaev vs Colby Covington (DEC)
- Belal Muhammad vs Leon Edwards (DEC)

**Men's Featherweight:**
- Alexander Volkanovski vs Max Holloway (DEC)
- Max Holloway vs Josh Emmett (TKO, R5)

**Women's Strawweight:**
- Zhang Weili vs Rose Namajunas (DEC)
- Zhang Weili vs Carla Esparza (SUB, R2)

## How the Algorithm Works

### 1. Performance Metric Calculation
Each fighter's performance is calculated by taking their fight statistics weighted by:
- Age at time of fight (Gaussian curve around prime age)
- Recency of the fight
- Division-specific parameters

### 2. Age Weighting (Asymmetric)
The system uses different sigma values for pre-prime vs post-prime:
- Pre-prime: More forgiving curve (σ ≈ 4.5-5.2)
- Post-prime: More punishing curve (σ ≈ 2.6-3.4)

### 3. Finish Type Multipliers
Finish bonuses vary by division and include:
- **KO/TKO**: Higher multipliers in lighter divisions (1.65× in flyweight vs 1.25× in heavyweight)
- **Submissions**: Higher multipliers in heavier divisions
- **Round bonuses**: Earlier finishes receive higher multipliers
- **Opponent quality**: Finishes against ranked opponents get bonuses

### 4. Final Elo Calculation
`Final Elo = 1500 + (adjustedScore × 50) + fightHistoryElo`

Where:
- `adjustedScore` = stats score × age weight × opponent quality weight
- `fightHistoryElo` = cumulative Elo changes from all fights (capped at ±200)

## Usage Tips

1. **Start with Test Data**: Use the test data to understand how the system works
2. **Explore Different Divisions**: Each weight class has unique characteristics
3. **Add Recent Fights**: The system weights recent performances more heavily
4. **Check Age Impact**: Notice how fighters past their prime are appropriately penalized
5. **Finish Type Impact**: Observe how different finish types affect rankings differently across divisions

## Technical Implementation

- **Framework**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS
- **Data Persistence**: Local storage with export/import functionality
- **Calculations**: Custom TypeScript implementation of all ranking algorithms
- **UI Components**: Modular React components for each feature

## Development

```bash
# Install dependencies
npm install

# Run development server
npm run dev

# Build for production
npm run build
```

The application will be available at `http://localhost:3000`.