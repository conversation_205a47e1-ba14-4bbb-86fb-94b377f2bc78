# UFC Fighter Statistics Guide

This document provides a comprehensive overview of all available fighter statistics in the UFC ranking calculator database.

## Table of Contents
1. [Fighter Profile Data](#fighter-profile-data)
2. [Career Statistics](#career-statistics)
3. [Fight-Level Data](#fight-level-data)
4. [Round-by-Round Statistics](#round-by-round-statistics)
5. [Calculated Performance Metrics](#calculated-performance-metrics)
6. [Age-Related Data](#age-related-data)

---

## Fighter Profile Data

Basic biographical and physical attributes stored in the `fighters` table:

### Personal Information
- `id` - Unique fighter identifier
- `first_name` - Fighter's first name
- `last_name` - Fighter's last name
- `nickname` - Fighter's nickname/alias
- `birthdate` - Date of birth (YYYY-MM-DD format)
- `url` - UFC Stats profile URL

### Physical Attributes
- `height` - Height in inches (e.g., "71"")
- `weight` - Fighting weight in pounds (e.g., "155 lbs.")
- `reach` - Arm reach in inches
- `stance` - Fighting stance (Orthodox, Southpaw, Switch)
- `weight_class` - Primary weight division

---

## Career Statistics

Aggregated career totals stored in the `fighters` table:

### Fight Record
- `wins` - Total career wins
- `losses` - Total career losses
- `draws` - Total career draws
- `no_contests` - Total no contest results
- `total_fights` - Total number of UFC fights

### Win Methods
- `ko_tko_wins` - Wins by knockout or technical knockout
- `submission_wins` - Wins by submission
- `decision_wins` - Wins by decision (all types)
- `finish_rate` - Percentage of wins by finish (KO/TKO/SUB)

### Striking Statistics
- `total_significant_strikes_landed` - Career significant strikes landed
- `total_significant_strikes_attempted` - Career significant strikes attempted
- `total_significant_strikes_absorbed` - Career significant strikes absorbed
- `striking_accuracy` - Percentage of significant strikes that land
- `strikes_absorbed_per_minute` - Average strikes absorbed per minute
- `striking_defense_percentage` - Percentage of opponent strikes avoided

### Grappling Statistics
- `total_takedowns_landed` - Career takedowns completed
- `total_takedowns_attempted` - Career takedown attempts
- `takedown_accuracy` - Percentage of takedowns completed
- `takedowns_average_per_15min` - Average takedowns per 15 minutes
- `total_knockdowns` - Total knockdowns scored
- `total_submission_attempts` - Total submission attempts
- `submissions_average_per_15min` - Average submissions per 15 minutes

### Control & Time Statistics
- `total_control_time_seconds` - Total control time in seconds
- `total_fight_time_seconds` - Total time spent fighting
- `avg_fight_time_seconds` - Average fight duration

---

## Fight-Level Data

Individual fight information stored in the `fights` table:

### Fight Information
- `id` - Unique fight identifier
- `event_id` - Reference to the event
- `bout` - Bout description (e.g., "Main Event", "Title Fight")
- `weight_class` - Weight class for this specific fight

### Fighter References
- `fighter1_id` - First fighter's ID
- `fighter2_id` - Second fighter's ID
- `winner_id` - ID of the winning fighter (null for draws/NC)

### Result Details
- `result_method` - How the fight ended (KO/TKO, Submission, Decision types, etc.)
- `result_round` - Round the fight ended
- `result_time` - Time in the round (MM:SS format)

### Age Data (per fighter)
- `fighter1_age` / `fighter2_age` - Age at fight time (decimal years)
- `fighter1_age_months` / `fighter2_age_months` - Age in total months
- `fighter1_age_bracket` / `fighter2_age_bracket` - Age range (e.g., "27-28")
- `fighter1_age_quarter` / `fighter2_age_quarter` - Quarterly age (27.0, 27.25, 27.5, 27.75)

---

## Round-by-Round Statistics

Detailed statistics per round stored in the `fight_stats` table:

### Core Statistics
- `round_number` - Round number (1-5)
- `knockdowns` - Knockdowns scored in the round
- `submission_attempts` - Submission attempts in the round
- `reversals` - Position reversals
- `control_time` - Time in control (MM:SS format)

### Striking Breakdown
#### Overall Strikes
- `sig_strikes_landed` - Significant strikes landed
- `sig_strikes_attempted` - Significant strikes attempted
- `sig_strikes_pct` - Significant strike accuracy percentage
- `total_strikes_landed` - All strikes landed (including non-significant)
- `total_strikes_attempted` - All strikes attempted

#### Target Areas
- `head_strikes_landed` / `head_strikes_attempted` - Head strikes
- `body_strikes_landed` / `body_strikes_attempted` - Body strikes
- `leg_strikes_landed` / `leg_strikes_attempted` - Leg strikes

#### Position-Based Strikes
- `distance_strikes_landed` / `distance_strikes_attempted` - At distance
- `clinch_strikes_landed` / `clinch_strikes_attempted` - In the clinch
- `ground_strikes_landed` / `ground_strikes_attempted` - On the ground

### Grappling
- `takedowns_landed` - Successful takedowns
- `takedowns_attempted` - Takedown attempts
- `takedowns_pct` - Takedown success percentage

---

## Calculated Performance Metrics

Advanced metrics calculated by the application (not stored in database):

### Research-Based Metrics (with ranking weights)
1. **`strikesAbsorbedPerMinute`** (Weight: 6) - #1 predictor of fight outcomes
2. **`controlTimeAverage`** (Weight: 5) - #2 predictor, average control time per fight
3. **`strikingDefense`** (Weight: 4) - Percentage of strikes avoided
4. **`takedownDefense`** (Weight: 4) - Percentage of takedowns defended
5. **`knockdowns`** (Weight: 4) - Average knockdowns per fight
6. **`strikingRatio`** (Weight: 3) - Strikes landed vs absorbed ratio
7. **`submissionAttempts`** (Weight: 3) - Average submission attempts
8. **`takedownAverage`** (Weight: 2) - Average takedowns per fight
9. **`strikingAccuracy`** (Weight: 2) - Percentage of strikes that land
10. **`takedownAccuracy`** (Weight: 1) - Percentage of takedowns completed

### Additional Calculated Metrics
- **`dominanceScore`** - Combined metric of fight control and damage
- **`finishRate`** - Percentage of wins by finish
- **`ageWeight`** - Performance adjustment based on fighter age
- **`recencyWeight`** - Weight based on how recent the fight was
- **`qualityWeight`** - Opponent quality adjustment

---

## Age-Related Data

### Fighter Age Tracking
Each fight record includes precise age calculations:
- **Decimal Age** - Exact age to 1 decimal place (e.g., 27.3 years)
- **Age in Months** - Total months old for precise bucketing
- **Age Bracket** - String representation for grouping (e.g., "27-28")
- **Age Quarter** - Quarterly precision (0, .25, .5, .75)

### Division-Specific Age Configuration
Each weight class has optimal age ranges defined:
- **`primeRange`** - [min, max] ages for peak performance
- **`median`** - Median age for the division
- **`sigmaPre`** - Standard deviation before median
- **`sigmaPost`** - Standard deviation after median

---

## Usage Examples

### Accessing Fighter Stats in Code
```typescript
// Fighter object includes
const fighter = {
  id: "123",
  name: "Fighter Name",
  birthdate: new Date("1990-01-01"),
  weightClass: "Lightweight",
  fights: [
    {
      id: "456",
      date: new Date("2023-01-01"),
      ageAtFight: 33.0,        // Fighter was 33.0 years old
      ageBracket: "33-34",     // In the 33-34 age bracket
      ageQuarter: 33.0,        // First quarter of age 33
      stats: {
        significantStrikesLanded: 45,
        controlTime: 180,      // 3 minutes
        // ... other stats
      }
    }
  ]
};
```

### Key Performance Indicators
The ranking system prioritizes:
1. **Damage Avoidance** - Strikes absorbed per minute (most predictive)
2. **Control** - Average control time per fight
3. **Defense** - Both striking and takedown defense
4. **Offense** - Knockdowns, submissions, and striking effectiveness

---

## Notes

- All time-based statistics are stored in seconds for consistency
- Percentages are stored as decimals (0-100)
- Age calculations use precise date arithmetic (365.25 days per year)
- Career statistics are aggregated from individual fight data
- Performance metrics are weighted by fighter age and fight recency