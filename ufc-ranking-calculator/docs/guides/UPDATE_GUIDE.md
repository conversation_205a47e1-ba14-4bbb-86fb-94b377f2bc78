# UFC Data Update Guide

## Overview

The `fetch-new-ufc-data.js` script automatically fetches new UFC event data from the scrape_ufc_stats repository and updates your local database.

## How It Works

1. Connects to your local database to find the latest event date
2. Fetches all data from the repository
3. Filters for events newer than your latest event (and after 2005)
4. Updates both the SQLite database and CSV files
5. Logs the update summary

## Manual Usage

Run the script manually:
```bash
node scripts/fetch-new-ufc-data.js
```

## Automatic Weekly Updates

### Option 1: Cron Job (Linux/Mac)

Add to your crontab (`crontab -e`):
```bash
# Run every Sunday at 3 AM
0 3 * * 0 cd /path/to/ufc-ranking-calculator && node scripts/fetch-new-ufc-data.js >> data/update.log 2>&1
```

### Option 2: Task Scheduler (Windows)

1. Open Task Scheduler
2. Create Basic Task
3. Set trigger to "Weekly" on Sunday
4. Set action to run: `node C:\path\to\ufc-ranking-calculator\scripts\fetch-new-ufc-data.js`

### Option 3: GitHub Actions

Create `.github/workflows/update-data.yml`:
```yaml
name: Update UFC Data
on:
  schedule:
    - cron: '0 3 * * 0'  # Weekly on Sunday
  workflow_dispatch:     # Manual trigger

jobs:
  update:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm install
      - run: node scripts/fetch-new-ufc-data.js
      - uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: 'Update UFC data'
```

## What Gets Updated

- **New Events**: Any UFC event after your latest date
- **New Fights**: All fights from new events
- **New Fighters**: Any fighters appearing in new events
- **Fight Statistics**: Detailed stats for all new fights

## Logs

Updates are logged to `data/update.log` with:
- Timestamp of update
- Number of new events/fights/stats added
- Latest event information

## Notes

- Only fetches events from 2005 onwards (modern era)
- Automatically handles duplicate fighters
- Maintains data consistency between SQLite and CSV files
- Safe to run multiple times (idempotent)