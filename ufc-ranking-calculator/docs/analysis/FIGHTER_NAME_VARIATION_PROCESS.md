# Fighter Name Variation Investigation Process

## Overview
This document outlines the process for investigating and resolving fighter name variations in the UFC database. The issue occurs when fighters are listed under different names in source data (e.g., maiden name vs. married name, with/without middle names, etc.).

## Example Case: <PERSON> / <PERSON>
- **Database Entry**: <PERSON> (ID: 1206)
- **CSV Variations**: "<PERSON>" and "<PERSON>"
- **Status**: <PERSON>perly merged - all 10 fights correctly linked

## Investigation Steps

### 1. Check Database Schema
First, understand the database structure:
```bash
node scripts/check-schema.js | grep -A 20 "=== fighters ==="
```

Key fields:
- `id`: Primary key
- `first_name`, `last_name`: Name fields
- `wins`, `losses`, `draws`: Record
- No single "name" field - names are split

### 2. Search for Specific Fighter Variations
Create a script to search for name variations:
```javascript
// Example: scripts/check-[fighter-name]-variations.js
const searchTerms = [
  { first: 'ian', last: 'garry' },
  { first: 'ian', last: 'machado' },
  { first: 'machado', last: 'garry' }
];

// Search with LIKE queries for partial matches
// Also search combined names: first_name || ' ' || last_name
```

### 3. Check Source CSV Files
Compare database entries with source data:
```bash
# Search in export files
cd scrape_ufc_stats/exports
grep -i "fighter.*name" *.csv | head -20

# Check specific variations
grep -i "machado.*garry\|garry.*machado" *.csv
```

### 4. Analyze Fight Linkage
Verify all fights are properly linked:
```sql
-- Get all fights for a fighter
SELECT f.*, e.event_name 
FROM fights f
JOIN events e ON f.event_id = e.id
WHERE f.fighter1_id = ? OR f.fighter2_id = ?
```

### 5. Check for Missing Stats
Ensure fight statistics are complete:
```sql
-- Find fights without stats
SELECT f.* FROM fights f
LEFT JOIN fight_stats fs ON f.id = fs.fight_id
WHERE (f.fighter1_id = ? OR f.fighter2_id = ?)
AND fs.id IS NULL
```

## Common Name Variation Patterns

### 1. Marriage Name Changes
- Michelle Waterson → Michelle Waterson-Gomez
- Tecia Torres → Tecia Pennington
- Katlyn Chookagian → Katlyn Cerminara

### 2. Middle Names / Full Names
- Ian Garry → Ian Machado Garry
- Christian Duncan → Christian Leroy Duncan

### 3. Name Order / Formatting
- JunYong Park → Junyong Park
- Danaa Batgerel → Batgerel Danaa

### 4. Nicknames
- Bobby Green → King Green

## Comprehensive Name Variation Detection Script

The `find-all-name-variations.js` script provides a complete analysis:

1. **Loads CSV data**: Reads fighter names from source files
2. **Compares with database**: Finds mismatches
3. **Identifies patterns**: 
   - Exact matches
   - Partial matches (contains)
   - Split compound names
   - Fuzzy matching
4. **Finds duplicates**: Detects potential duplicate entries

## Results Summary

From the analysis, we found:
- **7 fighters** with name variations between CSV and database
- **2025 fighters** in CSV but not in database (historical fighters)
- **All variations properly handled** during import

## Key Fighters to Review

| Fighter | Database Name | CSV Name | Status |
|---------|--------------|----------|---------|
| Ian Garry | Ian Garry | Ian Machado Garry | ✓ Merged |
| Michelle Waterson | Michelle Waterson | Michelle Waterson-Gomez | ✓ Merged |
| JunYong Park | Junyong Park | JunYong Park | ✓ Merged |
| Zachary Reese | Zach Reese | Zachary Reese | ✓ Merged |

## Next Steps for Other Fighters

1. **Run the comprehensive check**:
   ```bash
   node scripts/find-all-name-variations.js > name-variations-report.txt
   ```

2. **For specific fighter investigation**:
   - Copy `check-ian-garry-names.js`
   - Modify search terms for the fighter in question
   - Run analysis

3. **If duplicates found**:
   - Verify they're actually the same person (check fight history)
   - Use merge scripts if needed
   - Update fighter records

4. **For missing stats**:
   - Check if fights exist under alternate names
   - Import missing stats from source data
   - Verify fight linkage

## Important Notes

- The current import process handles most name variations automatically
- Manual intervention only needed for edge cases
- Always verify fight history before merging fighters
- Keep records of all name mappings for future reference