# Fighter Divisional Record Structure

## Proposed Implementation for Multi-Division Fighter Handling

### Core Principles
1. **Separation of Concerns**: Rankings/calculations use division-specific records, UI shows complete career
2. **Data Integrity**: No duplication of fight data, just different views
3. **Performance**: Efficient querying and caching by division

### Enhanced Data Structure

```typescript
// For calculations - Division-specific view
interface FighterDivisionRecord {
  fighterId: string;
  fighterName: string;
  weightClass: WeightClass;
  
  // Division-specific data
  fights: Fight[]; // Only fights at this weight class
  elo: number; // Division-specific ELO
  metrics: PerformanceMetrics; // Calculated only from this division
  
  // Division-specific stats
  divisionRecord: {
    wins: number;
    losses: number;
    draws: number;
    noContests: number;
  };
  
  // Activity tracking
  lastFightDate: Date;
  isActive: boolean; // Has fought in this division recently
  
  // Performance indicators
  winStreak: number;
  titleFights: number;
  rankingHistory: RankingSnapshot[];
}

// For UI display - Complete career view
interface FighterCompleteRecord {
  // Core identity
  id: string;
  name: string;
  birthdate: Date;
  
  // Career overview
  mainWeightClass: WeightClass; // Most recent or most fights
  currentWeightClass: WeightClass; // Latest fight's division
  allFights: Fight[]; // All fights across all divisions
  
  // Division breakdown
  divisionRecords: Map<WeightClass, FighterDivisionRecord>;
  
  // Career totals
  careerStats: {
    totalFights: number;
    totalWins: number;
    totalLosses: number;
    totalDraws: number;
    finishRate: number;
    // ... other aggregate stats
  };
  
  // Cross-division analysis
  divisionHistory: {
    weightClass: WeightClass;
    firstFight: Date;
    lastFight: Date;
    moveReason?: 'permanent' | 'one-off' | 'testing';
  }[];
}

// Helper type for querying
interface DivisionFilter {
  weightClass: WeightClass;
  includeOpenWeight?: boolean; // Include catch-weight fights
  includeTitleFights?: boolean; // Special handling for title fights
}
```

### Implementation Strategy

#### 1. Data Loading
```typescript
// Efficient loading pattern
async function loadFighterByDivision(
  fighterId: string, 
  weightClass: WeightClass
): Promise<FighterDivisionRecord> {
  const allFights = await loadFighterFights(fighterId);
  const divisionFights = allFights.filter(f => 
    f.weightClass === weightClass || 
    (includeOpenWeight && f.weightClass === 'Catch Weight')
  );
  
  return {
    fighterId,
    fighterName: getFighterName(fighterId),
    weightClass,
    fights: divisionFights,
    elo: calculateDivisionElo(divisionFights),
    metrics: calculatePerformanceMetrics(divisionFights),
    // ... other fields
  };
}
```

#### 2. Ranking Calculations
```typescript
// Division-specific rankings
function calculateDivisionRankings(weightClass: WeightClass): Ranking[] {
  // Get all fighters who have fought in this division
  const divisionFighters = await getFightersInDivision(weightClass);
  
  // Create division-specific records
  const divisionRecords = divisionFighters.map(fighter => 
    loadFighterByDivision(fighter.id, weightClass)
  );
  
  // Calculate rankings using only division-specific data
  return rankFighters(divisionRecords);
}
```

#### 3. Special Cases to Handle

##### Catch Weight Fights
```typescript
// Option 1: Assign to nearest weight class
// Option 2: Exclude from divisional records
// Option 3: Create special handling for super fights

function assignCatchWeightFight(fight: Fight): WeightClass {
  const catchWeight = parseCatchWeight(fight.catchWeightLimit);
  return findNearestWeightClass(catchWeight);
}
```

##### Title Fights in Different Divisions
```typescript
// Special handling for championship fights
interface ChampionshipException {
  fightId: string;
  challengerNormalDivision: WeightClass;
  titleDivision: WeightClass;
  isInterimTitle: boolean;
}
```

### Migration Path

1. **Phase 1**: Create division filtering without changing data structure
   - Add methods to filter fights by weight class
   - Test calculations with filtered data

2. **Phase 2**: Implement caching layer
   - Cache division-specific calculations
   - Add invalidation on new fight data

3. **Phase 3**: Update UI to show division splits
   - Add division selector to fighter pages
   - Show cross-division career timeline

4. **Phase 4**: Optimize database queries
   - Add indexes on weight_class columns
   - Consider materialized views for common queries

### Benefits

1. **Accuracy**: True division-specific performance metrics
2. **Fairness**: Fighters ranked only against actual divisional competition  
3. **Flexibility**: Easy to add new weight classes or handle special events
4. **Performance**: Calculations limited to relevant fights only
5. **Clarity**: Clear separation between career achievements and divisional dominance

### Example Use Cases

```typescript
// Get Holloway's featherweight record for rankings
const hollowayFW = await loadFighterByDivision('holloway-id', 'Featherweight');
// Only includes his 145lb fights, separate ELO, metrics, etc.

// Get Holloway's complete record for profile page  
const hollowayComplete = await loadFighterCompleteRecord('holloway-id');
// Shows all fights, but organized by division

// Calculate lightweight rankings
const lightweightRankings = await calculateDivisionRankings('Lightweight');
// Holloway appears here based only on his lightweight performances
```

### Considerations

1. **Minimum Fights**: Require minimum fights in division for ranking eligibility
2. **Recency**: Weight recent fights more heavily to detect division changes
3. **Catch Weights**: Clear policy on how to handle
4. **Tournament/Special Events**: May need special handling