# Modern UFC Database (2005-Present)

## Overview

The UFC database has been filtered to contain only modern era data from 2005 onwards. This ensures all rankings and calculations are based on relevant, contemporary MMA competition.

## Database Structure

### Active Databases (2005-Present)
Located in `/data/`:
- **SQLite Database**: `ufc_data.db` - Main database used by the application
  - `events` table: 678 events
  - `fighters` table: 2,339 fighters
  - `fights` table: 7,703 fights  
  - `fight_stats` table: 22,890 statistics records

- **CSV Files**: (for reference/import)
  - `ufc_events.csv` - 678 events
  - `ufc_fights.csv` - 7,703 fights  
  - `ufc_fighters.csv` - 2,339 fighters
  - `ufc_fight_stats.csv` - 22,912 fight statistics records

### Legacy Databases (Full Historical Data)
Preserved in `/data/legacy/`:
- Contains all UFC events from UFC 2 (1994) to present
- 732 total events with 8,143 fights
- Both CSV files and SQLite database backups
- Kept for historical reference

## Key Statistics

### Date Range
- **Earliest Event**: UFC 51: Super Saturday (February 5, 2005)
- **Latest Event**: UFC Fight Night: <PERSON> vs<PERSON> <PERSON> (May 17, 2025)

### Data Removed
- 54 events before 2005
- 440 fights before 2005
- 2,015 fighters who only competed before 2005
- 1,444 fight statistics records

### Notable Inclusions
- UFC 100 (July 11, 2009) - Complete with all 11 fights and detailed stats
- All modern champions and top contenders
- Complete fight statistics for accurate rankings

## Why 2005?

Starting from 2005 captures:
- The TUF era and MMA's mainstream growth
- Modern weight classes and unified rules
- Higher quality statistical data
- More relevant fighter comparisons for current rankings

## Application Usage

- **Rankings & Calculations**: Use the modern SQLite database (`/data/ufc_data.db`)
- **Tests**: Use the modern database for all accuracy testing
- **Website Display**: Currently shows modern data only (can be configured to show legacy)

The application automatically uses the modern database through the database connection in `src/lib/database.ts`.