# Division-Specific Fighter Records Implementation

## Overview
This implementation separates fighter records by weight class, treating "Fighter X at Featherweight" and "Fighter X at Lightweight" as distinct entities for ranking calculations while maintaining unified career records for display.

## Implementation Details

### 1. New Type Definitions (`src/types/division-records.ts`)
- **FighterDivisionRecord**: Division-specific record for calculations
  - Contains only fights from that weight class
  - Independent ELO rating
  - Division-specific performance metrics
  - Win/loss/draw record for that division only
  
- **FighterCompleteRecord**: Complete career view for UI
  - All fights across all divisions
  - Map of division-specific records
  - Career totals and timeline

### 2. Division Data Adapter (`src/lib/fighter-division-adapter.ts`)
Key functions:
- `loadFighterDivisionRecord()`: Load a fighter's record for a specific division
- `loadDivisionFighters()`: Get all fighters who competed in a division
- `loadFighterCompleteRecord()`: Load complete multi-division career data

Features:
- Excludes catch weight fights (as requested)
- Calculates division-specific statistics
- Tracks current streak within division
- Identifies title fights and wins

### 3. Division Calculations (`src/lib/division-calculations.ts`)
- `calculateDivisionMetrics()`: Performance metrics using only division fights
- `calculateDivisionElo()`: ELO rating specific to the division
- `calculateDivisionRankings()`: Generate rankings for a weight class
- Division-specific age weighting using existing age configs

### 4. API Endpoint (`src/app/api/rankings/division/route.ts`)
New endpoint: `/api/rankings/division?weightClass=Men_Lightweight`

Query parameters:
- `weightClass` (required): The specific division
- `limit`: Number of results (default: 50)
- `includeInactive`: Include fighters inactive >2 years

Response includes:
- Division-specific rankings with ELO
- Fighter records (wins-losses) for that division only
- Performance metrics calculated from division fights only

## Key Design Decisions

1. **No Minimum Fight Threshold**: Any fighter with 1+ fight in a division is ranked
2. **Catch Weight Excluded**: These fights don't count toward any division
3. **Independent ELO**: Each division has completely separate ELO calculations
4. **Preserve Original Data**: No changes to database structure, only filtered views

## Usage Examples

### Getting Lightweight Rankings
```javascript
// API call
GET /api/rankings/division?weightClass=Men_Lightweight&limit=20

// Returns fighters ranked by their lightweight-only performance
```

### Multi-Division Fighter Example
Max Holloway would have:
- Featherweight record: 20-7 (27 fights)
- Lightweight record: 1-1 (2 fights)
- Separate ELO ratings for each
- Different rankings in each division

### Testing
Run the test script to see division separation in action:
```bash
node scripts/test-division-separation.js
```

## Benefits

1. **Accuracy**: Rankings reflect true divisional performance
2. **Fairness**: Can't pad record with fights from other divisions  
3. **Clarity**: Clear what a fighter has achieved in each division
4. **Flexibility**: Easy to see career progression across weights

## Next Steps

1. Update the UI to show division-specific views
2. Add division selector to fighter profile pages
3. Create visualizations for cross-division careers
4. Consider adding historical division rankings