# Database-Driven Calculations Migration Guide

This guide outlines the migration from hardcoded calculation parameters to a fully database-driven system for the UFC ranking calculator.

## Overview

The goal is to move all hardcoded values from `calculations.ts` and related config files into the database, making the system more flexible and data-driven. The only exception is removing opponent rank multipliers entirely as requested.

## Current Hardcoded Values to Migrate

### 1. Division Age Configurations
**Current Location**: `src/config/divisions.ts` - `divisionAgeConfig`
- Median age per division
- Sigma pre/post values for age weight curves
- Data cutoff dates per division

### 2. Finish Multipliers
**Current Location**: `src/config/finish-multipliers.ts`
- KO/TKO multipliers per division
- Submission multipliers per division  
- Decision multipliers per division

### 3. Round Multipliers
**Current Location**: `src/config/divisions.ts` - `roundMultipliers`
- Round 1: 1.5
- Round 2: 1.25
- Round 3: 1.0
- Round 4: 0.9
- Round 5: 0.85

### 4. Optimal Metric Weights
**Current Location**: `src/config/optimal-weights.ts`
- strikingAccuracy: 1.8
- strikingDefense: 2.5
- takedownDefense: 1.2
- strikingRatio: 0.8
- takedownAccuracy: 0.6
- knockdowns: 0.4
- takedownAverage: 0.3
- submissionAttempts: 0.2

### 5. Recency Weights
**Current Location**: `src/config/optimal-weights.ts`
- halfLife: 48 months
- minimumWeight: 0.1

### 6. Dominance Score Values
**Current Location**: `calculations.ts` lines 297-396
- KO/TKO base: 3.0
- Submission base: 2.5
- Unanimous Decision: 1.0
- Majority Decision: 0.6
- Split Decision: 0.3
- DQ/Draw: 0.2
- Loss: -1.0
- Round bonuses (R1 KO: +1.5, R1 Sub: +1.0, etc.)
- Statistical bonuses (striking ratio > 1.5: +0.5, etc.)

### 7. Algorithm Parameters
**Current Location**: `calculations.ts` lines 785-811
- Convergence threshold: 0.5
- Max iterations: 50
- SOR relaxation factor: 1.2
- ELO K-factor: 32
- Initial ELO: 1500
- ELO bounds: [1000, 3000]

### 8. Items to Remove (Per Request)
- Opponent rank multipliers (top 3 = 1.6, top 5 = 1.45, etc.)

## Proposed Database Schema

### Table: calculation_parameters
```sql
CREATE TABLE calculation_parameters (
    id INTEGER PRIMARY KEY,
    category TEXT NOT NULL,
    subcategory TEXT,
    key TEXT NOT NULL,
    value REAL NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(category, subcategory, key)
);
```

### Table: division_parameters
```sql
CREATE TABLE division_parameters (
    id INTEGER PRIMARY KEY,
    division TEXT NOT NULL,
    parameter_type TEXT NOT NULL,
    key TEXT NOT NULL,
    value REAL NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(division, parameter_type, key)
);
```

### Table: dominance_scoring
```sql
CREATE TABLE dominance_scoring (
    id INTEGER PRIMARY KEY,
    result_type TEXT NOT NULL,
    method TEXT NOT NULL,
    base_score REAL NOT NULL,
    round_1_bonus REAL DEFAULT 0,
    round_2_bonus REAL DEFAULT 0,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(result_type, method)
);
```

### Table: statistical_bonuses
```sql
CREATE TABLE statistical_bonuses (
    id INTEGER PRIMARY KEY,
    stat_type TEXT NOT NULL,
    threshold REAL NOT NULL,
    bonus_value REAL NOT NULL,
    comparison_operator TEXT NOT NULL, -- '>', '<', '>=', '<=', '='
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Implementation Steps

### Phase 1: Database Setup
1. Create new tables in the database
2. Populate tables with current hardcoded values
3. Add indexes for performance

### Phase 2: Data Access Layer
1. Create database queries to fetch parameters
2. Implement caching mechanism for performance
3. Create TypeScript interfaces for parameter types

### Phase 3: Code Migration
1. Update calculations.ts to fetch values from database
2. Remove hardcoded config files
3. Update imports throughout the codebase
4. Remove opponent rank multiplier logic

### Phase 4: Testing & Validation
1. Verify calculations produce same results
2. Performance testing
3. Create admin interface for parameter management

## Benefits

1. **Flexibility**: Parameters can be adjusted without code changes
2. **Experimentation**: Easy A/B testing of different values
3. **Transparency**: All calculation parameters visible in database
4. **Auditability**: Track parameter changes over time
5. **Division-specific tuning**: Easy to customize per weight class

## Migration Checklist

- [ ] Create database tables
- [ ] Write migration script to populate initial values
- [ ] Create parameter fetching functions
- [ ] Update calculations.ts to use database values
- [ ] Remove opponent rank multipliers
- [ ] Remove hardcoded config files
- [ ] Update tests
- [ ] Document new parameter management process
- [ ] Create parameter update scripts

## Sample Data Examples

### calculation_parameters entries:
```sql
-- Algorithm parameters
('algorithm', NULL, 'convergence_threshold', 0.5, 'Maximum change threshold for convergence'),
('algorithm', NULL, 'max_iterations', 50, 'Maximum iterations before stopping'),
('algorithm', NULL, 'sor_relaxation_factor', 1.2, 'SOR acceleration factor'),
('elo', NULL, 'k_factor', 32, 'ELO K-factor for rating changes'),
('elo', NULL, 'initial_rating', 1500, 'Starting ELO rating'),
('elo', NULL, 'min_rating', 1000, 'Minimum allowed ELO rating'),
('elo', NULL, 'max_rating', 3000, 'Maximum allowed ELO rating'),

-- Metric weights
('metrics', NULL, 'striking_accuracy', 1.8, 'Weight for striking accuracy metric'),
('metrics', NULL, 'striking_defense', 2.5, 'Weight for striking defense metric'),
-- etc...

-- Recency parameters
('recency', NULL, 'half_life_months', 48, 'Half-life for fight recency decay'),
('recency', NULL, 'minimum_weight', 0.1, 'Minimum recency weight'),

-- Round multipliers
('round_multipliers', NULL, 'round_1', 1.5, 'Multiplier for round 1 finishes'),
('round_multipliers', NULL, 'round_2', 1.25, 'Multiplier for round 2 finishes'),
-- etc...
```

### division_parameters entries:
```sql
-- Age parameters
('Lightweight', 'age', 'median', 30.0, 'Median age for Lightweight division'),
('Lightweight', 'age', 'sigma_pre', 4.0, 'Pre-peak age curve sigma'),
('Lightweight', 'age', 'sigma_post', 3.0, 'Post-peak age curve sigma'),

-- Finish multipliers
('Lightweight', 'finish_multipliers', 'ko_tko', 1.35, 'KO/TKO multiplier for Lightweight'),
('Lightweight', 'finish_multipliers', 'submission', 1.30, 'Submission multiplier for Lightweight'),
('Lightweight', 'finish_multipliers', 'decision', 0.92, 'Decision multiplier for Lightweight'),
-- etc...
```

### dominance_scoring entries:
```sql
('win', 'KO', 3.0, 1.5, 0.75, 'Base score for KO/TKO wins'),
('win', 'TKO', 3.0, 1.5, 0.75, 'Base score for TKO wins'),
('win', 'Submission', 2.5, 1.0, 0.5, 'Base score for submission wins'),
('win', 'Decision - Unanimous', 1.0, 0, 0, 'Base score for unanimous decision wins'),
('win', 'Decision - Majority', 0.6, 0, 0, 'Base score for majority decision wins'),
('win', 'Decision - Split', 0.3, 0, 0, 'Base score for split decision wins'),
('win', 'DQ', 0.2, 0, 0, 'Base score for DQ wins (neutral)'),
('loss', 'ALL', -1.0, 0, 0, 'Base score for losses'),
('loss', 'Decision - Split', -0.6, 0, 0, 'Adjusted score for split decision losses'),
('loss', 'DQ', 0.2, 0, 0, 'Base score for DQ losses (neutral)'),
('draw', 'ALL', 0.2, 0, 0, 'Base score for draws'),
('nc', 'ALL', 0.1, 0, 0, 'Base score for no contests'),
```

### statistical_bonuses entries:
```sql
('striking_ratio', 1.5, 0.5, '>', 'Bonus for high striking ratio'),
('knockdowns', 0, 0.3, '>', 'Bonus per knockdown (multiplied by count)'),
('takedown_differential', 2, 0.4, '>', 'Bonus for takedown advantage'),
```

## Notes

- All numeric values should be stored as REAL (floating point) for consistency
- Consider adding version tracking for parameter sets
- May want to add effective_date columns for historical tracking
- Cache invalidation strategy needed when parameters change
- Consider read-replica for parameter queries to avoid impacting main DB

## Next Steps

1. Review and approve this design
2. Create database migration scripts
3. Implement parameter loading system
4. Begin phased migration of hardcoded values
5. Remove opponent rank multiplier system entirely