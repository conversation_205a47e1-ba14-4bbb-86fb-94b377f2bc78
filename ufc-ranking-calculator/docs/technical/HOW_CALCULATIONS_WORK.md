# How The UFC Ranking Calculator Works

## 🎯 Overview

This system calculates fighter rankings using a sophisticated ELO-based algorithm that considers:
- **Fight outcomes** (wins, losses, draws)
- **Performance metrics** (strikes, takedowns, control time)
- **Fighter age** (with division-specific aging curves)
- **Opponent quality** (beating better opponents = more points)
- **Finish types** (KO/TKO worth more than decisions)
- **Fight recency** (recent fights weighted more heavily)

The magic happens through an **iterative convergence algorithm** that calculates relative rankings by considering how all fighters performed against each other.

---

## 📊 Step-by-Step Process

### Step 1: Data Collection & Filtering

When calculating rankings, the system first:

1. **Loads all fighters** from the database
2. **Filters out invalid fights**:
   - ❌ No Contest fights (except marijuana-related ones)
   - ❌ Catch Weight fights
   - ✅ All other fights are included

```typescript
// Example: System checks each fight
if (fight.result_method === 'No Contest' && !MARIJUANA_NC_FIGHT_IDS.includes(fight.id)) {
  // Skip this fight
  return;
}

if (fight.weight_class === 'Catch Weight') {
  // Skip this fight
  return;
}
```

---

### Step 2: Calculate Performance Metrics

For each fighter, the system calculates **11 key metrics**:

#### **Defensive Metrics** (Most Important)
1. **Strikes Absorbed Per Minute** (Weight: 6) - #1 predictor
   - How many strikes they take per minute
   - LOWER is better (inverted scoring)
   
2. **Control Time Average** (Weight: 5) - #2 predictor
   - How long they control opponents on the ground
   - Measured in minutes per fight

3. **Striking Defense** (Weight: 4)
   - Percentage of opponent strikes avoided
   - Formula: `(Strikes Attempted - Strikes Landed) / Strikes Attempted × 100`

4. **Takedown Defense** (Weight: 4)
   - Percentage of takedowns stuffed
   - Formula: `(Takedowns Attempted - Takedowns Landed) / Takedowns Attempted × 100`

#### **Offensive Metrics**
5. **Knockdowns** (Weight: 4)
   - Average knockdowns per fight

6. **Striking Ratio** (Weight: 3)
   - Your accuracy vs opponent's accuracy
   - Formula: `(Your Landed/Attempted) / (Opponent Landed/Attempted)`

7. **Submission Attempts** (Weight: 3)
   - Average submission attempts per fight

8. **Takedown Average** (Weight: 2)
   - Average takedowns landed per fight

9. **Striking Accuracy** (Weight: 2)
   - Percentage of strikes that land

10. **Takedown Accuracy** (Weight: 1)
    - Percentage of takedowns completed

#### **Overall Performance**
11. **Dominance Score** (Weight: 6)
    - Calculated based on how you won/lost
    - Finish Rate (Weight: 3) - Percentage of wins by KO/TKO/Submission

---

### Step 3: Age-Weighted Calculations

The system applies **division-specific age curves** because different weight classes peak at different ages:

```
Heavyweight:      Peak age 31-32 (can compete longer)
Light Heavyweight: Peak age 30-31
Middleweight:     Peak age 29-30
Welterweight:     Peak age 28-29
Lightweight:      Peak age 28-29
Featherweight:    Peak age 27-28
Bantamweight:     Peak age 27-28
Flyweight:        Peak age 26-27 (peak earliest)
```

**How it works:**
- At peak age: 100% weight
- Younger/Older: Reduced weight using Gaussian curve
- Recent fights at peak age count more than fights when too young/old

---

### Step 4: Dominance Score Calculation

Each fight result gets a **dominance score**:

#### **Wins:**
- **KO/TKO**: 3.0 points
  - Round 1: +1.5 bonus (total 4.5)
  - Round 2: +0.75 bonus (total 3.75)
- **Submission**: 2.5 points
  - Round 1: +1.0 bonus (total 3.5)
  - Round 2: +0.5 bonus (total 3.0)
- **Unanimous Decision**: 1.0 points
- **Majority Decision**: 0.6 points
- **Split Decision**: 0.3 points
- **DQ Win**: 0.2 points (treated like draw)

#### **Statistical Bonuses:**
- Striking ratio > 1.5: +0.5 points
- Each knockdown: +0.3 points
- Takedowns > opponent+2: +0.4 points

#### **Losses:**
- **Any loss**: -1.0 points
- **Split decision loss**: -0.6 points (less penalty)
- **DQ loss**: 0.2 points (treated like draw)

#### **Other:**
- **Draw**: 0.2 points
- **No Contest**: Not counted (except marijuana NC = normal result)

---

### Step 5: Division Normalization

The system normalizes metrics within each weight class because:
- Heavyweights have different striking patterns than Flyweights
- Wrestlers are more common in some divisions
- Finish rates vary by weight class

**Process:**
1. Calculate min/max/average for each metric in the division
2. Scale each fighter's metrics relative to their division
3. Compare fighter to division average
4. Better than average = positive score, worse = negative

---

### Step 6: ELO Calculation

#### **Initial ELO (1500 base)**

New fighters start at 1500, then adjusted based on:
- Recent win rate (+/- 200 points)
- Finish rate in wins (+100 points if high)
- Age relative to division peak (-5 per year away from peak)

#### **ELO Change Formula**

For each fight:
```
ELO Change = K × (Actual - Expected) × Recency × Age × Finish Multiplier

Where:
- K = 32 (base factor)
- Actual = 1 for win, 0 for loss, 0.5 for draw/DQ
- Expected = 1 / (1 + 10^((OpponentELO - YourELO) / 400))
- Recency = e^(-YearsSinceFight / 5)
- Age = Gaussian weight based on age at fight time
- Finish Multiplier = Based on how fight ended
```

#### **Finish Multipliers (by division)**

Example for Lightweight:
- KO/TKO: 1.35×
- Submission: 1.25×
- Decision: 1.0×

Heavier divisions have higher KO multipliers, lighter have higher submission multipliers.

#### **Opponent Quality Multipliers**
- Top 3 opponent: 1.6×
- Top 5 opponent: 1.45×
- Top 10 opponent: 1.25×
- Top 15 opponent: 1.15×
- Top 25 opponent: 1.05×

---

### Step 7: Iterative Convergence

The system uses an **iterative algorithm** because fighter rankings are interdependent:

1. **Initialize** all fighters to calculated starting ELOs
2. **Loop** (up to 50 iterations):
   - Calculate new ELO for each fighter based on current opponent ELOs
   - Check how much ELOs changed
   - If changes < 0.5 points, stop (converged)
3. **Apply SOR acceleration** after 5 iterations to speed convergence
4. **Detect oscillation** to prevent infinite loops

**Why iterative?**
- Fighter A's ranking depends on Fighter B's ranking
- Fighter B's ranking depends on Fighter C's ranking
- Fighter C's ranking depends on Fighter A's ranking
- Must solve simultaneously!

---

### Step 8: Final Adjustments

After convergence:
1. **Round** all ELOs to whole numbers
2. **Validate** ranges (warn if < 1000 or > 3000)
3. **Sort** fighters by final ELO
4. **Generate** rankings by division

---

## 🔧 Special Cases

### Marijuana No Contest Fights
These 9 fights are treated as normal results because marijuana doesn't affect fighting ability:
- Jim Miller vs Pat Healy (Fight #7121)
- Vitor Belfort vs Kelvin Gastelum (Fight #2867)
- And 7 others...

### DQ (Disqualification) Results
- Treated as draws (0.5 actual result)
- Minimal impact on rankings
- Recognizes DQs don't reflect skill difference

### Missing Data Handling
- No striking data? Use 1.0 ratios
- No age data? Use division median
- No opponent data? Skip ELO change

---

## 📈 Example Calculation

**Jon Jones vs Alexander Gustafsson**

1. **Jones' metrics calculated**:
   - Strikes absorbed/min: 2.1 (excellent)
   - Control time: 1.2 min average
   - Striking defense: 64%
   - 5 career knockdowns

2. **Age weight**: 
   - Fight at age 26 (near LHW peak of 30)
   - Weight: 0.89

3. **Dominance score**:
   - Won by decision: 1.0 base
   - Close fight: No bonuses
   - Total: 1.0 × 0.89 = 0.89

4. **ELO change**:
   - Jones ELO: 2100, Gustafsson: 1950
   - Expected win: 68%
   - Actual: 100% (won)
   - Change: 32 × (1 - 0.68) × multipliers = +12 points

5. **Iterative adjustment**:
   - After all fights calculated
   - Converges to final ELO: 2156

---

## 🎮 The Complete Flow

```
1. Load Fighters → 2. Filter Fights → 3. Calculate Metrics
        ↓                                        ↓
6. Iterative ELO ← 5. Normalize ← 4. Apply Age Weights
        ↓
7. Convergence Check → 8. Final Rankings
```

---

## 💡 Why This Works

1. **Research-Based**: Uses metrics proven to predict MMA outcomes
2. **Context-Aware**: Considers opponent quality and fight circumstances  
3. **Division-Specific**: Recognizes weight class differences
4. **Time-Sensitive**: Recent performance matters more
5. **Holistic**: Combines multiple factors for accurate rankings

The result is a ranking system that closely mirrors expert consensus while being completely objective and data-driven!