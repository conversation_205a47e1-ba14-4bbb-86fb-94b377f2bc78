# UFC Ranking Calculator Database Structure

## Overview
The UFC Ranking Calculator uses a SQLite database (`ufc_data.db`) to store comprehensive UFC fight data, fighter statistics, and calculation parameters. The database contains 9 main tables with detailed fight history, statistics, and scoring parameters.

## Database Tables

### 1. **fighters** (2,335 records)
Stores comprehensive fighter information and career statistics.

#### Columns:
- `id` (INTEGER, PRIMARY KEY) - Unique fighter identifier
- `first_name` (TEXT) - Fighter's first name
- `last_name` (TEXT) - Fighter's last name
- `nickname` (TEXT) - Fighter's nickname
- `url` (TEXT, UNIQUE) - UFC.com profile URL
- `created_at` (TIMESTAMP) - Record creation timestamp
- `birthdate` (TEXT) - Fighter's date of birth
- `weight_class` (TEXT) - Primary weight class
- `height` (TEXT) - Fighter's height
- `weight` (TEXT) - Fighter's weight
- `reach` (TEXT) - Fighter's reach
- `stance` (TEXT) - Fighting stance (Orthodox, Southpaw, etc.)

#### Career Statistics:
- `wins` (INTEGER) - Total wins
- `losses` (INTEGER) - Total losses
- `draws` (INTEGER) - Total draws
- `no_contests` (INTEGER) - Total no contests
- `total_fights` (INTEGER) - Total number of fights
- `ko_tko_wins` (INTEGER) - Wins by KO/TKO
- `submission_wins` (INTEGER) - Wins by submission
- `decision_wins` (INTEGER) - Wins by decision
- `finish_rate` (REAL) - Percentage of fights finished

#### Strike Statistics:
- `total_significant_strikes_landed` (INTEGER)
- `total_significant_strikes_attempted` (INTEGER)
- `total_significant_strikes_absorbed` (INTEGER)
- `striking_accuracy` (REAL) - Strike accuracy percentage
- `strikes_absorbed_per_minute` (REAL)
- `striking_defense_percentage` (REAL)

#### Grappling Statistics:
- `total_takedowns_landed` (INTEGER)
- `total_takedowns_attempted` (INTEGER)
- `takedown_accuracy` (REAL) - Takedown success rate
- `takedowns_average_per_15min` (REAL)
- `total_submission_attempts` (INTEGER)
- `submissions_average_per_15min` (REAL)

#### Other Statistics:
- `total_knockdowns` (INTEGER)
- `total_control_time_seconds` (INTEGER)
- `total_fight_time_seconds` (INTEGER)
- `avg_fight_time_seconds` (REAL)

### 2. **events** (678 records)
Stores UFC event information.

#### Columns:
- `id` (INTEGER, PRIMARY KEY) - Unique event identifier
- `event_name` (TEXT, UNIQUE, NOT NULL) - Official event name
- `url` (TEXT) - Event URL
- `date` (TEXT) - Event date
- `location` (TEXT) - Event location
- `created_at` (TIMESTAMP) - Record creation timestamp

### 3. **fights** (7,386 records)
Stores individual fight records with results and metadata.

#### Columns:
- `id` (INTEGER, PRIMARY KEY) - Unique fight identifier
- `event_id` (INTEGER, FOREIGN KEY → events.id) - Associated event
- `bout` (TEXT, NOT NULL) - Bout description
- `url` (TEXT, UNIQUE) - Fight URL
- `fighter1_id` (INTEGER, FOREIGN KEY → fighters.id) - First fighter
- `fighter2_id` (INTEGER, FOREIGN KEY → fighters.id) - Second fighter
- `winner_id` (INTEGER, FOREIGN KEY → fighters.id) - Winner (NULL for draws/NC)
- `result_method` (TEXT) - How fight ended (KO/TKO, Submission, Decision, etc.)
- `result_round` (INTEGER) - Round fight ended
- `result_time` (TEXT) - Time fight ended
- `weight_class` (TEXT) - Weight class for this fight
- `created_at` (TIMESTAMP) - Record creation timestamp

#### Age Tracking Fields:
- `fighter1_age` (REAL) - Fighter 1's age at fight time
- `fighter1_age_months` (INTEGER) - Fighter 1's age in months
- `fighter1_age_bracket` (TEXT) - Age bracket (e.g., "25-29")
- `fighter1_age_quarter` (REAL) - Age with quarter precision
- `fighter2_age` (REAL) - Fighter 2's age at fight time
- `fighter2_age_months` (INTEGER) - Fighter 2's age in months
- `fighter2_age_bracket` (TEXT) - Age bracket
- `fighter2_age_quarter` (REAL) - Age with quarter precision

### 4. **fight_stats** (31,778 records)
Stores detailed round-by-round statistics for each fighter in each fight.

#### Columns:
- `id` (INTEGER, PRIMARY KEY) - Unique stat record identifier
- `fight_id` (INTEGER, FOREIGN KEY → fights.id) - Associated fight
- `fighter_id` (INTEGER, FOREIGN KEY → fighters.id) - Fighter these stats belong to
- `round_number` (INTEGER) - Round number

#### Striking Statistics:
- `knockdowns` (INTEGER) - Knockdowns in this round
- `sig_strikes_landed` (INTEGER) - Significant strikes landed
- `sig_strikes_attempted` (INTEGER) - Significant strikes attempted
- `sig_strikes_pct` (REAL) - Significant strike accuracy %
- `total_strikes_landed` (INTEGER) - Total strikes landed
- `total_strikes_attempted` (INTEGER) - Total strikes attempted

#### Strike Targets:
- `head_strikes_landed` (INTEGER)
- `head_strikes_attempted` (INTEGER)
- `body_strikes_landed` (INTEGER)
- `body_strikes_attempted` (INTEGER)
- `leg_strikes_landed` (INTEGER)
- `leg_strikes_attempted` (INTEGER)

#### Strike Positions:
- `distance_strikes_landed` (INTEGER)
- `distance_strikes_attempted` (INTEGER)
- `clinch_strikes_landed` (INTEGER)
- `clinch_strikes_attempted` (INTEGER)
- `ground_strikes_landed` (INTEGER)
- `ground_strikes_attempted` (INTEGER)

#### Grappling Statistics:
- `takedowns_landed` (INTEGER)
- `takedowns_attempted` (INTEGER)
- `takedowns_pct` (REAL) - Takedown success %
- `submission_attempts` (INTEGER)
- `reversals` (INTEGER)
- `control_time` (TEXT) - Ground control time (MM:SS format)

### 5. **calculation_parameters** (24 records)
Stores global calculation parameters for the ranking system.

#### Columns:
- `id` (INTEGER, PRIMARY KEY)
- `category` (TEXT, NOT NULL) - Parameter category
- `subcategory` (TEXT) - Parameter subcategory
- `key` (TEXT, NOT NULL) - Parameter key
- `value` (REAL, NOT NULL) - Parameter value
- `description` (TEXT) - Parameter description
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)
- **Unique constraint**: (category, subcategory, key)

#### Categories Include:
- Weight adjustments
- Age adjustments
- Activity bonuses
- Statistical thresholds

### 6. **division_parameters** (36 records)
Stores division-specific parameters for calculations.

#### Columns:
- `id` (INTEGER, PRIMARY KEY)
- `division` (TEXT, NOT NULL) - Weight division name
- `parameter_type` (TEXT, NOT NULL) - Type of parameter
- `key` (TEXT, NOT NULL) - Parameter key
- `value` (REAL, NOT NULL) - Parameter value
- `description` (TEXT) - Parameter description
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)
- **Unique constraint**: (division, parameter_type, key)

#### Parameter Types:
- Finish multipliers
- Division-specific weights
- Activity thresholds

### 7. **dominance_scoring** (19 records)
Defines scoring values for different fight outcomes and dominance levels.

#### Columns:
- `id` (INTEGER, PRIMARY KEY)
- `result_type` (TEXT, NOT NULL) - Type of result (win/loss)
- `method` (TEXT, NOT NULL) - Method of result
- `base_score` (REAL, NOT NULL) - Base score value
- `round_1_bonus` (REAL) - Bonus for R1 finish
- `round_2_bonus` (REAL) - Bonus for R2 finish
- `description` (TEXT) - Scoring description
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)
- **Unique constraint**: (result_type, method)

#### Result Types and Methods:
- Win by KO/TKO, Submission, Decision (various types)
- Loss by same methods
- Draw scenarios
- No Contest

### 8. **statistical_bonuses** (3 records)
Defines bonus calculations based on fighter statistics.

#### Columns:
- `id` (INTEGER, PRIMARY KEY)
- `stat_type` (TEXT, NOT NULL) - Type of statistic
- `threshold` (REAL, NOT NULL) - Threshold value
- `bonus_value` (REAL, NOT NULL) - Bonus amount
- `comparison_operator` (TEXT, NOT NULL) - Operator (>, <, >=, <=, =)
- `description` (TEXT) - Bonus description
- `created_at` (TIMESTAMP)
- `updated_at` (TIMESTAMP)

#### Current Bonuses:
- High significant strike differential
- High takedown average
- High striking accuracy

### 9. **sqlite_sequence** (8 records)
Internal SQLite table tracking auto-increment sequences.

## Data Integrity

### Foreign Key Relationships:
1. **fights** → **events**: Every fight must belong to an event
2. **fights** → **fighters**: fighter1_id, fighter2_id, winner_id must reference valid fighters
3. **fight_stats** → **fights**: Every stat record must belong to a fight
4. **fight_stats** → **fighters**: Every stat record must belong to a fighter

### Data Quality Checks:
- No orphaned records (all foreign keys valid)
- No duplicate fight records
- Winner must be either fighter1 or fighter2
- No self-fights (fighter1 ≠ fighter2)
- 1 duplicate fighter found: Bruno Silva (2 entries)

## Key Features

### Age Tracking System
The database tracks fighter ages at the time of each fight with multiple precision levels:
- Exact age in years (with decimals)
- Age in months
- Age brackets (5-year ranges)
- Quarter-year precision

### Comprehensive Statistics
- Round-by-round fight statistics
- Career aggregates maintained in fighter records
- Detailed striking breakdowns by target and position
- Grappling and control metrics

### Flexible Scoring System
- Parameterized calculations stored in database
- Division-specific adjustments
- Dominance scoring based on fight outcomes
- Statistical bonuses for exceptional performances

## Database Size and Performance
- Total Records: ~50,000+ across all tables
- Primary tables well-indexed with foreign key constraints
- Unique constraints prevent duplicate data
- Timestamp tracking for data versioning