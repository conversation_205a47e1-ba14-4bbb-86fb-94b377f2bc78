/**
 * Finish multipliers for different fight outcomes
 */

export interface FinishMultiplier {
  method: string;
  multiplier: number;
  description: string;
}

export const FINISH_MULTIPLIERS: Record<string, FinishMultiplier> = {
  'KO': {
    method: 'KO',
    multiplier: 1.3,
    description: 'Knockout victory'
  },
  'TKO': {
    method: 'TKO',
    multiplier: 1.25,
    description: 'Technical knockout victory'
  },
  'Submission': {
    method: 'Submission',
    multiplier: 1.2,
    description: 'Submission victory'
  },
  'Decision': {
    method: 'Decision',
    multiplier: 1.0,
    description: 'Decision victory'
  },
  'DQ': {
    method: 'DQ',
    multiplier: 0.8,
    description: 'Disqualification victory'
  },
  'NC': {
    method: 'NC',
    multiplier: 0.0,
    description: 'No contest'
  }
};

/**
 * Get finish multiplier for a given result method
 */
export function getFinishMultiplier(method: string): number {
  if (!method) return 1.0;
  
  // Normalize the method string
  const normalizedMethod = method.toUpperCase().trim();
  
  // Check for exact matches first
  if (FINISH_MULTIPLIERS[normalizedMethod]) {
    return FINISH_MULTIPLIERS[normalizedMethod].multiplier;
  }
  
  // Check for partial matches
  if (normalizedMethod.includes('KO') && !normalizedMethod.includes('TKO')) {
    return FINISH_MULTIPLIERS['KO'].multiplier;
  }
  
  if (normalizedMethod.includes('TKO')) {
    return FINISH_MULTIPLIERS['TKO'].multiplier;
  }
  
  if (normalizedMethod.includes('SUBMISSION') || normalizedMethod.includes('SUB')) {
    return FINISH_MULTIPLIERS['Submission'].multiplier;
  }
  
  if (normalizedMethod.includes('DECISION') || normalizedMethod.includes('DEC')) {
    return FINISH_MULTIPLIERS['Decision'].multiplier;
  }
  
  if (normalizedMethod.includes('DQ') || normalizedMethod.includes('DISQUALIFICATION')) {
    return FINISH_MULTIPLIERS['DQ'].multiplier;
  }
  
  if (normalizedMethod.includes('NC') || normalizedMethod.includes('NO CONTEST')) {
    return FINISH_MULTIPLIERS['NC'].multiplier;
  }
  
  // Default to decision multiplier
  return FINISH_MULTIPLIERS['Decision'].multiplier;
}

/**
 * Get all available finish multipliers
 */
export function getAllFinishMultipliers(): FinishMultiplier[] {
  return Object.values(FINISH_MULTIPLIERS);
}
