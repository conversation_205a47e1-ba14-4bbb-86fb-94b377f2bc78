'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import FighterSearch from '@/components/FighterSearch';

interface Fighter {
  id: number;
  first_name: string;
  last_name: string;
  nickname: string | null;
  total_fights: number;
  weight_class?: string;
  wins?: number;
  losses?: number;
}

export default function FightersPage() {
  const [fighters, setFighters] = useState<Fighter[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [offset, setOffset] = useState(0);
  const limit = 50;

  const fetchFighters = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
      });
      
      if (searchTerm) {
        params.append('search', searchTerm);
      }

      const response = await fetch(`/api/fighters?${params}`);
      const data = await response.json();
      
      if (data.success) {
        setFighters(data.data);
      }
    } catch (error) {
      console.error('Error fetching fighters:', error);
    } finally {
      setLoading(false);
    }
  }, [searchTerm, offset, limit]);

  useEffect(() => {
    fetchFighters();
  }, [fetchFighters]);


  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setOffset(0);
  };

  const handlePrevious = () => {
    setOffset(Math.max(0, offset - limit));
  };

  const handleNext = () => {
    setOffset(offset + limit);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">UFC Fighters</h1>
          <p className="text-gray-600">Browse and search through all UFC fighters</p>
        </div>

        <div className="mb-6">
          <FighterSearch onSearch={handleSearch} />
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="text-xl text-gray-600">Loading fighters...</div>
          </div>
        ) : (
          <>
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {fighters.map((fighter) => (
                  <li key={fighter.id}>
                    <Link 
                      href={`/fighter/whr/${fighter.id}`}
                      className="block hover:bg-gray-50 px-4 py-4 sm:px-6 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="flex-shrink-0">
                            <div className="h-12 w-12 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-lg font-medium text-gray-700">
                                {fighter.first_name[0]}{fighter.last_name[0]}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-lg font-medium text-gray-900">
                              {fighter.first_name} {fighter.last_name}
                            </div>
                            {fighter.nickname && (
                              <div className="text-sm text-gray-500">
                                &quot;{fighter.nickname}&quot;
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center">
                          <div className="text-sm text-gray-600">
                            {fighter.total_fights} fights
                          </div>
                          <svg
                            className="ml-2 h-5 w-5 text-gray-400"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5l7 7-7 7"
                            />
                          </svg>
                        </div>
                      </div>
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div className="mt-6 flex justify-between items-center">
              <button
                onClick={handlePrevious}
                disabled={offset === 0}
                className="px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>
              
              <div className="text-sm text-gray-700">
                Showing {offset + 1} - {offset + fighters.length} fighters
              </div>

              <button
                onClick={handleNext}
                disabled={fighters.length < limit}
                className="px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}