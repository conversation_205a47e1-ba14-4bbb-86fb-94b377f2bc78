import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import Link from "next/link";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "UFC WHR Rankings",
  description: "Advanced UFC fighter ranking system using Weighted Historical Rating (WHR) algorithm",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <nav className="bg-gray-900 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <Link href="/" className="text-xl font-bold">UFC WHR</Link>
                <div className="ml-10 flex items-baseline space-x-4">
                  <Link href="/whr" className="hover:bg-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                    WHR Rankings
                  </Link>
                  <Link href="/whr/predict" className="hover:bg-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                    Fight Predictor
                  </Link>
                  <Link href="/fighter" className="hover:bg-gray-700 px-3 py-2 rounded-md text-sm font-medium">
                    Fighters
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </nav>
        {children}
      </body>
    </html>
  );
}
