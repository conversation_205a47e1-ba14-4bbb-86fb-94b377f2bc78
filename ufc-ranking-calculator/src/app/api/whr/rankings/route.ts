import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/database";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const division = searchParams.get("division");
    const limit = parseInt(searchParams.get("limit") || "50");
    const activeOnly = searchParams.get("activeOnly") === "true";

    let sql = `
      SELECT
        dr.rank,
        dr.fighter_id,
        f.first_name,
        f.last_name,
        f.nickname,
        dr.division,
        dr.rating,
        dr.rating_deviation,
        dr.points,
        dr.streak,
        dr.dominance_score,
        dr.activity_score,
        dr.status,
        dr.confidence,
        wr.fight_count,
        wr.win_count,
        wr.loss_count,
        wr.last_fight_date,
        wr.performance_variance,
        CAST(julianday('now') - julianday(wr.last_fight_date) AS INTEGER) as days_since_last_fight
      FROM whr_division_rankings dr
      JOIN fighters f ON dr.fighter_id = f.id
      JOIN whr_ratings wr ON dr.fighter_id = wr.fighter_id AND dr.division = wr.division
      WHERE dr.ranking_date = (
        SELECT MAX(ranking_date)
        FROM whr_division_rankings dr2
        WHERE dr2.division = dr.division
      )
    `;

    const params: (string | number)[] = [];

    // Always exclude Catch Weight division
    sql += ' AND dr.division != "Catch Weight"';

    // Filter by division if specified
    if (division && division !== "all") {
      sql += " AND dr.division = ?";
      params.push(division);
    }

    // Filter by active status if requested
    if (activeOnly) {
      sql += ' AND dr.status = "active"';
    }

    sql += " ORDER BY dr.division, dr.rank LIMIT ?";
    params.push(limit);

    const rankings = query(sql, params);

    // Get division summaries (excluding Catch Weight)
    const divisionSummaries = query(`
      SELECT
        division,
        COUNT(*) as total_fighters,
        AVG(rating) as avg_rating,
        MAX(rating) as max_rating,
        MIN(rating) as min_rating,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_fighters,
        COUNT(CASE WHEN confidence = 'provisional' THEN 1 END) as provisional_fighters,
        COUNT(CASE WHEN confidence = 'developing' THEN 1 END) as developing_fighters,
        COUNT(CASE WHEN confidence = 'established' THEN 1 END) as established_fighters
      FROM whr_division_rankings
      WHERE ranking_date = (
        SELECT MAX(ranking_date) FROM whr_division_rankings
      ) AND division != 'Catch Weight'
      GROUP BY division
      ORDER BY total_fighters DESC
    `);

    // Get recent rating changes (last 10 fights, excluding Catch Weight)
    const recentChanges = query(`
      SELECT
        fh.fight_id,
        fh.division,
        f1.first_name || ' ' || f1.last_name as fighter1_name,
        f2.first_name || ' ' || f2.last_name as fighter2_name,
        fh.fighter1_pre_rating,
        fh.fighter2_pre_rating,
        fh.rating_change_fighter1,
        fh.rating_change_fighter2,
        fh.expected_outcome,
        fh.actual_outcome,
        fh.surprise_factor,
        e.date as fight_date,
        e.event_name
      FROM whr_fight_history fh
      JOIN fighters f1 ON fh.fighter1_id = f1.id
      JOIN fighters f2 ON fh.fighter2_id = f2.id
      JOIN fights f ON fh.fight_id = f.id
      JOIN events e ON f.event_id = e.id
      WHERE fh.division != 'Catch Weight'
      ORDER BY fh.calculation_timestamp DESC
      LIMIT 10
    `);

    return NextResponse.json({
      success: true,
      data: {
        rankings,
        divisionSummaries,
        recentChanges,
        totalRankings: rankings.length,
        filters: {
          division: division || "all",
          limit,
          activeOnly,
        },
      },
    });
  } catch (error) {
    console.error("WHR Rankings API Error:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch WHR rankings" },
      { status: 500 }
    );
  }
}
