import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/database";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const fighterId = parseInt(id);

    // Get fighter's WHR ratings across all divisions
    const ratings = query(
      `
      SELECT
        wr.division,
        wr.rating,
        wr.rating_deviation,
        wr.fight_count,
        wr.win_count,
        wr.loss_count,
        wr.last_fight_date,
        dr.rank,
        dr.streak,
        dr.dominance_score,
        dr.activity_score,
        dr.status
      FROM whr_ratings wr
      LEFT JOIN whr_division_rankings dr ON
        wr.fighter_id = dr.fighter_id AND
        wr.division = dr.division AND
        dr.ranking_date = (
          SELECT MAX(ranking_date)
          FROM whr_division_rankings dr2
          WHERE dr2.division = dr.division
        )
      WHERE wr.fighter_id = ?
      ORDER BY wr.rating DESC
    `,
      [fighterId]
    );

    // Get fighter's rating history
    const ratingHistory = query(
      `
      SELECT
        fh.fight_id,
        fh.division,
        fh.fighter1_id,
        fh.fighter2_id,
        CASE
          WHEN fh.fighter1_id = ? THEN f2.first_name || ' ' || f2.last_name
          ELSE f1.first_name || ' ' || f1.last_name
        END as opponent_name,
        CASE
          WHEN fh.fighter1_id = ? THEN fh.fighter2_id
          ELSE fh.fighter1_id
        END as opponent_id,
        CASE
          WHEN fh.fighter1_id = ? THEN fh.fighter1_pre_rating
          ELSE fh.fighter2_pre_rating
        END as pre_rating,
        CASE
          WHEN fh.fighter1_id = ? THEN fh.fighter1_post_rating
          ELSE fh.fighter2_post_rating
        END as post_rating,
        CASE
          WHEN fh.fighter1_id = ? THEN fh.rating_change_fighter1
          ELSE fh.rating_change_fighter2
        END as rating_change,
        CASE
          WHEN fh.fighter1_id = ? THEN fh.fighter2_pre_rating
          ELSE fh.fighter1_pre_rating
        END as opponent_rating,
        fh.expected_outcome,
        fh.actual_outcome,
        CASE
          WHEN fh.fighter1_id = ? THEN fh.actual_outcome
          ELSE 1 - fh.actual_outcome
        END as result,
        fh.surprise_factor,
        fh.k_factor,
        e.date as fight_date,
        e.event_name,
        f.result_method,
        f.result_round
      FROM whr_fight_history fh
      JOIN fighters f1 ON fh.fighter1_id = f1.id
      JOIN fighters f2 ON fh.fighter2_id = f2.id
      JOIN fights f ON fh.fight_id = f.id
      JOIN events e ON f.event_id = e.id
      WHERE fh.fighter1_id = ? OR fh.fighter2_id = ?
      ORDER BY e.date DESC
    `,
      [
        fighterId,
        fighterId,
        fighterId,
        fighterId,
        fighterId,
        fighterId,
        fighterId,
        fighterId,
        fighterId,
      ]
    );

    // Calculate performance metrics
    const performanceMetrics = query(
      `
      SELECT
        division,
        COUNT(*) as total_fights,
        SUM(CASE WHEN result > 0.5 THEN 1 ELSE 0 END) as wins_in_division,
        AVG(rating_change) as avg_rating_change,
        MAX(post_rating) as peak_rating,
        MIN(post_rating) as lowest_rating,
        AVG(opponent_rating) as avg_opponent_rating
      FROM (
        SELECT
          fh.division,
          CASE
            WHEN fh.fighter1_id = ? THEN fh.fighter1_post_rating
            ELSE fh.fighter2_post_rating
          END as post_rating,
          CASE
            WHEN fh.fighter1_id = ? THEN fh.rating_change_fighter1
            ELSE fh.rating_change_fighter2
          END as rating_change,
          CASE
            WHEN fh.fighter1_id = ? THEN fh.fighter2_pre_rating
            ELSE fh.fighter1_pre_rating
          END as opponent_rating,
          CASE
            WHEN fh.fighter1_id = ? THEN fh.actual_outcome
            ELSE 1 - fh.actual_outcome
          END as result
        FROM whr_fight_history fh
        WHERE fh.fighter1_id = ? OR fh.fighter2_id = ?
      )
      GROUP BY division
    `,
      [fighterId, fighterId, fighterId, fighterId, fighterId, fighterId]
    );

    // Get fighter basic info
    const fighterInfo = query(
      `
      SELECT
        id,
        first_name,
        last_name,
        nickname,
        birthdate,
        height,
        weight,
        reach,
        stance,
        weight_class
      FROM fighters
      WHERE id = ?
    `,
      [fighterId]
    );

    return NextResponse.json({
      success: true,
      data: {
        fighter: fighterInfo[0] || null,
        ratings,
        ratingHistory,
        performanceMetrics,
        totalDivisions: ratings.length,
      },
    });
  } catch (error) {
    console.error("WHR Fighter API Error:", error);
    return NextResponse.json(
      { success: false, error: "Failed to fetch fighter WHR data" },
      { status: 500 }
    );
  }
}
