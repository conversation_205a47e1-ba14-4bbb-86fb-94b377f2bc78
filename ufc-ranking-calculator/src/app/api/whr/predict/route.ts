import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/database";

// Type definitions
interface WHRRating {
  fighter_id: number;
  rating: number;
  rating_deviation: number;
  last_fight_date: string;
  fight_count: number;
  win_count: number;
  loss_count: number;
}

interface DivisionParameters {
  division: string;
  rating_scale_divisor: number;
  [key: string]: unknown;
}

interface Fighter {
  id: number;
  name: string;
}

interface LastFight {
  fighter_num: number;
  result_method: string;
  winner_id: number;
  date: string;
}

interface EmpiricalParams {
  ring_rust?: {
    threshold: number;
    penalty: number;
  };
  recovery_penalties?: {
    ANY_FINISH: Array<{
      maxDays: number;
      penalty: number;
    }>;
  };
}

// Data-driven parameters - loaded from empirical analysis
let EMPIRICAL_PARAMS: EmpiricalParams | null = null;

// Load empirical parameters from database
async function loadEmpiricalParameters(): Promise<EmpiricalParams> {
  if (EMPIRICAL_PARAMS) return EMPIRICAL_PARAMS;

  try {
    const results = query(`
      SELECT parameter_type, parameter_data
      FROM empirical_parameters
    `) as Array<{ parameter_type: string; parameter_data: string }>;

    const params: Record<string, unknown> = {};
    results.forEach((row) => {
      params[row.parameter_type] = JSON.parse(row.parameter_data);
    });

    EMPIRICAL_PARAMS = params as EmpiricalParams;
    return EMPIRICAL_PARAMS;
  } catch (error) {
    console.error("Failed to load empirical parameters:", error);
    // Fallback to research-based values from recovery-based-prediction-model.md
    const fallbackParams: EmpiricalParams = {
      ring_rust: { threshold: 366, penalty: 0.057 },
      recovery_penalties: {
        ANY_FINISH: [
          { maxDays: 90, penalty: 0.056 },
          { maxDays: 180, penalty: 0.0 },
          { maxDays: 365, penalty: 0.042 },
          { maxDays: Infinity, penalty: 0.096 },
        ],
      },
    };
    EMPIRICAL_PARAMS = fallbackParams;
    return fallbackParams;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { fighter1Id, fighter2Id, division, fightDate } = body;

    if (!fighter1Id || !fighter2Id || !division) {
      return NextResponse.json(
        { success: false, error: "Missing required parameters" },
        { status: 400 }
      );
    }

    // Load empirical parameters
    const empiricalParams = await loadEmpiricalParameters();

    // Get current WHR ratings for both fighters
    const ratings = query(
      `
      SELECT
        fighter_id,
        rating,
        rating_deviation,
        last_fight_date,
        fight_count,
        win_count,
        loss_count
      FROM whr_ratings
      WHERE fighter_id IN (?, ?) AND division = ?
    `,
      [fighter1Id, fighter2Id, division]
    ) as WHRRating[];

    if (ratings.length !== 2) {
      return NextResponse.json(
        {
          success: false,
          error: "One or both fighters not found in this division",
        },
        { status: 404 }
      );
    }

    const fighter1Rating = ratings.find((r) => r.fighter_id === fighter1Id);
    const fighter2Rating = ratings.find((r) => r.fighter_id === fighter2Id);

    if (!fighter1Rating || !fighter2Rating) {
      return NextResponse.json(
        { success: false, error: "Fighter ratings not found" },
        { status: 404 }
      );
    }

    // Get division parameters
    const divisionParamsResult = query(
      `
      SELECT * FROM division_parameters WHERE division = ?
    `,
      [division]
    ) as DivisionParameters[];

    const divisionParams = divisionParamsResult[0];

    if (!divisionParams) {
      return NextResponse.json(
        { success: false, error: "Division parameters not found" },
        { status: 404 }
      );
    }

    // Calculate base win probability using division-specific scale
    const ratingDiff = fighter1Rating.rating - fighter2Rating.rating;
    const baseWinProbability =
      1 / (1 + Math.pow(10, -ratingDiff / divisionParams.rating_scale_divisor));

    // Apply ring rust adjustments if fight date provided
    let adjustedWinProbability = baseWinProbability;
    const ringRustAdjustments = { fighter1: 0, fighter2: 0 };
    const recoveryAdjustments = { fighter1: 0, fighter2: 0 };

    if (fightDate) {
      const fightDateObj = new Date(fightDate);

      // Calculate ring rust for both fighters
      const fighter1DaysSinceLastFight = Math.floor(
        (fightDateObj.getTime() -
          new Date(fighter1Rating.last_fight_date).getTime()) /
          (1000 * 60 * 60 * 24)
      );
      const fighter2DaysSinceLastFight = Math.floor(
        (fightDateObj.getTime() -
          new Date(fighter2Rating.last_fight_date).getTime()) /
          (1000 * 60 * 60 * 24)
      );

      // Apply empirical ring rust penalties
      const ringRustThreshold = empiricalParams.ring_rust?.threshold || 366;
      const ringRustPenalty = empiricalParams.ring_rust?.penalty || 0.057;

      if (fighter1DaysSinceLastFight >= ringRustThreshold) {
        ringRustAdjustments.fighter1 = -ringRustPenalty;
      }
      if (fighter2DaysSinceLastFight >= ringRustThreshold) {
        ringRustAdjustments.fighter2 = -ringRustPenalty;
      }

      // Check for recovery penalties (requires fight history)
      const lastFights = query(
        `
        SELECT
          CASE
            WHEN f.fighter1_id = ? THEN 1
            WHEN f.fighter2_id = ? THEN 1
            WHEN f.fighter1_id = ? THEN 2
            WHEN f.fighter2_id = ? THEN 2
          END as fighter_num,
          f.result_method,
          f.winner_id,
          e.date
        FROM fights f
        JOIN events e ON f.event_id = e.id
        WHERE (f.fighter1_id IN (?, ?) OR f.fighter2_id IN (?, ?))
          AND e.date = (
            SELECT MAX(e2.date)
            FROM fights f2
            JOIN events e2 ON f2.event_id = e2.id
            WHERE (
              (fighter_num = 1 AND (f2.fighter1_id = ? OR f2.fighter2_id = ?)) OR
              (fighter_num = 2 AND (f2.fighter1_id = ? OR f2.fighter2_id = ?))
            )
          )
      `,
        [
          fighter1Id,
          fighter1Id,
          fighter2Id,
          fighter2Id,
          fighter1Id,
          fighter2Id,
          fighter1Id,
          fighter2Id,
          fighter1Id,
          fighter1Id,
          fighter2Id,
          fighter2Id,
        ]
      ) as LastFight[];

      // Apply recovery penalties if fighter was finished in last fight
      for (const lastFight of lastFights) {
        const fighterNum = lastFight.fighter_num;
        const fighterId = fighterNum === 1 ? fighter1Id : fighter2Id;
        const wasFinished =
          lastFight.winner_id !== fighterId &&
          (lastFight.result_method.includes("KO") ||
            lastFight.result_method.includes("TKO") ||
            lastFight.result_method.includes("Submission"));

        if (wasFinished) {
          const daysSinceFinish = Math.floor(
            (fightDateObj.getTime() - new Date(lastFight.date).getTime()) /
              (1000 * 60 * 60 * 24)
          );

          // Use empirical recovery penalties (ALL FINISHES COMBINED - best sample sizes)
          const recoveryPenalties = empiricalParams.recovery_penalties
            ?.ANY_FINISH || [
            { maxDays: 90, penalty: 0.056 },
            { maxDays: 180, penalty: 0.0 },
            { maxDays: 365, penalty: 0.042 },
            { maxDays: Infinity, penalty: 0.096 },
          ];

          for (const { maxDays, penalty } of recoveryPenalties) {
            if (daysSinceFinish <= maxDays) {
              if (fighterNum === 1) {
                recoveryAdjustments.fighter1 = -penalty;
              } else {
                recoveryAdjustments.fighter2 = -penalty;
              }
              break;
            }
          }
        }
      }

      // Apply all adjustments
      const totalFighter1Adjustment =
        ringRustAdjustments.fighter1 + recoveryAdjustments.fighter1;
      const totalFighter2Adjustment =
        ringRustAdjustments.fighter2 + recoveryAdjustments.fighter2;
      const netAdjustment = totalFighter1Adjustment - totalFighter2Adjustment;

      adjustedWinProbability = baseWinProbability + netAdjustment;
      adjustedWinProbability = Math.max(0, Math.min(1, adjustedWinProbability)); // Clamp to [0, 1]
    }

    // Get fighter names
    const fighters = query(
      `
      SELECT id, first_name || ' ' || last_name as name
      FROM fighters
      WHERE id IN (?, ?)
    `,
      [fighter1Id, fighter2Id]
    ) as Fighter[];

    const fighter1Name =
      fighters.find((f) => f.id === fighter1Id)?.name || "Unknown";
    const fighter2Name =
      fighters.find((f) => f.id === fighter2Id)?.name || "Unknown";

    // Get historical matchup data if any
    const historicalMatchups = query(
      `
      SELECT
        f.id as fight_id,
        f.winner_id,
        f.result_method,
        f.result_round,
        e.date,
        e.event_name
      FROM fights f
      JOIN events e ON f.event_id = e.id
      WHERE ((f.fighter1_id = ? AND f.fighter2_id = ?) OR
             (f.fighter1_id = ? AND f.fighter2_id = ?))
        AND f.weight_class = ?
      ORDER BY e.date DESC
    `,
      [fighter1Id, fighter2Id, fighter2Id, fighter1Id, division]
    );

    return NextResponse.json({
      success: true,
      data: {
        prediction: {
          fighter1: {
            id: fighter1Id,
            name: fighter1Name,
            rating: fighter1Rating.rating,
            deviation: fighter1Rating.rating_deviation,
            record: `${fighter1Rating.win_count}-${fighter1Rating.loss_count}`,
            baseWinProbability: baseWinProbability,
            adjustedWinProbability: adjustedWinProbability,
            ringRustAdjustment: ringRustAdjustments.fighter1,
            recoveryAdjustment: recoveryAdjustments.fighter1,
          },
          fighter2: {
            id: fighter2Id,
            name: fighter2Name,
            rating: fighter2Rating.rating,
            deviation: fighter2Rating.rating_deviation,
            record: `${fighter2Rating.win_count}-${fighter2Rating.loss_count}`,
            baseWinProbability: 1 - baseWinProbability,
            adjustedWinProbability: 1 - adjustedWinProbability,
            ringRustAdjustment: ringRustAdjustments.fighter2,
            recoveryAdjustment: recoveryAdjustments.fighter2,
          },
          division,
          ratingDifference: Math.abs(ratingDiff),
          confidence: calculateConfidence(fighter1Rating, fighter2Rating),
          historicalMatchups,
        },
      },
    });
  } catch (error) {
    console.error("WHR Prediction API Error:", error);
    return NextResponse.json(
      { success: false, error: "Failed to generate prediction" },
      { status: 500 }
    );
  }
}

function calculateConfidence(rating1: WHRRating, rating2: WHRRating): string {
  // Confidence based on rating deviations and fight counts
  const avgDeviation =
    (rating1.rating_deviation + rating2.rating_deviation) / 2;
  const minFights = Math.min(rating1.fight_count, rating2.fight_count);

  if (minFights < 3 || avgDeviation > 100) return "Low";
  if (minFights < 5 || avgDeviation > 75) return "Medium";
  if (minFights < 10 || avgDeviation > 50) return "High";
  return "Very High";
}
