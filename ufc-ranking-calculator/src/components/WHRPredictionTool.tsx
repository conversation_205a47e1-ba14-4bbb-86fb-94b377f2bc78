"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

import { Label } from "@/components/ui/label";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import {
  CalendarIcon,
  Swords,
  TrendingUp,
  AlertCircle,
  Clock,
} from "lucide-react";
import FighterSearch from "./FighterSearch";
import { divisions } from "@/config/divisions";

interface Fighter {
  id: number;
  name: string;
}

interface PredictionResult {
  fighter1: {
    id: number;
    name: string;
    rating: number;
    deviation: number;
    record: string;
    baseWinProbability: number;
    adjustedWinProbability: number;
    ringRustAdjustment: number;
    recoveryAdjustment: number;
  };
  fighter2: {
    id: number;
    name: string;
    rating: number;
    deviation: number;
    record: string;
    baseWinProbability: number;
    adjustedWinProbability: number;
    ringRustAdjustment: number;
    recoveryAdjustment: number;
  };
  division: string;
  ratingDifference: number;
  confidence: string;
  historicalMatchups: Array<{
    event_name: string;
    date: string;
    winner_id: number;
    result_method: string;
  }>;
}

export default function WHRPredictionTool() {
  const [fighter1, setFighter1] = useState<Fighter | null>(null);
  const [fighter2, setFighter2] = useState<Fighter | null>(null);
  const [division, setDivision] = useState<string>("");
  const [fightDate, setFightDate] = useState<Date | undefined>(new Date());
  const [prediction, setPrediction] = useState<PredictionResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handlePredict = async () => {
    if (!fighter1 || !fighter2 || !division) {
      setError("Please select both fighters and a division");
      return;
    }

    setLoading(true);
    setError(null);
    setPrediction(null);

    try {
      const response = await fetch("/api/whr/predict", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          fighter1Id: fighter1.id,
          fighter2Id: fighter2.id,
          division,
          fightDate: fightDate?.toISOString(),
        }),
      });

      const data = await response.json();

      if (data.success) {
        setPrediction(data.data.prediction);
      } else {
        setError(data.error || "Failed to generate prediction");
      }
    } catch {
      setError("Failed to generate prediction");
    } finally {
      setLoading(false);
    }
  };

  const getConfidenceColor = (confidence: string) => {
    switch (confidence) {
      case "Very High":
        return "text-green-600";
      case "High":
        return "text-blue-600";
      case "Medium":
        return "text-yellow-600";
      case "Low":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const getProbabilityBar = (probability: number) => {
    return (
      <div className="relative w-full h-8 bg-muted rounded-lg overflow-hidden">
        <div
          className="absolute left-0 top-0 h-full bg-primary transition-all"
          style={{ width: `${probability * 100}%` }}
        />
        <div className="absolute inset-0 flex items-center justify-center text-sm font-medium">
          {(probability * 100).toFixed(1)}%
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Swords className="h-5 w-5" />
            WHR Fight Prediction
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Fighter Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label>Fighter 1</Label>
              <FighterSearch
                onSelect={(f) =>
                  setFighter1({
                    id: f.id,
                    name: `${f.first_name} ${f.last_name}`,
                  })
                }
                placeholder="Search for fighter 1..."
              />
              {fighter1 && (
                <div className="text-sm text-muted-foreground">
                  Selected: {fighter1.name}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label>Fighter 2</Label>
              <FighterSearch
                onSelect={(f) =>
                  setFighter2({
                    id: f.id,
                    name: `${f.first_name} ${f.last_name}`,
                  })
                }
                placeholder="Search for fighter 2..."
              />
              {fighter2 && (
                <div className="text-sm text-muted-foreground">
                  Selected: {fighter2.name}
                </div>
              )}
            </div>
          </div>

          {/* Division Selection */}
          <div className="space-y-2">
            <Label>Division</Label>
            <select
              className="w-full p-2 border rounded-lg"
              value={division}
              onChange={(e) => setDivision(e.target.value)}
            >
              <option value="">Select division...</option>
              {divisions.map((div) => (
                <option key={div.value} value={div.value}>
                  {div.label}
                </option>
              ))}
            </select>
          </div>

          {/* Fight Date */}
          <div className="space-y-2">
            <Label>Fight Date (Optional)</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !fightDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {fightDate ? (
                    format(fightDate, "PPP")
                  ) : (
                    <span>Pick a date</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={fightDate}
                  onSelect={setFightDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            <p className="text-xs text-muted-foreground">
              Date is used to calculate ring rust and recovery penalties
            </p>
          </div>

          {error && (
            <div className="flex items-center gap-2 text-red-600 text-sm">
              <AlertCircle className="h-4 w-4" />
              {error}
            </div>
          )}

          <Button
            onClick={handlePredict}
            disabled={loading || !fighter1 || !fighter2 || !division}
            className="w-full"
          >
            {loading ? "Generating Prediction..." : "Generate Prediction"}
          </Button>
        </CardContent>
      </Card>

      {/* Prediction Results */}
      {prediction && (
        <Card>
          <CardHeader>
            <CardTitle>Prediction Results</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Fighter Comparison */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="text-lg font-semibold">
                  {prediction.fighter1.name}
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Record</span>
                    <span className="font-mono">
                      {prediction.fighter1.record}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">WHR Rating</span>
                    <span className="font-mono">
                      {prediction.fighter1.rating.toFixed(0)} ±
                      {prediction.fighter1.deviation.toFixed(0)}
                    </span>
                  </div>
                  {prediction.fighter1.ringRustAdjustment !== 0 && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Ring Rust</span>
                      <span className="font-mono text-red-600">
                        {(prediction.fighter1.ringRustAdjustment * 100).toFixed(
                          0
                        )}
                        %
                      </span>
                    </div>
                  )}
                  {prediction.fighter1.recoveryAdjustment !== 0 && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Recovery Penalty
                      </span>
                      <span className="font-mono text-red-600">
                        {(prediction.fighter1.recoveryAdjustment * 100).toFixed(
                          0
                        )}
                        %
                      </span>
                    </div>
                  )}
                </div>
              </div>

              <div className="space-y-3">
                <div className="text-lg font-semibold">
                  {prediction.fighter2.name}
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Record</span>
                    <span className="font-mono">
                      {prediction.fighter2.record}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">WHR Rating</span>
                    <span className="font-mono">
                      {prediction.fighter2.rating.toFixed(0)} ±
                      {prediction.fighter2.deviation.toFixed(0)}
                    </span>
                  </div>
                  {prediction.fighter2.ringRustAdjustment !== 0 && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Ring Rust</span>
                      <span className="font-mono text-red-600">
                        {(prediction.fighter2.ringRustAdjustment * 100).toFixed(
                          0
                        )}
                        %
                      </span>
                    </div>
                  )}
                  {prediction.fighter2.recoveryAdjustment !== 0 && (
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">
                        Recovery Penalty
                      </span>
                      <span className="font-mono text-red-600">
                        {(prediction.fighter2.recoveryAdjustment * 100).toFixed(
                          0
                        )}
                        %
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Win Probability */}
            <div className="space-y-4">
              <div>
                <div className="flex justify-between mb-2">
                  <span className="font-medium">
                    {prediction.fighter1.name}
                  </span>
                  <span className="font-medium">
                    {prediction.fighter2.name}
                  </span>
                </div>
                {getProbabilityBar(prediction.fighter1.adjustedWinProbability)}
              </div>

              {/* Base vs Adjusted if different */}
              {prediction.fighter1.baseWinProbability !==
                prediction.fighter1.adjustedWinProbability && (
                <div className="text-sm text-muted-foreground">
                  <div className="flex justify-between">
                    <span>Base Probability:</span>
                    <span>
                      {(prediction.fighter1.baseWinProbability * 100).toFixed(
                        1
                      )}
                      % -{" "}
                      {(prediction.fighter2.baseWinProbability * 100).toFixed(
                        1
                      )}
                      %
                    </span>
                  </div>
                  <div className="flex items-center gap-1 mt-1">
                    <Clock className="h-3 w-3" />
                    <span>Adjusted for ring rust and recovery</span>
                  </div>
                </div>
              )}
            </div>

            {/* Prediction Confidence */}
            <div className="flex items-center justify-between p-4 bg-muted rounded-lg">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                <span>Prediction Confidence:</span>
              </div>
              <span
                className={`font-semibold ${getConfidenceColor(
                  prediction.confidence
                )}`}
              >
                {prediction.confidence}
              </span>
            </div>

            {/* Historical Matchups */}
            {prediction.historicalMatchups.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Previous Encounters</h4>
                {prediction.historicalMatchups.map((match, idx) => (
                  <div key={idx} className="text-sm p-3 bg-muted rounded-lg">
                    <div className="flex justify-between">
                      <span>{match.event_name}</span>
                      <span>{format(new Date(match.date), "MMM d, yyyy")}</span>
                    </div>
                    <div className="mt-1">
                      Winner:{" "}
                      {match.winner_id === prediction.fighter1.id
                        ? prediction.fighter1.name
                        : prediction.fighter2.name}{" "}
                      ({match.result_method})
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
