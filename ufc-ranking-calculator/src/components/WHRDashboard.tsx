"use client";

import { useState, useEffect, use<PERSON>em<PERSON>, use<PERSON><PERSON>back } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  TrendingUp,
  TrendingDown,
  Minus,
  Trophy,
  Activity,
  Users,
  BarChart3,
  Clock,
  Target,
} from "lucide-react";
import { format } from "date-fns";
import { divisions } from "@/config/divisions";

interface WHRRanking {
  rank: number;
  fighter_id: number;
  first_name: string;
  last_name: string;
  nickname?: string;
  division: string;
  rating: number;
  rating_deviation: number;
  points: number;
  streak: number;
  dominance_score: number;
  activity_score: number;
  status: "active" | "inactive";
  fight_count: number;
  win_count: number;
  loss_count: number;
  last_fight_date: string;
  days_since_last_fight: number;
}

interface DivisionSummary {
  division: string;
  total_fighters: number;
  avg_rating: number;
  max_rating: number;
  min_rating: number;
  active_fighters: number;
}

interface RecentChange {
  fight_id: number;
  division: string;
  fighter1_name: string;
  fighter2_name: string;
  fighter1_pre_rating: number;
  fighter2_pre_rating: number;
  rating_change_fighter1: number;
  rating_change_fighter2: number;
  expected_outcome: number;
  actual_outcome: number;
  surprise_factor: number;
  fight_date: string;
  event_name: string;
}

export default function WHRDashboard() {
  const [selectedDivision, setSelectedDivision] = useState<string>("all");
  const [rankings, setRankings] = useState<WHRRanking[]>([]);
  const [divisionSummaries, setDivisionSummaries] = useState<DivisionSummary[]>(
    []
  );
  const [recentChanges, setRecentChanges] = useState<RecentChange[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeOnly, setActiveOnly] = useState(true);

  const fetchRankings = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        division: selectedDivision,
        activeOnly: activeOnly.toString(),
        limit: "50",
      });

      const response = await fetch(`/api/whr/rankings?${params}`);
      const data = await response.json();

      if (data.success) {
        setRankings(data.data.rankings);
        setDivisionSummaries(data.data.divisionSummaries);
        setRecentChanges(data.data.recentChanges);
      }
    } catch (error) {
      console.error("Failed to fetch WHR rankings:", error);
    } finally {
      setLoading(false);
    }
  }, [selectedDivision, activeOnly]);

  useEffect(() => {
    fetchRankings();
  }, [selectedDivision, activeOnly, fetchRankings]);

  // Group rankings by division for 'all' view
  const groupedRankings = useMemo(() => {
    if (selectedDivision !== "all") return { [selectedDivision]: rankings };

    return rankings.reduce((acc, ranking) => {
      if (!acc[ranking.division]) acc[ranking.division] = [];
      acc[ranking.division].push(ranking);
      return acc;
    }, {} as Record<string, WHRRanking[]>);
  }, [rankings, selectedDivision]);

  const getRatingColor = (rating: number) => {
    if (rating >= 1700) return "text-purple-600 dark:text-purple-400";
    if (rating >= 1600) return "text-blue-600 dark:text-blue-400";
    if (rating >= 1500) return "text-green-600 dark:text-green-400";
    if (rating >= 1400) return "text-yellow-600 dark:text-yellow-400";
    return "text-gray-600 dark:text-gray-400";
  };

  const getStreakIcon = (streak: number) => {
    if (streak > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (streak < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Minus className="h-4 w-4 text-gray-400" />;
  };

  const getActivityBadge = (daysSinceLastFight: number) => {
    if (daysSinceLastFight < 180)
      return <Badge variant="success">Active</Badge>;
    if (daysSinceLastFight < 365)
      return <Badge variant="warning">Semi-Active</Badge>;
    return <Badge variant="secondary">Inactive</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-2">
          <Trophy className="h-6 w-6 text-yellow-500" />
          <h1 className="text-2xl font-bold">WHR Rankings</h1>
        </div>

        <div className="flex gap-2">
          <Select value={selectedDivision} onValueChange={setSelectedDivision}>
            <SelectTrigger className="w-[200px]">
              <SelectValue placeholder="Select division" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Divisions</SelectItem>
              {divisions.map((div) => (
                <SelectItem key={div.value} value={div.value}>
                  {div.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant={activeOnly ? "default" : "outline"}
            onClick={() => setActiveOnly(!activeOnly)}
            size="sm"
          >
            <Activity className="h-4 w-4 mr-1" />
            Active Only
          </Button>
        </div>
      </div>

      {/* Division Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {divisionSummaries.slice(0, 4).map((summary) => (
          <Card key={summary.division}>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">
                {summary.division}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{summary.total_fighters}</div>
              <p className="text-xs text-muted-foreground">
                {summary.active_fighters} active • Avg:{" "}
                {summary.avg_rating.toFixed(0)}
              </p>
              <div className="mt-2 flex items-center gap-2">
                <Badge variant="outline" className="text-xs">
                  {summary.min_rating.toFixed(0)} -{" "}
                  {summary.max_rating.toFixed(0)}
                </Badge>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Tabs defaultValue="rankings" className="space-y-4">
        <TabsList>
          <TabsTrigger value="rankings">Rankings</TabsTrigger>
          <TabsTrigger value="recent">Recent Changes</TabsTrigger>
          <TabsTrigger value="stats">Statistics</TabsTrigger>
        </TabsList>

        {/* Rankings Tab */}
        <TabsContent value="rankings" className="space-y-4">
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            Object.entries(groupedRankings).map(([division, divRankings]) => (
              <Card key={division}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{division}</span>
                    <Badge variant="secondary">
                      {divRankings.length} fighters
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {divRankings.slice(0, 10).map((fighter) => (
                      <div
                        key={`${fighter.division}-${fighter.fighter_id}`}
                        className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <div className="text-lg font-bold text-muted-foreground w-8">
                            #{fighter.rank}
                          </div>
                          <div>
                            <div className="font-medium">
                              {fighter.first_name} {fighter.last_name}
                              {fighter.nickname && (
                                <span className="text-muted-foreground ml-1">
                                  &quot;{fighter.nickname}&quot;
                                </span>
                              )}
                            </div>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span>
                                {fighter.win_count}-{fighter.loss_count}
                              </span>
                              <span>•</span>
                              <span className="flex items-center gap-1">
                                {getStreakIcon(fighter.streak)}
                                {Math.abs(fighter.streak)}
                              </span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          <div className="text-right">
                            <div
                              className={`text-lg font-bold ${getRatingColor(
                                fighter.rating
                              )}`}
                            >
                              {fighter.rating.toFixed(0)}
                            </div>
                            <div className="text-xs text-muted-foreground">
                              ±{fighter.rating_deviation.toFixed(0)}
                            </div>
                          </div>
                          {getActivityBadge(fighter.days_since_last_fight)}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </TabsContent>

        {/* Recent Changes Tab */}
        <TabsContent value="recent" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Rating Changes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentChanges.map((change, idx) => (
                  <div
                    key={idx}
                    className="border-b last:border-0 pb-3 last:pb-0"
                  >
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <div className="font-medium">
                          {change.fighter1_name} vs {change.fighter2_name}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {change.event_name} •{" "}
                          {format(new Date(change.fight_date), "MMM d, yyyy")}
                        </div>
                      </div>
                      <Badge variant="outline">{change.division}</Badge>
                    </div>

                    <div className="mt-2 grid grid-cols-2 gap-2 text-sm">
                      <div className="flex items-center justify-between">
                        <span
                          className={
                            change.actual_outcome > 0.5 ? "font-medium" : ""
                          }
                        >
                          {change.fighter1_name}
                        </span>
                        <span
                          className={`font-mono ${
                            change.rating_change_fighter1 > 0
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {change.rating_change_fighter1 > 0 ? "+" : ""}
                          {change.rating_change_fighter1.toFixed(1)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span
                          className={
                            change.actual_outcome < 0.5 ? "font-medium" : ""
                          }
                        >
                          {change.fighter2_name}
                        </span>
                        <span
                          className={`font-mono ${
                            change.rating_change_fighter2 > 0
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {change.rating_change_fighter2 > 0 ? "+" : ""}
                          {change.rating_change_fighter2.toFixed(1)}
                        </span>
                      </div>
                    </div>

                    {change.surprise_factor > 0.3 && (
                      <Badge variant="warning" className="mt-2 text-xs">
                        Upset! {(change.surprise_factor * 100).toFixed(0)}%
                        surprise
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Statistics Tab */}
        <TabsContent value="stats" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Division Overview
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {divisionSummaries.map((summary) => (
                    <div
                      key={summary.division}
                      className="flex items-center justify-between py-2"
                    >
                      <span className="font-medium">{summary.division}</span>
                      <div className="flex items-center gap-2">
                        <Badge variant="secondary">
                          <Users className="h-3 w-3 mr-1" />
                          {summary.total_fighters}
                        </Badge>
                        <Badge variant="outline">
                          <Target className="h-3 w-3 mr-1" />
                          {summary.avg_rating.toFixed(0)}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  System Health
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span>Total Fighters</span>
                    <span className="font-mono">
                      {divisionSummaries.reduce(
                        (sum, d) => sum + d.total_fighters,
                        0
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Active Fighters</span>
                    <span className="font-mono">
                      {divisionSummaries.reduce(
                        (sum, d) => sum + d.active_fighters,
                        0
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Avg Rating Spread</span>
                    <span className="font-mono">
                      {Math.round(
                        divisionSummaries.reduce(
                          (sum, d) => sum + (d.max_rating - d.min_rating),
                          0
                        ) / divisionSummaries.length
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Recent Updates</span>
                    <span className="font-mono">{recentChanges.length}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
