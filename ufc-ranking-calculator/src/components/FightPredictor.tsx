'use client';

import React, { useState } from 'react';
import FighterSearch from './FighterSearch';

interface Fighter {
  id: number;
  name: string;
  rating: number;
  rating_deviation: number;
  fight_count: number;
  win_count: number;
  loss_count: number;
  weight_class: string;
  active_status: string;
  win_rate: number;
}

interface Prediction {
  fighter1_win_probability: number;
  fighter2_win_probability: number;
  confidence: number;
  rating_difference: number;
  likely_method: string;
  method_probabilities: {
    ko_tko: number;
    submission: number;
    decision: number;
  };
}

interface PreviousFight {
  id: number;
  date: string;
  result_method: string;
  result_round: number;
  winner: string;
}

interface SearchFighter {
  id: number;
  first_name: string;
  last_name: string;
  nickname: string | null;
  total_fights: number;
  wins?: number;
  losses?: number;
  rating?: number;
  rating_deviation?: number;
  weight_class?: string;
}

export default function FightPredictor() {
  const [fighter1, setFighter1] = useState<Fighter | null>(null);
  const [fighter2, setFighter2] = useState<Fighter | null>(null);
  const [prediction, setPrediction] = useState<Prediction | null>(null);
  const [previousFights, setPreviousFights] = useState<PreviousFight[]>([]);
  const [commonOpponents, setCommonOpponents] = useState<number>(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handlePrediction = async () => {
    if (!fighter1 || !fighter2) {
      setError('Please select both fighters');
      return;
    }

    if (fighter1.id === fighter2.id) {
      setError('Please select different fighters');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/whr/predict', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fighter1Id: fighter1.id,
          fighter2Id: fighter2.id,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setPrediction(data.data.prediction);
        setFighter1(data.data.fighter1);
        setFighter2(data.data.fighter2);
        setPreviousFights(data.data.history.previous_fights);
        setCommonOpponents(data.data.history.common_opponents);
      } else {
        setError(data.error || 'Failed to generate prediction');
      }
    } catch (err) {
      setError('Network error: ' + (err as Error).message);
    } finally {
      setLoading(false);
    }
  };

  const handleFighter1Select = (fighter: SearchFighter) => {
    setFighter1({
      id: fighter.id,
      name: `${fighter.first_name} ${fighter.last_name}`,
      rating: fighter.rating || 1500,
      rating_deviation: fighter.rating_deviation || 100,
      fight_count: fighter.total_fights || 0,
      win_count: fighter.wins || 0,
      loss_count: fighter.losses || 0,
      weight_class: fighter.weight_class || 'Unknown',
      active_status: 'Active',
      win_rate: 0
    });
    setPrediction(null);
  };

  const handleFighter2Select = (fighter: SearchFighter) => {
    setFighter2({
      id: fighter.id,
      name: `${fighter.first_name} ${fighter.last_name}`,
      rating: fighter.rating || 1500,
      rating_deviation: fighter.rating_deviation || 100,
      fight_count: fighter.total_fights || 0,
      win_count: fighter.wins || 0,
      loss_count: fighter.losses || 0,
      weight_class: fighter.weight_class || 'Unknown',
      active_status: 'Active',
      win_rate: 0
    });
    setPrediction(null);
  };

  const getFavorite = () => {
    if (!prediction) return null;
    if (prediction.fighter1_win_probability > prediction.fighter2_win_probability) {
      return { fighter: fighter1, probability: prediction.fighter1_win_probability };
    }
    return { fighter: fighter2, probability: prediction.fighter2_win_probability };
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h2 className="text-3xl font-bold mb-6">Fight Predictor</h2>

      <div className="grid md:grid-cols-2 gap-6 mb-6">
        {/* Fighter 1 Selection */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-xl font-semibold mb-4">Fighter 1</h3>
          <FighterSearch onSelect={handleFighter1Select} />
          
          {fighter1 && (
            <div className="mt-4 p-4 bg-gray-50 rounded">
              <div className="font-semibold text-lg">{fighter1.name}</div>
              <div className="text-sm text-gray-600">
                {fighter1.weight_class} • {fighter1.win_count}-{fighter1.loss_count}
              </div>
              <div className="text-sm mt-2">
                WHR Rating: <span className="font-semibold">{fighter1.rating}</span>
              </div>
            </div>
          )}
        </div>

        {/* Fighter 2 Selection */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-xl font-semibold mb-4">Fighter 2</h3>
          <FighterSearch onSelect={handleFighter2Select} />
          
          {fighter2 && (
            <div className="mt-4 p-4 bg-gray-50 rounded">
              <div className="font-semibold text-lg">{fighter2.name}</div>
              <div className="text-sm text-gray-600">
                {fighter2.weight_class} • {fighter2.win_count}-{fighter2.loss_count}
              </div>
              <div className="text-sm mt-2">
                WHR Rating: <span className="font-semibold">{fighter2.rating}</span>
              </div>
            </div>
          )}
        </div>
      </div>

      <div className="text-center mb-6">
        <button
          onClick={handlePrediction}
          disabled={!fighter1 || !fighter2 || loading}
          className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
        >
          {loading ? 'Generating Prediction...' : 'Generate Prediction'}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {prediction && fighter1 && fighter2 && (
        <>
          {/* Prediction Results */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
            <h3 className="text-2xl font-bold mb-4">Prediction</h3>
            
            <div className="grid md:grid-cols-3 gap-4 mb-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {prediction.fighter1_win_probability}%
                </div>
                <div className="text-sm text-gray-600">{fighter1.name}</div>
              </div>
              
              <div className="text-center flex items-center justify-center">
                <div>
                  <div className="text-sm text-gray-500">VS</div>
                  <div className="text-sm text-gray-500 mt-1">
                    Confidence: {prediction.confidence}%
                  </div>
                </div>
              </div>
              
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">
                  {prediction.fighter2_win_probability}%
                </div>
                <div className="text-sm text-gray-600">{fighter2.name}</div>
              </div>
            </div>

            {/* Win Probability Bar */}
            <div className="relative h-10 bg-gray-200 rounded-lg overflow-hidden mb-6">
              <div 
                className="absolute left-0 top-0 h-full bg-blue-600"
                style={{ width: `${prediction.fighter1_win_probability}%` }}
              />
              <div className="absolute inset-0 flex items-center justify-between px-4 text-sm font-semibold">
                <span className="text-white">{fighter1.name.split(' ').slice(-1)[0]}</span>
                <span className="text-gray-700">{fighter2.name.split(' ').slice(-1)[0]}</span>
              </div>
            </div>

            {/* Prediction Details */}
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">Predicted Outcome</h4>
                <div className="text-sm text-gray-600">
                  {getFavorite() && (
                    <>
                      <span className="font-semibold">{getFavorite()!.fighter!.name}</span> is favored 
                      with a {getFavorite()!.probability}% chance of victory
                    </>
                  )}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Rating difference: {Math.abs(prediction.rating_difference)} points
                </div>
              </div>
              
              <div>
                <h4 className="font-semibold mb-2">Likely Method</h4>
                <div className="text-sm">
                  <div className="font-semibold text-blue-600">{prediction.likely_method}</div>
                  <div className="mt-2 space-y-1">
                    <div>KO/TKO: {prediction.method_probabilities.ko_tko}%</div>
                    <div>Submission: {prediction.method_probabilities.submission}%</div>
                    <div>Decision: {prediction.method_probabilities.decision}%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Historical Data */}
          {(previousFights.length > 0 || commonOpponents > 0) && (
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h3 className="text-xl font-bold mb-4">Historical Context</h3>
              
              {previousFights.length > 0 && (
                <div className="mb-4">
                  <h4 className="font-semibold mb-2">Previous Meetings</h4>
                  <div className="space-y-2">
                    {previousFights.map((fight) => (
                      <div key={fight.id} className="text-sm text-gray-600">
                        {formatDate(fight.date)} - {fight.winner === 'fighter1' ? fighter1.name : fighter2.name} won 
                        via {fight.result_method} in Round {fight.result_round}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              {commonOpponents > 0 && (
                <div className="text-sm text-gray-600">
                  These fighters have {commonOpponents} common opponent{commonOpponents > 1 ? 's' : ''}
                </div>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
}