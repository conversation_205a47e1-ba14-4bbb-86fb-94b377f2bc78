"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

interface DbFighter {
  id: number;
  first_name: string;
  last_name: string;
  nickname: string | null;
  url: string;
  total_fights: number;
}

interface FighterSearchResult {
  success: boolean;
  data: Db<PERSON>ighter[];
  total: number;
}

interface FighterSearchProps {
  onSearch?: (value: string) => void;
  onSelect?: (fighter: DbFighter) => void;
  placeholder?: string;
}

export default function FighterSearch({
  onSearch,
  onSelect,
  placeholder,
}: FighterSearchProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [fighters, setFighters] = useState<DbFighter[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const searchFighters = async (query: string) => {
    if (!query.trim()) {
      setFighters([]);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(
        `/api/fighters?search=${encodeURIComponent(query)}&limit=20`
      );
      const data: FighterSearchResult = await response.json();

      if (data.success) {
        setFighters(data.data);
      } else {
        setError("Failed to search fighters");
      }
    } catch (err) {
      setError("Network error occurred");
      console.error("Search error:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchTerm) {
        searchFighters(searchTerm);
        onSearch?.(searchTerm);
      } else {
        setFighters([]);
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, onSearch]);

  const handleFighterClick = (fighter: DbFighter) => {
    if (onSelect) {
      onSelect(fighter);
    } else {
      router.push(`/fighter/db/${fighter.id}`);
    }
  };

  const formatFighterName = (fighter: DbFighter) => {
    const name = `${fighter.first_name} ${fighter.last_name}`;
    return fighter.nickname ? `${name} "${fighter.nickname}"` : name;
  };

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          Search UFC Fighters ({fighters.length} shown)
        </h2>
        <div className="relative">
          <input
            type="text"
            placeholder={placeholder || "Search by fighter name..."}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-lg"
          />
          {loading && (
            <div className="absolute right-3 top-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600"></div>
            </div>
          )}
        </div>
        {error && <p className="mt-2 text-red-600 text-sm">{error}</p>}
      </div>

      {searchTerm && !loading && fighters.length === 0 && !error && (
        <div className="text-center py-8 text-gray-500">
          No fighters found matching &quot;{searchTerm}&quot;
        </div>
      )}

      {fighters.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              Found {fighters.length} fighter{fighters.length !== 1 ? "s" : ""}
            </h3>
          </div>
          <div className="divide-y divide-gray-200">
            {fighters.map((fighter) => (
              <div
                key={fighter.id}
                onClick={() => handleFighterClick(fighter)}
                className="px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors duration-150"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-lg font-medium text-gray-900">
                      {formatFighterName(fighter)}
                    </h4>
                    <p className="text-sm text-gray-500">
                      {fighter.total_fights} UFC fight
                      {fighter.total_fights !== 1 ? "s" : ""}
                    </p>
                  </div>
                  <div className="text-red-600">
                    <svg
                      className="w-5 h-5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {!searchTerm && fighters.length === 0 && !loading && (
        <div className="text-center py-12 text-gray-500">
          <svg
            className="w-16 h-16 mx-auto mb-4 text-gray-300"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          <p className="text-lg">Start typing to search for UFC fighters</p>
          <p className="text-sm mt-1">
            Search through 4000+ fighters in the database
          </p>
        </div>
      )}
    </div>
  );
}
