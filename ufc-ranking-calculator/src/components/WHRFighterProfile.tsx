"use client";

import { useState, useEffect, useCallback } from "react";
import { useParams } from "next/navigation";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  ReferenceLine,
} from "recharts";
// import {
//   Trophy,
//   TrendingUp,
//   TrendingDown,
//   Calendar,
//   Target,
//   Award,
//   Activity,
//   BarChart3
// } from 'lucide-react';
import { format } from "date-fns";

interface FighterInfo {
  id: number;
  first_name: string;
  last_name: string;
  nickname?: string;
  birthdate: string;
  height?: string;
  weight?: string;
  reach?: string;
  stance?: string;
  weight_class?: string;
}

interface FighterRating {
  division: string;
  rating: number;
  rating_deviation: number;
  fight_count: number;
  win_count: number;
  loss_count: number;
  last_fight_date: string;
  rank?: number;
  streak?: number;
  dominance_score?: number;
  activity_score?: number;
  status?: string;
}

interface RatingHistory {
  fight_id: number;
  division: string;
  opponent_name: string;
  opponent_id: number;
  pre_rating: number;
  post_rating: number;
  rating_change: number;
  opponent_rating: number;
  expected_outcome: number;
  actual_outcome: number;
  result: number;
  surprise_factor: number;
  k_factor: number;
  fight_date: string;
  event_name: string;
  result_method: string;
  result_round?: number;
}

interface PerformanceMetric {
  division: string;
  total_fights: number;
  wins_in_division: number;
  avg_rating_change: number;
  peak_rating: number;
  lowest_rating: number;
  avg_opponent_rating: number;
}

export default function WHRFighterProfile() {
  const params = useParams();
  const fighterId = params.id as string;

  const [fighter, setFighter] = useState<FighterInfo | null>(null);
  const [ratings, setRatings] = useState<FighterRating[]>([]);
  const [ratingHistory, setRatingHistory] = useState<RatingHistory[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<
    PerformanceMetric[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [selectedDivision, setSelectedDivision] = useState<string>("");

  const fetchFighterData = useCallback(async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/whr/fighter/${fighterId}`);
      const data = await response.json();

      if (data.success) {
        setFighter(data.data.fighter);
        setRatings(data.data.ratings);
        setRatingHistory(data.data.ratingHistory);
        setPerformanceMetrics(data.data.performanceMetrics);

        // Set default selected division to highest rated
        if (data.data.ratings.length > 0) {
          setSelectedDivision(data.data.ratings[0].division);
        }
      }
    } catch (error) {
      console.error("Failed to fetch fighter data:", error);
    } finally {
      setLoading(false);
    }
  }, [fighterId]);

  useEffect(() => {
    fetchFighterData();
  }, [fighterId, fetchFighterData]);

  const getRatingColor = (rating: number) => {
    if (rating >= 1700) return "text-purple-600 dark:text-purple-400";
    if (rating >= 1600) return "text-blue-600 dark:text-blue-400";
    if (rating >= 1500) return "text-green-600 dark:text-green-400";
    if (rating >= 1400) return "text-yellow-600 dark:text-yellow-400";
    return "text-gray-600 dark:text-gray-400";
  };

  const getResultBadge = (result: number, method: string) => {
    if (result > 0.5) {
      if (method.includes("KO") || method.includes("TKO")) {
        return <Badge variant="destructive">KO/TKO Win</Badge>;
      } else if (method.includes("Submission")) {
        return <Badge variant="warning">Submission Win</Badge>;
      } else {
        return <Badge variant="success">Decision Win</Badge>;
      }
    } else if (result < 0.5) {
      return <Badge variant="secondary">Loss</Badge>;
    } else {
      return <Badge variant="outline">Draw</Badge>;
    }
  };

  // Prepare chart data
  const chartData = ratingHistory
    .filter((h) => h.division === selectedDivision)
    .map((h) => ({
      date: format(new Date(h.fight_date), "MMM yyyy"),
      rating: h.post_rating,
      opponent: h.opponent_name,
      change: h.rating_change,
    }))
    .reverse();

  const currentRating = ratings.find((r) => r.division === selectedDivision);
  const divisionMetrics = performanceMetrics.find(
    (m) => m.division === selectedDivision
  );

  if (loading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!fighter) {
    return <div>Fighter not found</div>;
  }

  return (
    <div className="space-y-6">
      {/* Fighter Header */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-start justify-between">
            <div>
              <h1 className="text-3xl font-bold">
                {fighter.first_name} {fighter.last_name}
                {fighter.nickname && (
                  <span className="text-muted-foreground ml-2">
                    &quot;{fighter.nickname}&quot;
                  </span>
                )}
              </h1>
              <div className="flex items-center gap-4 mt-2 text-muted-foreground">
                {fighter.weight_class && <span>{fighter.weight_class}</span>}
                {fighter.height && <span>{fighter.height}</span>}
                {fighter.reach && <span>{fighter.reach}&quot; reach</span>}
                {fighter.stance && <span>{fighter.stance}</span>}
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground">Career Record</div>
              <div className="text-2xl font-bold">
                {ratings.reduce((sum, r) => sum + r.win_count, 0)}-
                {ratings.reduce((sum, r) => sum + r.loss_count, 0)}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Division Ratings */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {ratings.map((rating) => (
          <Card
            key={rating.division}
            className={`cursor-pointer transition-all ${
              selectedDivision === rating.division ? "ring-2 ring-primary" : ""
            }`}
            onClick={() => setSelectedDivision(rating.division)}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium flex items-center justify-between">
                {rating.division}
                {rating.rank && (
                  <Badge variant="secondary">#{rating.rank}</Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div
                className={`text-2xl font-bold ${getRatingColor(
                  rating.rating
                )}`}
              >
                {rating.rating.toFixed(0)}
                <span className="text-sm font-normal text-muted-foreground ml-1">
                  ±{rating.rating_deviation.toFixed(0)}
                </span>
              </div>
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="outline">
                  {rating.win_count}-{rating.loss_count}
                </Badge>
                {rating.streak && rating.streak !== 0 && (
                  <Badge
                    variant={rating.streak > 0 ? "success" : "destructive"}
                  >
                    {rating.streak > 0 ? "W" : "L"}
                    {Math.abs(rating.streak)}
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Division View */}
      {currentRating && (
        <Tabs defaultValue="history" className="space-y-4">
          <TabsList>
            <TabsTrigger value="history">Fight History</TabsTrigger>
            <TabsTrigger value="chart">Rating Chart</TabsTrigger>
            <TabsTrigger value="stats">Statistics</TabsTrigger>
          </TabsList>

          {/* Fight History Tab */}
          <TabsContent value="history">
            <Card>
              <CardHeader>
                <CardTitle>{selectedDivision} Fight History</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {ratingHistory
                    .filter((h) => h.division === selectedDivision)
                    .map((fight, idx) => (
                      <div
                        key={idx}
                        className="border-b last:border-0 pb-3 last:pb-0"
                      >
                        <div className="flex items-start justify-between">
                          <div>
                            <div className="font-medium">
                              vs {fight.opponent_name} (
                              {fight.opponent_rating.toFixed(0)})
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {fight.event_name} •{" "}
                              {format(
                                new Date(fight.fight_date),
                                "MMM d, yyyy"
                              )}
                            </div>
                          </div>
                          {getResultBadge(fight.result, fight.result_method)}
                        </div>

                        <div className="mt-2 grid grid-cols-3 gap-2 text-sm">
                          <div>
                            <span className="text-muted-foreground">
                              Rating Change:
                            </span>
                            <span
                              className={`ml-1 font-mono ${
                                fight.rating_change > 0
                                  ? "text-green-600"
                                  : "text-red-600"
                              }`}
                            >
                              {fight.rating_change > 0 ? "+" : ""}
                              {fight.rating_change.toFixed(1)}
                            </span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">
                              Expected:
                            </span>
                            <span className="ml-1 font-mono">
                              {(fight.expected_outcome * 100).toFixed(0)}%
                            </span>
                          </div>
                          <div>
                            <span className="text-muted-foreground">
                              New Rating:
                            </span>
                            <span className="ml-1 font-mono">
                              {fight.post_rating.toFixed(0)}
                            </span>
                          </div>
                        </div>

                        {fight.surprise_factor > 0.3 && (
                          <Badge variant="warning" className="mt-2 text-xs">
                            {fight.result > 0.5 ? "Upset Win" : "Upset Loss"}
                          </Badge>
                        )}
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Rating Chart Tab */}
          <TabsContent value="chart">
            <Card>
              <CardHeader>
                <CardTitle>{selectedDivision} Rating Progression</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[400px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis domain={["dataMin - 50", "dataMax + 50"]} />
                      <Tooltip
                        content={({ active, payload }) => {
                          if (active && payload && payload[0]) {
                            const data = payload[0].payload;
                            return (
                              <div className="bg-background border rounded p-2 shadow-lg">
                                <div className="font-medium">{data.date}</div>
                                <div className="text-sm">
                                  vs {data.opponent}
                                </div>
                                <div className="text-sm">
                                  Rating: {data.rating.toFixed(0)}
                                  <span
                                    className={`ml-1 ${
                                      data.change > 0
                                        ? "text-green-600"
                                        : "text-red-600"
                                    }`}
                                  >
                                    ({data.change > 0 ? "+" : ""}
                                    {data.change.toFixed(1)})
                                  </span>
                                </div>
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <Line
                        type="monotone"
                        dataKey="rating"
                        stroke="hsl(var(--primary))"
                        strokeWidth={2}
                        dot={{ fill: "hsl(var(--primary))" }}
                      />
                      <ReferenceLine
                        y={1500}
                        stroke="gray"
                        strokeDasharray="3 3"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Statistics Tab */}
          <TabsContent value="stats">
            <Card>
              <CardHeader>
                <CardTitle>{selectedDivision} Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                {divisionMetrics && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div>
                        <div className="text-sm text-muted-foreground">
                          Total Fights
                        </div>
                        <div className="text-2xl font-bold">
                          {divisionMetrics.total_fights}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">
                          Win Rate
                        </div>
                        <div className="text-2xl font-bold">
                          {(
                            (divisionMetrics.wins_in_division /
                              divisionMetrics.total_fights) *
                            100
                          ).toFixed(0)}
                          %
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">
                          Avg Rating Change
                        </div>
                        <div
                          className={`text-2xl font-bold ${
                            divisionMetrics.avg_rating_change > 0
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          {divisionMetrics.avg_rating_change > 0 ? "+" : ""}
                          {divisionMetrics.avg_rating_change.toFixed(1)}
                        </div>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div>
                        <div className="text-sm text-muted-foreground">
                          Peak Rating
                        </div>
                        <div className="text-2xl font-bold">
                          {divisionMetrics.peak_rating.toFixed(0)}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">
                          Lowest Rating
                        </div>
                        <div className="text-2xl font-bold">
                          {divisionMetrics.lowest_rating.toFixed(0)}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-muted-foreground">
                          Avg Opponent Rating
                        </div>
                        <div className="text-2xl font-bold">
                          {divisionMetrics.avg_opponent_rating.toFixed(0)}
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
