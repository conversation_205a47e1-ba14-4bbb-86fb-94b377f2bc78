export interface Fighter {
  id: string;
  name: string;
  birthdate: Date;
  weightClass: WeightClass;
  fights: Fight[];
  rank?: number;
  fightingStyle?: string;
  height?: string;
  weight?: string;
  reach?: string;
}

export interface Fight {
  id: string;
  date: Date;
  opponent: string;
  opponentId: string;
  result: FightResult;
  method: FinishType;
  round: number;
  stats: FightStats;
  opponentStats: FightStats;
  opponentRank?: number;
  weightClass?: string; // Weight class for this specific fight
  ageAtFight?: number; // Decimal years (e.g., 27.3)
  ageMonths?: number; // Total months for precise bucketing
  ageBracket?: string; // Age bracket (e.g., "27-28")
  ageQuarter?: number; // Quarterly precision (27.0, 27.25, 27.5, 27.75)
}

export interface FightStats {
  significantStrikesLanded: number;
  significantStrikesAttempted: number;
  knockdowns: number;
  takedownsLanded: number;
  takedownsAttempted: number;
  submissionAttempts: number;
  controlTime?: number; // in seconds, from database
  totalFightTime?: number; // calculated from round * 5 minutes
}

export type FightResult = 'win' | 'loss' | 'draw' | 'nc';

export type FinishType = 
  // Code format (original)
  | 'KO' 
  | 'TKO' 
  | 'SUB' 
  | 'DEC_U' 
  | 'DEC_M' 
  | 'DEC_S' 
  | 'DRAW_U'
  | 'DRAW_M'
  | 'DRAW_S'
  | 'DQ' 
  | 'NC'
  // Database formats
  | 'KO/TKO'
  | 'Submission'
  | 'Decision - Unanimous'
  | 'Decision - Majority'
  | 'Decision - Split'
  | 'Draw - Unanimous'
  | 'Draw - Majority'
  | 'Draw - Split'
  | 'No Contest'
  | 'TKO - Doctor\'s Stoppage';

export type WeightClass = 
  | 'Men_Flyweight'
  | 'Men_Bantamweight' 
  | 'Men_Featherweight'
  | 'Men_Lightweight'
  | 'Men_Welterweight'
  | 'Men_Middleweight'
  | 'Men_LightHeavyweight'
  | 'Men_Heavyweight'
  | 'Women_Strawweight'
  | 'Women_Flyweight'
  | 'Women_Bantamweight'
  | 'Women_Featherweight';

export interface PerformanceMetrics {
  strikingRatio: number;
  strikingDefense: number;
  takedownDefense: number;
  knockdowns: number;
  submissionAttempts: number;
  takedownAverage: number;
  strikingAccuracy: number;
  takedownAccuracy: number;
}

export interface DivisionConfig {
  primeRange: [number, number];
  median: number;
  sigmaPre: number;
  sigmaPost: number;
}