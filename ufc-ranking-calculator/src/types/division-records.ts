import { Fight, WeightClass, PerformanceMetrics } from './fighter';

/**
 * Division-specific fighter record for calculations
 */
export interface FighterDivisionRecord {
  fighterId: string;
  fighterName: string;
  birthdate: Date;
  weightClass: WeightClass;
  
  // Division-specific data
  fights: Fight[]; // Only fights at this weight class (excluding catch weight)
  rank?: number; // Division-specific rank
  metrics?: PerformanceMetrics; // Calculated only from this division
  
  // Division-specific stats
  divisionRecord: {
    wins: number;
    losses: number;
    draws: number;
    noContests: number;
    koWins: number;
    subWins: number;
    decWins: number;
  };
  
  // Division-specific aggregated statistics
  divisionStats: {
    totalSignificantStrikesLanded: number;
    totalSignificantStrikesAttempted: number;
    totalSignificantStrikesAbsorbed: number;
    strikingAccuracy: number; // percentage
    strikesAbsorbedPerMinute: number;
    strikingDefensePercentage: number;
    totalTakedownsLanded: number;
    totalTakedownsAttempted: number;
    takedownAccuracy: number; // percentage
    takedownDefense: number; // percentage
    totalKnockdowns: number;
    totalSubmissionAttempts: number;
    totalControlTimeSeconds: number;
    totalFightTimeSeconds: number;
    averageFightTimeSeconds: number;
    finishRate: number; // percentage
  };
  
  // Activity tracking
  lastFightDate: Date | null;
  totalDivisionFights: number;
  
  // Performance indicators
  currentStreak: number; // Positive for wins, negative for losses
}

/**
 * Complete fighter record for UI display
 */
export interface FighterCompleteRecord {
  // Core identity
  id: string;
  name: string;
  birthdate: Date;
  
  // Physical attributes
  height?: string;
  weight?: string;
  reach?: string;
  stance?: string;
  
  // Career overview
  mainWeightClass: WeightClass; // Most fights or most recent
  currentWeightClass: WeightClass; // Latest fight's division
  allFights: Fight[]; // All fights across all divisions
  
  // Division breakdown
  divisionRecords: Map<WeightClass, FighterDivisionRecord>;
  
  // Career totals
  careerStats: {
    totalFights: number;
    totalWins: number;
    totalLosses: number;
    totalDraws: number;
    totalNoContests: number;
    finishRate: number;
    totalKnockdowns: number;
    totalControlTime: number;
    averageFightTime: number;
  };
  
  // Weight class history
  divisionTimeline: DivisionTimelineEntry[];
}

/**
 * Track fighter's movement between divisions
 */
export interface DivisionTimelineEntry {
  weightClass: WeightClass;
  firstFightDate: Date;
  lastFightDate: Date;
  fightCount: number;
  isCurrent: boolean;
}

/**
 * Division ranking entry
 */
export interface DivisionRanking {
  weightClass: WeightClass;
  rankings: {
    rank: number;
    fighterDivisionRecord: FighterDivisionRecord;
    rating: number;
    lastFightDate: Date;
  }[];
  lastUpdated: Date;
}