/**
 * Division-specific fighter data adapter
 */

import { getDatabase } from './database';
import { Fighter, Fight, WeightClass } from '@/types/fighter';
import { FighterDivisionRecord, FighterCompleteRecord, DivisionTimelineEntry } from '@/types/division-records';
import { loadFighterWithHistory } from './fighter-data-adapter';

/**
 * Load a fighter's record for a specific weight class
 */
export async function loadFighterDivisionRecord(
  fighterId: number, 
  weightClass: WeightClass
): Promise<FighterDivisionRecord | null> {
  const fighter = await loadFighterWithHistory(fighterId);
  if (!fighter) return null;
  
  // Filter fights for this weight class
  const divisionFights = fighter.fights.filter(fight => 
    fight.weightClass === weightClass
  );
  
  // Calculate division-specific stats
  const divisionRecord = calculateDivisionRecord(divisionFights);
  const divisionStats = calculateDivisionAggregateStats(divisionFights);
  const lastFight = divisionFights.length > 0 
    ? divisionFights[divisionFights.length - 1] 
    : null;
  
  return {
    fighterId: fighter.id,
    fighterName: fighter.name,
    birthdate: fighter.birthdate,
    weightClass,
    fights: divisionFights,
    divisionRecord,
    divisionStats,
    lastFightDate: lastFight?.date || null,
    totalDivisionFights: divisionFights.length,
    currentStreak: calculateStreak(divisionFights),
  };
}

/**
 * Load all fighters who have competed in a specific division
 */
export async function loadDivisionFighters(weightClass: WeightClass): Promise<FighterDivisionRecord[]> {
  const db = getDatabase();
  
  // Get all unique fighter IDs who have fought in this division
  const fighterIdsQuery = db.prepare(`
    SELECT DISTINCT fighter_id FROM (
      SELECT fighter1_id as fighter_id FROM fights WHERE weight_class = ? AND weight_class != 'Catch Weight'
      UNION
      SELECT fighter2_id as fighter_id FROM fights WHERE weight_class = ? AND weight_class != 'Catch Weight'
    )
  `);
  
  const fighterIds = fighterIdsQuery.all(weightClass, weightClass) as { fighter_id: number }[];
  
  // Load division records for each fighter
  const divisionRecords: FighterDivisionRecord[] = [];
  
  for (const { fighter_id } of fighterIds) {
    const record = await loadFighterDivisionRecord(fighter_id, weightClass);
    if (record && record.totalDivisionFights > 0) {
      divisionRecords.push(record);
    }
  }
  
  return divisionRecords;
}

/**
 * Load complete fighter record with all divisions
 */
export async function loadFighterCompleteRecord(fighterId: number): Promise<FighterCompleteRecord | null> {
  const fighter = await loadFighterWithHistory(fighterId);
  if (!fighter) return null;
  
  // Group fights by weight class
  const fightsByDivision = new Map<WeightClass, Fight[]>();
  const divisionTimeline: DivisionTimelineEntry[] = [];
  
  fighter.fights.forEach(fight => {
    if (fight.weightClass && fight.weightClass !== 'Catch Weight') {
      const weightClass = fight.weightClass as WeightClass;
      if (!fightsByDivision.has(weightClass)) {
        fightsByDivision.set(weightClass, []);
      }
      fightsByDivision.get(weightClass)!.push(fight);
    }
  });
  
  // Create division records
  const divisionRecords = new Map<WeightClass, FighterDivisionRecord>();
  
  for (const [weightClass, fights] of fightsByDivision) {
    const divisionRecord = calculateDivisionRecord(fights);
    const divisionStats = calculateDivisionAggregateStats(fights);
    const lastFight = fights[fights.length - 1];
    const firstFight = fights[0];
    
    divisionRecords.set(weightClass, {
      fighterId: fighter.id,
      fighterName: fighter.name,
      birthdate: fighter.birthdate,
      weightClass,
      fights,
      divisionRecord,
      divisionStats,
      lastFightDate: lastFight.date,
      totalDivisionFights: fights.length,
      currentStreak: calculateStreak(fights),
    });
    
    divisionTimeline.push({
      weightClass,
      firstFightDate: firstFight.date,
      lastFightDate: lastFight.date,
      fightCount: fights.length,
      isCurrent: lastFight === fighter.fights[fighter.fights.length - 1]
    });
  }
  
  // Determine main and current weight classes
  const mainWeightClass = determineMainWeightClass(fightsByDivision, fighter.weightClass);
  const lastFight = fighter.fights[fighter.fights.length - 1];
  const currentWeightClass = (lastFight?.weightClass as WeightClass) || mainWeightClass;
  
  // Calculate career totals
  const careerStats = calculateCareerStats(fighter);
  
  return {
    id: fighter.id,
    name: fighter.name,
    birthdate: fighter.birthdate,
    height: fighter.height,
    weight: fighter.weight,
    reach: fighter.reach,
    stance: fighter.fightingStyle,
    mainWeightClass,
    currentWeightClass,
    allFights: fighter.fights,
    divisionRecords,
    careerStats,
    divisionTimeline: divisionTimeline.sort((a, b) => 
      b.lastFightDate.getTime() - a.lastFightDate.getTime()
    )
  };
}

/**
 * Calculate division-specific win/loss record
 */
function calculateDivisionRecord(fights: Fight[]) {
  const stats = {
    wins: 0,
    losses: 0,
    draws: 0,
    noContests: 0,
    koWins: 0,
    subWins: 0,
    decWins: 0
  };
  
  fights.forEach(fight => {
    switch (fight.result) {
      case 'win':
        stats.wins++;
        if (fight.method === 'KO' || fight.method === 'TKO' || fight.method === 'KO/TKO') {
          stats.koWins++;
        } else if (fight.method === 'SUB' || fight.method === 'Submission') {
          stats.subWins++;
        } else if (fight.method.includes('DEC') || fight.method.includes('Decision')) {
          stats.decWins++;
        }
        break;
      case 'loss':
        stats.losses++;
        break;
      case 'draw':
        stats.draws++;
        break;
      case 'nc':
        stats.noContests++;
        break;
    }
  });
  
  return stats;
}

/**
 * Calculate division-specific aggregate statistics
 */
function calculateDivisionAggregateStats(fights: Fight[]) {
  let totalStrikesLanded = 0;
  let totalStrikesAttempted = 0;
  let totalStrikesAbsorbed = 0;
  let totalTakedownsLanded = 0;
  let totalTakedownsAttempted = 0;
  let opponentTakedownsAttempted = 0;
  let opponentTakedownsLanded = 0;
  let totalKnockdowns = 0;
  let totalSubmissionAttempts = 0;
  let totalControlTime = 0;
  let totalFightTime = 0;
  let finishes = 0;
  let wins = 0;
  
  fights.forEach(fight => {
    const { stats, opponentStats, result, method } = fight;
    
    // Accumulate striking stats
    totalStrikesLanded += stats.significantStrikesLanded;
    totalStrikesAttempted += stats.significantStrikesAttempted;
    totalStrikesAbsorbed += opponentStats.significantStrikesLanded;
    
    // Accumulate grappling stats
    totalTakedownsLanded += stats.takedownsLanded;
    totalTakedownsAttempted += stats.takedownsAttempted;
    opponentTakedownsAttempted += opponentStats.takedownsAttempted;
    opponentTakedownsLanded += opponentStats.takedownsLanded;
    
    // Other stats
    totalKnockdowns += stats.knockdowns;
    totalSubmissionAttempts += stats.submissionAttempts;
    totalControlTime += stats.controlTime || 0;
    totalFightTime += stats.totalFightTime || 0;
    
    // Track finishes
    if (result === 'win') {
      wins++;
      if (['KO', 'TKO', 'SUB', 'KO/TKO', 'Submission'].includes(method)) {
        finishes++;
      }
    }
  });
  
  const totalFights = fights.length;
  const fightMinutes = totalFightTime / 60;
  
  return {
    totalSignificantStrikesLanded: totalStrikesLanded,
    totalSignificantStrikesAttempted: totalStrikesAttempted,
    totalSignificantStrikesAbsorbed: totalStrikesAbsorbed,
    strikingAccuracy: totalStrikesAttempted > 0 
      ? (totalStrikesLanded / totalStrikesAttempted) * 100 
      : 0,
    strikesAbsorbedPerMinute: fightMinutes > 0 
      ? totalStrikesAbsorbed / fightMinutes 
      : 0,
    strikingDefensePercentage: totalStrikesAttempted > 0
      ? (1 - (totalStrikesAbsorbed / totalStrikesAttempted)) * 100
      : 0,
    totalTakedownsLanded,
    totalTakedownsAttempted,
    takedownAccuracy: totalTakedownsAttempted > 0
      ? (totalTakedownsLanded / totalTakedownsAttempted) * 100
      : 0,
    takedownDefense: opponentTakedownsAttempted > 0
      ? (1 - (opponentTakedownsLanded / opponentTakedownsAttempted)) * 100
      : 100,
    totalKnockdowns,
    totalSubmissionAttempts,
    totalControlTimeSeconds: totalControlTime,
    totalFightTimeSeconds: totalFightTime,
    averageFightTimeSeconds: totalFights > 0 ? totalFightTime / totalFights : 0,
    finishRate: wins > 0 ? (finishes / wins) * 100 : 0
  };
}

/**
 * Calculate current win/loss streak
 */
function calculateStreak(fights: Fight[]): number {
  if (fights.length === 0) return 0;
  
  let streak = 0;
  const lastResult = fights[fights.length - 1].result;
  
  // Count backwards from most recent fight
  for (let i = fights.length - 1; i >= 0; i--) {
    const fight = fights[i];
    if (fight.result === 'nc') continue; // Skip no contests
    
    if ((lastResult === 'win' && fight.result === 'win') ||
        (lastResult === 'loss' && fight.result === 'loss')) {
      streak += (lastResult === 'win' ? 1 : -1);
    } else {
      break;
    }
  }
  
  return streak;
}

/**
 * Determine main weight class based on fight history
 */
function determineMainWeightClass(
  fightsByDivision: Map<WeightClass, Fight[]>, 
  defaultWeightClass: WeightClass
): WeightClass {
  let mainWeightClass = defaultWeightClass;
  let maxFights = 0;
  
  // Find division with most fights
  fightsByDivision.forEach((fights, weightClass) => {
    if (fights.length > maxFights) {
      maxFights = fights.length;
      mainWeightClass = weightClass;
    }
  });
  
  return mainWeightClass;
}

/**
 * Calculate career statistics across all divisions
 */
function calculateCareerStats(fighter: Fighter) {
  let totalWins = 0, totalLosses = 0, totalDraws = 0, totalNoContests = 0;
  let totalKnockdowns = 0, totalControlTime = 0, totalFightTime = 0;
  let finishes = 0;
  
  fighter.fights.forEach(fight => {
    switch (fight.result) {
      case 'win': 
        totalWins++; 
        if (fight.method === 'KO' || fight.method === 'TKO' || 
            fight.method === 'SUB' || fight.method === 'Submission' ||
            fight.method === 'KO/TKO') {
          finishes++;
        }
        break;
      case 'loss': totalLosses++; break;
      case 'draw': totalDraws++; break;
      case 'nc': totalNoContests++; break;
    }
    
    totalKnockdowns += fight.stats.knockdowns || 0;
    totalControlTime += fight.stats.controlTime || 0;
    totalFightTime += fight.stats.totalFightTime || 0;
  });
  
  const totalFights = fighter.fights.length;
  const finishRate = totalWins > 0 ? (finishes / totalWins) * 100 : 0;
  const averageFightTime = totalFights > 0 ? totalFightTime / totalFights : 0;
  
  return {
    totalFights,
    totalWins,
    totalLosses,
    totalDraws,
    totalNoContests,
    finishRate,
    totalKnockdowns,
    totalControlTime,
    averageFightTime
  };
}