/**
 * Data adapter layer to transform database data into Fighter objects for calculations.ts
 */

import { getDatabase } from './database';
import { Fighter, Fight, FightStats, WeightClass, FinishType, FightResult } from '@/types/fighter';
import { Db<PERSON>ighter } from '@/lib/database';
import { parseControlTime, calculateTotalFightTime } from '@/lib/data-mappers';

interface FightQueryResult {
  id: number;
  bout: string;
  fighter1_id: number;
  fighter2_id: number;
  winner_id: number | null;
  result_method: string | null;
  result_round: number | null;
  result_time: string | null;
  event_date: string;
  event_name: string;
  opponent_name: string;
  opponent_id: number;
  weight_class: string | null;
  fighter_age?: number | null;
  fighter_age_months?: number | null;
  fighter_age_bracket?: string | null;
  fighter_age_quarter?: number | null;
}

interface StatsQueryResult {
  fight_id: number;
  fighter_id: number;
  round_number: number;
  sig_strikes_landed: number;
  sig_strikes_attempted: number;
  knockdowns: number;
  takedowns_landed: number;
  takedowns_attempted: number;
  submission_attempts: number;
  control_time: string | null;
}

/**
 * Load all fighters with their complete fight history
 */
export async function loadAllFighters(): Promise<Fighter[]> {
  const db = getDatabase();
  
  // Get all fighters
  const fightersQuery = db.prepare(`
    SELECT * FROM fighters 
    WHERE total_fights > 0
    ORDER BY total_fights DESC
  `);
  
  const dbFighters = fightersQuery.all() as DbFighter[];
  const fighters: Fighter[] = [];
  
  for (const dbFighter of dbFighters) {
    const fighter = await loadFighterWithHistory(dbFighter.id);
    if (fighter) {
      fighters.push(fighter);
    }
  }
  
  return fighters;
}

/**
 * Load a single fighter with complete fight history
 */
export async function loadFighterWithHistory(fighterId: number): Promise<Fighter | null> {
  const db = getDatabase();
  
  // Get fighter details
  const fighterQuery = db.prepare('SELECT * FROM fighters WHERE id = ?');
  const dbFighter = fighterQuery.get(fighterId) as DbFighter | undefined;
  
  if (!dbFighter) {
    return null;
  }
  
  // Get all fights for this fighter
  const fightsQuery = db.prepare(`
    SELECT 
      f.*,
      e.date as event_date,
      e.event_name,
      CASE 
        WHEN f.fighter1_id = ? THEN f.fighter2_id
        ELSE f.fighter1_id
      END as opponent_id,
      CASE 
        WHEN f.fighter1_id = ? THEN f2.first_name || ' ' || f2.last_name
        ELSE f1.first_name || ' ' || f1.last_name
      END as opponent_name,
      CASE 
        WHEN f.fighter1_id = ? THEN f.fighter1_age
        ELSE f.fighter2_age
      END as fighter_age,
      CASE 
        WHEN f.fighter1_id = ? THEN f.fighter1_age_months
        ELSE f.fighter2_age_months
      END as fighter_age_months,
      CASE 
        WHEN f.fighter1_id = ? THEN f.fighter1_age_bracket
        ELSE f.fighter2_age_bracket
      END as fighter_age_bracket,
      CASE 
        WHEN f.fighter1_id = ? THEN f.fighter1_age_quarter
        ELSE f.fighter2_age_quarter
      END as fighter_age_quarter
    FROM fights f
    JOIN events e ON f.event_id = e.id
    LEFT JOIN fighters f1 ON f.fighter1_id = f1.id
    LEFT JOIN fighters f2 ON f.fighter2_id = f2.id
    WHERE f.fighter1_id = ? OR f.fighter2_id = ?
    ORDER BY e.date ASC
  `);
  
  const dbFights = fightsQuery.all(fighterId, fighterId, fighterId, fighterId, fighterId, fighterId, fighterId, fighterId) as FightQueryResult[];
  
  // Convert to Fighter object
  return convertToFighter(dbFighter, dbFights);
}

/**
 * Convert database records to Fighter object
 */
function convertToFighter(dbFighter: DbFighter, dbFights: FightQueryResult[]): Fighter {
  const fights: Fight[] = [];
  const db = getDatabase();
  
  for (const dbFight of dbFights) {
    // Get stats for both fighters in this fight
    const statsQuery = db.prepare(`
      SELECT * FROM fight_stats 
      WHERE fight_id = ? 
      ORDER BY fighter_id, round_number
    `);
    
    const allStats = statsQuery.all(dbFight.id) as StatsQueryResult[];
    
    // Separate stats by fighter
    const fighterStats = allStats.filter(s => s.fighter_id === dbFighter.id);
    const opponentStats = allStats.filter(s => s.fighter_id === dbFight.opponent_id);
    
    // Aggregate stats
    const stats = aggregateStats(fighterStats, dbFight);
    const oppStats = aggregateStats(opponentStats, dbFight);
    
    // Determine result
    let result: FightResult;
    if (!dbFight.winner_id) {
      // Check if it's a draw based on result_method
      if (dbFight.result_method && dbFight.result_method.toLowerCase().includes('draw')) {
        result = 'draw';
      } else {
        result = 'nc';
      }
    } else if (dbFight.winner_id === dbFighter.id) {
      result = 'win';
    } else {
      result = 'loss';
    }
    
    // Map finish method
    const method = mapFinishMethod(dbFight.result_method);
    
    fights.push({
      id: dbFight.id.toString(),
      date: new Date(dbFight.event_date),
      opponent: dbFight.opponent_name,
      opponentId: dbFight.opponent_id.toString(),
      result,
      method,
      round: dbFight.result_round || 3,
      stats,
      opponentStats: oppStats,
      weightClass: dbFight.weight_class || undefined,
      ageAtFight: dbFight.fighter_age ?? undefined,
      ageMonths: dbFight.fighter_age_months ?? undefined,
      ageBracket: dbFight.fighter_age_bracket ?? undefined,
      ageQuarter: dbFight.fighter_age_quarter ?? undefined
    });
  }
  
  // Determine weight class
  const weightClass = determineWeightClass(dbFighter, dbFights);
  
  return {
    id: dbFighter.id.toString(),
    name: `${dbFighter.first_name} ${dbFighter.last_name}`,
    birthdate: dbFighter.birthdate ? new Date(dbFighter.birthdate) : undefined,
    weightClass,
    fights,
    fightingStyle: dbFighter.stance || 'Unknown',
    // Include all biographical data for potential future use
    height: dbFighter.height || undefined,
    weight: dbFighter.weight || undefined,
    reach: dbFighter.reach || undefined
  };
}

/**
 * Aggregate round-by-round stats into fight totals
 */
function aggregateStats(roundStats: StatsQueryResult[], fight: FightQueryResult): FightStats {
  const totals = {
    significantStrikesLanded: 0,
    significantStrikesAttempted: 0,
    knockdowns: 0,
    takedownsLanded: 0,
    takedownsAttempted: 0,
    submissionAttempts: 0,
    controlTime: 0,
    totalFightTime: 0
  };
  
  for (const round of roundStats) {
    totals.significantStrikesLanded += round.sig_strikes_landed;
    totals.significantStrikesAttempted += round.sig_strikes_attempted;
    totals.knockdowns += round.knockdowns;
    totals.takedownsLanded += round.takedowns_landed;
    totals.takedownsAttempted += round.takedowns_attempted;
    totals.submissionAttempts += round.submission_attempts;
    totals.controlTime += parseControlTime(round.control_time);
  }
  
  // Calculate total fight time
  if (fight.result_round && fight.result_time) {
    totals.totalFightTime = calculateTotalFightTime(
      fight.result_round,
      fight.result_time
    );
  } else if (roundStats.length > 0) {
    // If no finish time, use number of rounds
    const maxRound = Math.max(...roundStats.map(s => s.round_number));
    totals.totalFightTime = maxRound * 5 * 60; // 5 minutes per round
  }
  
  return totals;
}

/**
 * Map database finish method to FinishType enum
 */
function mapFinishMethod(method: string | null): FinishType {
  if (!method) return 'DEC_U';
  
  const normalized = method.toUpperCase();
  
  if (normalized.includes('KO')) return 'KO';
  if (normalized.includes('TKO')) return 'TKO';
  if (normalized.includes('SUB')) return 'SUB';
  if (normalized.includes('DQ')) return 'DQ';
  if (normalized.includes('NC') || normalized.includes('NO CONTEST')) return 'NC';
  
  // Draw types
  if (normalized.includes('DRAW')) {
    if (normalized.includes('SPLIT')) return 'DRAW_S';
    if (normalized.includes('MAJORITY')) return 'DRAW_M';
    if (normalized.includes('UNANIMOUS')) return 'DRAW_U';
    return 'DRAW_U'; // Default draw type
  }
  
  // Decision types
  if (normalized.includes('SPLIT')) return 'DEC_S';
  if (normalized.includes('MAJORITY')) return 'DEC_M';
  if (normalized.includes('UNANIMOUS')) return 'DEC_U';
  
  // Default to unanimous decision
  return 'DEC_U';
}

/**
 * Determine fighter's weight class from their fights
 */
function determineWeightClass(fighter: DbFighter, fights: FightQueryResult[]): WeightClass {
  // First check if fighter has a weight class set
  if (fighter.weight_class) {
    return mapWeightClass(fighter.weight_class);
  }
  
  // Otherwise, look at their most recent fights
  // This is a simplified approach - you might want to look at the most common weight class
  for (let i = fights.length - 1; i >= 0; i--) {
    const fight = fights[i];
    if (fight.bout) {
      const weightClass = extractWeightClassFromBout(fight.bout);
      if (weightClass) {
        return weightClass;
      }
    }
  }
  
  // Default
  return 'Men_Middleweight';
}

/**
 * Map database weight class string to WeightClass enum
 */
function mapWeightClass(dbWeightClass: string): WeightClass {
  const normalized = dbWeightClass.toLowerCase();
  
  // Men's divisions
  if (normalized.includes('flyweight') && !normalized.includes('women')) return 'Men_Flyweight';
  if (normalized.includes('bantamweight') && !normalized.includes('women')) return 'Men_Bantamweight';
  if (normalized.includes('featherweight') && !normalized.includes('women')) return 'Men_Featherweight';
  if (normalized.includes('lightweight') && !normalized.includes('light heavyweight')) return 'Men_Lightweight';
  if (normalized.includes('welterweight')) return 'Men_Welterweight';
  if (normalized.includes('middleweight')) return 'Men_Middleweight';
  if (normalized.includes('light heavyweight')) return 'Men_LightHeavyweight';
  if (normalized.includes('heavyweight') && !normalized.includes('light')) return 'Men_Heavyweight';
  
  // Women's divisions
  if (normalized.includes('strawweight')) return 'Women_Strawweight';
  if (normalized.includes('flyweight') && normalized.includes('women')) return 'Women_Flyweight';
  if (normalized.includes('bantamweight') && normalized.includes('women')) return 'Women_Bantamweight';
  if (normalized.includes('featherweight') && normalized.includes('women')) return 'Women_Featherweight';
  
  return 'Men_Middleweight'; // Default
}

/**
 * Extract weight class from bout description
 */
function extractWeightClassFromBout(bout: string): WeightClass | null {
  const normalized = bout.toLowerCase();
  
  // Look for weight class mentions in the bout string
  if (normalized.includes('flyweight')) {
    if (normalized.includes('women')) return 'Women_Flyweight';
    return 'Men_Flyweight';
  }
  if (normalized.includes('bantamweight')) {
    if (normalized.includes('women')) return 'Women_Bantamweight';
    return 'Men_Bantamweight';
  }
  if (normalized.includes('featherweight')) {
    if (normalized.includes('women')) return 'Women_Featherweight';
    return 'Men_Featherweight';
  }
  if (normalized.includes('lightweight') && !normalized.includes('light heavyweight')) {
    return 'Men_Lightweight';
  }
  if (normalized.includes('welterweight')) {
    return 'Men_Welterweight';
  }
  if (normalized.includes('middleweight')) {
    return 'Men_Middleweight';
  }
  if (normalized.includes('light heavyweight')) {
    return 'Men_LightHeavyweight';
  }
  if (normalized.includes('heavyweight') && !normalized.includes('light')) {
    return 'Men_Heavyweight';
  }
  if (normalized.includes('strawweight')) {
    return 'Women_Strawweight';
  }
  
  return null;
}