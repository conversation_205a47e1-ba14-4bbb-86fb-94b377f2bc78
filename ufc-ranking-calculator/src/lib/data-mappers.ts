/**
 * Data mapping utilities for UFC fight statistics
 */

/**
 * Parse control time string (e.g., "3:45") to seconds
 */
export function parseControlTime(timeStr: string | null): number {
  if (!timeStr || timeStr === '--' || timeStr === '') {
    return 0;
  }
  
  const parts = timeStr.split(':');
  if (parts.length !== 2) {
    return 0;
  }
  
  const minutes = parseInt(parts[0], 10) || 0;
  const seconds = parseInt(parts[1], 10) || 0;
  
  return minutes * 60 + seconds;
}

/**
 * Calculate total fight time in seconds from round and time
 */
export function calculateTotalFightTime(round: number, time: string): number {
  if (!round || !time) {
    return 0;
  }
  
  // Each round is 5 minutes (300 seconds)
  const completedRounds = (round - 1) * 300;
  
  // Parse the time in the current round
  const currentRoundTime = parseControlTime(time);
  
  return completedRounds + currentRoundTime;
}

/**
 * Format seconds to MM:SS format
 */
export function formatTime(seconds: number): string {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

/**
 * Parse percentage string to number
 */
export function parsePercentage(percentStr: string | null): number {
  if (!percentStr || percentStr === '--' || percentStr === '') {
    return 0;
  }
  
  const cleaned = percentStr.replace('%', '');
  return parseFloat(cleaned) || 0;
}

/**
 * Parse numeric string with fallback
 */
export function parseNumeric(numStr: string | null): number {
  if (!numStr || numStr === '--' || numStr === '') {
    return 0;
  }
  
  return parseFloat(numStr) || 0;
}
