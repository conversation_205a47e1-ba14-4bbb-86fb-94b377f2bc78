/**
 * Division-specific data adapter
 * Provides clean interfaces for working with division-separated fighter data
 */

import { query, queryOne } from './database';
import type { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>erDivisionRecord, 
  DbFighterDivisionMetrics, 
  FighterDivisionData 
} from './database';

// Type for joined query results
interface JoinedDivisionResult extends Db<PERSON>ighter {
  // Record fields
  record_id: number;
  wins: number;
  losses: number;
  draws: number;
  no_contests: number;
  total_fights: number;
  ko_tko_wins: number;
  submission_wins: number;
  decision_wins: number;
  ko_tko_losses: number;
  submission_losses: number;
  decision_losses: number;
  win_percentage: number;
  finish_rate: number;
  finish_defense_rate: number;
  first_fight_date: string;
  last_fight_date: string;
  record_created_at: string;
  record_updated_at: string;
  
  // Metrics fields
  metrics_id: number;
  total_significant_strikes_landed: number;
  total_significant_strikes_attempted: number;
  total_significant_strikes_absorbed: number;
  striking_accuracy: number;
  striking_defense_percentage: number;
  strikes_landed_per_minute: number;
  strikes_absorbed_per_minute: number;
  total_takedowns_landed: number;
  total_takedowns_attempted: number;
  total_takedowns_defended: number;
  takedown_accuracy: number;
  takedown_defense_percentage: number;
  takedowns_per_15min: number;
  total_submission_attempts: number;
  total_submission_defenses: number;
  submissions_per_15min: number;
  total_control_time_seconds: number;
  control_time_per_fight: number;
  total_knockdowns_scored: number;
  total_knockdowns_absorbed: number;
  knockdown_rate: number;
  total_fight_time_seconds: number;
  average_fight_time_seconds: number;
  metrics_created_at: string;
  metrics_updated_at: string;
}

/**
 * Get fighter's record and metrics for a specific division
 */
export function getFighterDivisionData(fighterId: number, division: string): FighterDivisionData | null {
  const result = queryOne<JoinedDivisionResult>(`
    SELECT 
      f.*,
      fdr.id as record_id,
      fdr.wins, fdr.losses, fdr.draws, fdr.no_contests, fdr.total_fights,
      fdr.ko_tko_wins, fdr.submission_wins, fdr.decision_wins,
      fdr.ko_tko_losses, fdr.submission_losses, fdr.decision_losses,
      fdr.win_percentage, fdr.finish_rate, fdr.finish_defense_rate,
      fdr.first_fight_date, fdr.last_fight_date,
      fdr.created_at as record_created_at, fdr.updated_at as record_updated_at,
      
      fdm.id as metrics_id,
      fdm.total_significant_strikes_landed, fdm.total_significant_strikes_attempted,
      fdm.total_significant_strikes_absorbed, fdm.striking_accuracy, fdm.striking_defense_percentage,
      fdm.strikes_landed_per_minute, fdm.strikes_absorbed_per_minute,
      fdm.total_takedowns_landed, fdm.total_takedowns_attempted, fdm.total_takedowns_defended,
      fdm.takedown_accuracy, fdm.takedown_defense_percentage, fdm.takedowns_per_15min,
      fdm.total_submission_attempts, fdm.total_submission_defenses, fdm.submissions_per_15min,
      fdm.total_control_time_seconds, fdm.control_time_per_fight,
      fdm.total_knockdowns_scored, fdm.total_knockdowns_absorbed, fdm.knockdown_rate,
      fdm.total_fight_time_seconds, fdm.average_fight_time_seconds,
      fdm.created_at as metrics_created_at, fdm.updated_at as metrics_updated_at
      
    FROM fighters f
    JOIN fighter_division_records fdr ON f.id = fdr.fighter_id
    JOIN fighter_division_metrics fdm ON f.id = fdm.fighter_id AND fdr.weight_class = fdm.weight_class
    WHERE f.id = ? AND fdr.weight_class = ?
  `, [fighterId, division]);

  if (!result) return null;

  const fighter: DbFighter = {
    id: result.id,
    first_name: result.first_name,
    last_name: result.last_name,
    nickname: result.nickname,
    url: result.url,
    created_at: result.created_at,
    birthdate: result.birthdate,
    weight_class: result.weight_class,
    height: result.height,
    weight: result.weight,
    reach: result.reach,
    stance: result.stance,
  };

  const division_record: DbFighterDivisionRecord = {
    id: result.record_id,
    fighter_id: result.id,
    weight_class: division,
    wins: result.wins,
    losses: result.losses,
    draws: result.draws,
    no_contests: result.no_contests,
    total_fights: result.total_fights,
    ko_tko_wins: result.ko_tko_wins,
    submission_wins: result.submission_wins,
    decision_wins: result.decision_wins,
    ko_tko_losses: result.ko_tko_losses,
    submission_losses: result.submission_losses,
    decision_losses: result.decision_losses,
    win_percentage: result.win_percentage,
    finish_rate: result.finish_rate,
    finish_defense_rate: result.finish_defense_rate,
    first_fight_date: result.first_fight_date,
    last_fight_date: result.last_fight_date,
    created_at: result.record_created_at,
    updated_at: result.record_updated_at,
  };

  const division_metrics: DbFighterDivisionMetrics = {
    id: result.metrics_id,
    fighter_id: result.id,
    weight_class: division,
    total_significant_strikes_landed: result.total_significant_strikes_landed,
    total_significant_strikes_attempted: result.total_significant_strikes_attempted,
    total_significant_strikes_absorbed: result.total_significant_strikes_absorbed,
    striking_accuracy: result.striking_accuracy,
    striking_defense_percentage: result.striking_defense_percentage,
    strikes_landed_per_minute: result.strikes_landed_per_minute,
    strikes_absorbed_per_minute: result.strikes_absorbed_per_minute,
    total_takedowns_landed: result.total_takedowns_landed,
    total_takedowns_attempted: result.total_takedowns_attempted,
    total_takedowns_defended: result.total_takedowns_defended,
    takedown_accuracy: result.takedown_accuracy,
    takedown_defense_percentage: result.takedown_defense_percentage,
    takedowns_per_15min: result.takedowns_per_15min,
    total_submission_attempts: result.total_submission_attempts,
    total_submission_defenses: result.total_submission_defenses,
    submissions_per_15min: result.submissions_per_15min,
    total_control_time_seconds: result.total_control_time_seconds,
    control_time_per_fight: result.control_time_per_fight,
    total_knockdowns_scored: result.total_knockdowns_scored,
    total_knockdowns_absorbed: result.total_knockdowns_absorbed,
    knockdown_rate: result.knockdown_rate,
    total_fight_time_seconds: result.total_fight_time_seconds,
    average_fight_time_seconds: result.average_fight_time_seconds,
    created_at: result.metrics_created_at,
    updated_at: result.metrics_updated_at,
  };

  return {
    ...fighter,
    division_record,
    division_metrics,
  };
}

/**
 * Get all divisions a fighter has competed in
 */
export function getFighterDivisions(fighterId: number): string[] {
  const divisions = query<{ weight_class: string }>(`
    SELECT DISTINCT weight_class 
    FROM fighter_division_records 
    WHERE fighter_id = ?
    ORDER BY total_fights DESC
  `, [fighterId]);

  return divisions.map(d => d.weight_class);
}

/**
 * Get top fighters in a division by various metrics
 */
export function getDivisionLeaders(division: string, metric: string = 'wins', limit: number = 10): FighterDivisionData[] {
  let orderBy = 'fdr.wins DESC, fdr.win_percentage DESC';
  
  switch (metric) {
    case 'win_percentage':
      orderBy = 'fdr.win_percentage DESC, fdr.total_fights DESC';
      break;
    case 'finish_rate':
      orderBy = 'fdr.finish_rate DESC, fdr.wins DESC';
      break;
    case 'striking_accuracy':
      orderBy = 'fdm.striking_accuracy DESC, fdm.total_significant_strikes_attempted DESC';
      break;
    case 'takedown_accuracy':
      orderBy = 'fdm.takedown_accuracy DESC, fdm.total_takedowns_attempted DESC';
      break;
    case 'activity':
      orderBy = 'fdr.last_fight_date DESC, fdr.total_fights DESC';
      break;
  }

  const results = query<JoinedDivisionResult>(`
    SELECT 
      f.*,
      fdr.*, 
      fdm.*
    FROM fighters f
    JOIN fighter_division_records fdr ON f.id = fdr.fighter_id
    JOIN fighter_division_metrics fdm ON f.id = fdm.fighter_id AND fdr.weight_class = fdm.weight_class
    WHERE fdr.weight_class = ? AND fdr.total_fights >= 3
    ORDER BY ${orderBy}
    LIMIT ?
  `, [division, limit]);

  return results.map((result) => {
    const fighter: DbFighter = {
      id: result.id,
      first_name: result.first_name,
      last_name: result.last_name,
      nickname: result.nickname,
      url: result.url,
      created_at: result.created_at,
      birthdate: result.birthdate,
      weight_class: result.weight_class,
      height: result.height,
      weight: result.weight,
      reach: result.reach,
      stance: result.stance,
    };

    const division_record: DbFighterDivisionRecord = {
      id: result.record_id,
      fighter_id: result.id,
      weight_class: division,
      wins: result.wins,
      losses: result.losses,
      draws: result.draws,
      no_contests: result.no_contests,
      total_fights: result.total_fights,
      ko_tko_wins: result.ko_tko_wins,
      submission_wins: result.submission_wins,
      decision_wins: result.decision_wins,
      ko_tko_losses: result.ko_tko_losses,
      submission_losses: result.submission_losses,
      decision_losses: result.decision_losses,
      win_percentage: result.win_percentage,
      finish_rate: result.finish_rate,
      finish_defense_rate: result.finish_defense_rate,
      first_fight_date: result.first_fight_date,
      last_fight_date: result.last_fight_date,
      created_at: result.record_created_at,
      updated_at: result.record_updated_at,
    };

    const division_metrics: DbFighterDivisionMetrics = {
      id: result.metrics_id,
      fighter_id: result.id,
      weight_class: division,
      total_significant_strikes_landed: result.total_significant_strikes_landed,
      total_significant_strikes_attempted: result.total_significant_strikes_attempted,
      total_significant_strikes_absorbed: result.total_significant_strikes_absorbed,
      striking_accuracy: result.striking_accuracy,
      striking_defense_percentage: result.striking_defense_percentage,
      strikes_landed_per_minute: result.strikes_landed_per_minute,
      strikes_absorbed_per_minute: result.strikes_absorbed_per_minute,
      total_takedowns_landed: result.total_takedowns_landed,
      total_takedowns_attempted: result.total_takedowns_attempted,
      total_takedowns_defended: result.total_takedowns_defended,
      takedown_accuracy: result.takedown_accuracy,
      takedown_defense_percentage: result.takedown_defense_percentage,
      takedowns_per_15min: result.takedowns_per_15min,
      total_submission_attempts: result.total_submission_attempts,
      total_submission_defenses: result.total_submission_defenses,
      submissions_per_15min: result.submissions_per_15min,
      total_control_time_seconds: result.total_control_time_seconds,
      control_time_per_fight: result.control_time_per_fight,
      total_knockdowns_scored: result.total_knockdowns_scored,
      total_knockdowns_absorbed: result.total_knockdowns_absorbed,
      knockdown_rate: result.knockdown_rate,
      total_fight_time_seconds: result.total_fight_time_seconds,
      average_fight_time_seconds: result.average_fight_time_seconds,
      created_at: result.metrics_created_at,
      updated_at: result.metrics_updated_at,
    };

    return {
      ...fighter,
      division_record,
      division_metrics,
    };
  });
}

/**
 * Compare fighter's performance across different divisions
 */
export function compareFighterAcrossDivisions(fighterId: number): FighterDivisionData[] {
  const results = query<JoinedDivisionResult>(`
    SELECT 
      f.*,
      fdr.id as record_id,
      fdr.wins, fdr.losses, fdr.draws, fdr.no_contests, fdr.total_fights,
      fdr.ko_tko_wins, fdr.submission_wins, fdr.decision_wins,
      fdr.ko_tko_losses, fdr.submission_losses, fdr.decision_losses,
      fdr.win_percentage, fdr.finish_rate, fdr.finish_defense_rate,
      fdr.first_fight_date, fdr.last_fight_date,
      fdr.created_at as record_created_at, fdr.updated_at as record_updated_at,
      
      fdm.id as metrics_id,
      fdm.total_significant_strikes_landed, fdm.total_significant_strikes_attempted,
      fdm.total_significant_strikes_absorbed, fdm.striking_accuracy, fdm.striking_defense_percentage,
      fdm.strikes_landed_per_minute, fdm.strikes_absorbed_per_minute,
      fdm.total_takedowns_landed, fdm.total_takedowns_attempted, fdm.total_takedowns_defended,
      fdm.takedown_accuracy, fdm.takedown_defense_percentage, fdm.takedowns_per_15min,
      fdm.total_submission_attempts, fdm.total_submission_defenses, fdm.submissions_per_15min,
      fdm.total_control_time_seconds, fdm.control_time_per_fight,
      fdm.total_knockdowns_scored, fdm.total_knockdowns_absorbed, fdm.knockdown_rate,
      fdm.total_fight_time_seconds, fdm.average_fight_time_seconds,
      fdm.created_at as metrics_created_at, fdm.updated_at as metrics_updated_at
    FROM fighters f
    JOIN fighter_division_records fdr ON f.id = fdr.fighter_id
    JOIN fighter_division_metrics fdm ON f.id = fdm.fighter_id AND fdr.weight_class = fdm.weight_class
    WHERE f.id = ?
    ORDER BY fdr.total_fights DESC
  `, [fighterId]);

  return results.map((result) => {
    const fighter: DbFighter = {
      id: result.id,
      first_name: result.first_name,
      last_name: result.last_name,
      nickname: result.nickname,
      url: result.url,
      created_at: result.created_at,
      birthdate: result.birthdate,
      weight_class: result.weight_class,
      height: result.height,
      weight: result.weight,
      reach: result.reach,
      stance: result.stance,
    };

    // Since the query uses fdr.* and fdm.*, we need to extract the fields properly
    const division_record: DbFighterDivisionRecord = {
      id: result.record_id,
      fighter_id: fighterId,
      weight_class: result.weight_class!, // This comes from fdr.weight_class (non-null from JOIN)
      wins: result.wins,
      losses: result.losses,
      draws: result.draws,
      no_contests: result.no_contests,
      total_fights: result.total_fights,
      ko_tko_wins: result.ko_tko_wins,
      submission_wins: result.submission_wins,
      decision_wins: result.decision_wins,
      ko_tko_losses: result.ko_tko_losses,
      submission_losses: result.submission_losses,
      decision_losses: result.decision_losses,
      win_percentage: result.win_percentage,
      finish_rate: result.finish_rate,
      finish_defense_rate: result.finish_defense_rate,
      first_fight_date: result.first_fight_date,
      last_fight_date: result.last_fight_date,
      created_at: result.record_created_at,
      updated_at: result.record_updated_at,
    };

    const division_metrics: DbFighterDivisionMetrics = {
      id: result.metrics_id,
      fighter_id: fighterId,
      weight_class: result.weight_class!, // Non-null from JOIN
      total_significant_strikes_landed: result.total_significant_strikes_landed,
      total_significant_strikes_attempted: result.total_significant_strikes_attempted,
      total_significant_strikes_absorbed: result.total_significant_strikes_absorbed,
      striking_accuracy: result.striking_accuracy,
      striking_defense_percentage: result.striking_defense_percentage,
      strikes_landed_per_minute: result.strikes_landed_per_minute,
      strikes_absorbed_per_minute: result.strikes_absorbed_per_minute,
      total_takedowns_landed: result.total_takedowns_landed,
      total_takedowns_attempted: result.total_takedowns_attempted,
      total_takedowns_defended: result.total_takedowns_defended,
      takedown_accuracy: result.takedown_accuracy,
      takedown_defense_percentage: result.takedown_defense_percentage,
      takedowns_per_15min: result.takedowns_per_15min,
      total_submission_attempts: result.total_submission_attempts,
      total_submission_defenses: result.total_submission_defenses,
      submissions_per_15min: result.submissions_per_15min,
      total_control_time_seconds: result.total_control_time_seconds,
      control_time_per_fight: result.control_time_per_fight,
      total_knockdowns_scored: result.total_knockdowns_scored,
      total_knockdowns_absorbed: result.total_knockdowns_absorbed,
      knockdown_rate: result.knockdown_rate,
      total_fight_time_seconds: result.total_fight_time_seconds,
      average_fight_time_seconds: result.average_fight_time_seconds,
      created_at: result.metrics_created_at,
      updated_at: result.metrics_updated_at,
    };

    return {
      ...fighter,
      division_record,
      division_metrics,
    };
  });
}

/**
 * Get division statistics summary
 */
export function getDivisionStats(division: string): {
    total_fighters: number;
    total_fights: number;
    avg_win_percentage: number;
    avg_finish_rate: number;
    avg_striking_accuracy: number;
    most_active_fighter: string;
    last_activity: string;
  } | null {
  return queryOne<{
    total_fighters: number;
    total_fights: number;
    avg_win_percentage: number;
    avg_finish_rate: number;
    avg_striking_accuracy: number;
    most_active_fighter: string;
    last_activity: string;
  }>(`
    SELECT 
      COUNT(*) as total_fighters,
      SUM(fdr.total_fights) as total_fights,
      AVG(fdr.win_percentage) as avg_win_percentage,
      AVG(fdr.finish_rate) as avg_finish_rate,
      AVG(fdm.striking_accuracy) as avg_striking_accuracy,
      (SELECT f.first_name || ' ' || f.last_name 
       FROM fighters f 
       JOIN fighter_division_records fdr2 ON f.id = fdr2.fighter_id 
       WHERE fdr2.weight_class = ? 
       ORDER BY fdr2.total_fights DESC 
       LIMIT 1) as most_active_fighter,
      MAX(fdr.last_fight_date) as last_activity
    FROM fighter_division_records fdr
    JOIN fighter_division_metrics fdm ON fdr.fighter_id = fdm.fighter_id AND fdr.weight_class = fdm.weight_class
    WHERE fdr.weight_class = ?
  `, [division, division]);
}