/**
 * Database connection and query utilities for UFC data
 */

import Database from "better-sqlite3";
import path from "path";

// Database path
const DB_PATH = path.join(process.cwd(), "data", "ufc_data.db");

let db: Database.Database | null = null;

/**
 * Get database connection (singleton)
 */
export function getDatabase(): Database.Database {
  if (!db) {
    try {
      db = new Database(DB_PATH);
      db.pragma("journal_mode = WAL");
      db.pragma("synchronous = NORMAL");
      db.pragma("cache_size = 1000000");
      db.pragma("temp_store = memory");
    } catch (error) {
      console.error("Failed to connect to database:", error);
      throw new Error("Database connection failed");
    }
  }
  return db;
}

/**
 * Close database connection
 */
export function closeDatabase(): void {
  if (db) {
    db.close();
    db = null;
  }
}

/**
 * Execute a query and return results
 */
export function query<T = unknown>(sql: string, params: unknown[] = []): T[] {
  const database = getDatabase();
  try {
    const stmt = database.prepare(sql);
    return stmt.all(...params) as T[];
  } catch (error) {
    console.error("Database query error:", error);
    throw error;
  }
}

/**
 * Execute a query and return single result
 */
export function queryOne<T = unknown>(
  sql: string,
  params: unknown[] = []
): T | null {
  const results = query<T>(sql, params);
  return results.length > 0 ? results[0] : null;
}

/**
 * Execute a mutation (INSERT, UPDATE, DELETE)
 */
export function execute(
  sql: string,
  params: unknown[] = []
): Database.RunResult {
  const database = getDatabase();
  try {
    const stmt = database.prepare(sql);
    return stmt.run(...params);
  } catch (error) {
    console.error("Database execution error:", error);
    throw error;
  }
}

// Database interfaces for TypeScript
export interface DbEvent {
  id: number;
  event_name: string;
  url: string;
  date: string;
  location: string;
  created_at: string;
}

export interface DbFighter {
  id: number;
  first_name: string;
  last_name: string;
  nickname: string | null;
  url: string;
  created_at: string;
  // Biographical data
  birthdate: string | null;
  weight_class: string | null;
  height: string | null;
  weight: string | null;
  reach: string | null;
  stance: string | null;
}

// Calculated fighter statistics interface
export interface CalculatedFighterStats {
  // Career record
  wins: number;
  losses: number;
  draws: number;
  no_contests: number;
  total_fights: number;
  ko_tko_wins: number;
  submission_wins: number;
  decision_wins: number;
  // Fight statistics
  total_significant_strikes_landed: number;
  total_significant_strikes_attempted: number;
  total_takedowns_landed: number;
  total_takedowns_attempted: number;
  total_knockdowns: number;
  total_submission_attempts: number;
  total_control_time_seconds: number;
  striking_accuracy: number;
  takedown_accuracy: number;
  finish_rate: number;
  avg_fight_time_seconds: number;
  total_significant_strikes_absorbed: number;
  strikes_absorbed_per_minute: number;
  striking_defense_percentage: number;
  total_fight_time_seconds: number;
  takedowns_average_per_15min: number;
  submissions_average_per_15min: number;
}

// Combined fighter data with calculated stats
export interface FighterWithStats extends DbFighter, CalculatedFighterStats {}

export interface DbFight {
  id: number;
  event_id: number;
  bout: string;
  url: string;
  fighter1_id: number | null;
  fighter2_id: number | null;
  winner_id: number | null;
  result_method: string | null;
  result_round: number | null;
  result_time: string | null;
  weight_class: string | null;
  created_at: string;
  fight_status: 'valid' | 'excluded_nc' | 'exhibition' | 'overturned';
  nc_reason: string | null;
}

export interface DbFightStats {
  id: number;
  fight_id: number;
  fighter_id: number;
  round_number: number;
  knockdowns: number;
  sig_strikes_landed: number;
  sig_strikes_attempted: number;
  sig_strikes_pct: number | null;
  total_strikes_landed: number;
  total_strikes_attempted: number;
  takedowns_landed: number;
  takedowns_attempted: number;
  takedowns_pct: number | null;
  submission_attempts: number;
  reversals: number;
  control_time: string | null;
  // Strike location fields
  head_strikes_landed: number;
  head_strikes_attempted: number;
  body_strikes_landed: number;
  body_strikes_attempted: number;
  leg_strikes_landed: number;
  leg_strikes_attempted: number;
  // Strike position fields
  distance_strikes_landed: number;
  distance_strikes_attempted: number;
  clinch_strikes_landed: number;
  clinch_strikes_attempted: number;
  ground_strikes_landed: number;
  ground_strikes_attempted: number;
}

// Division-specific record interfaces
export interface DbFighterDivisionRecord {
  id: number;
  fighter_id: number;
  weight_class: string;
  // Fight record
  wins: number;
  losses: number;
  draws: number;
  no_contests: number;
  total_fights: number;
  // Win/loss breakdown
  ko_tko_wins: number;
  submission_wins: number;
  decision_wins: number;
  ko_tko_losses: number;
  submission_losses: number;
  decision_losses: number;
  // Performance metrics
  win_percentage: number;
  finish_rate: number;
  finish_defense_rate: number;
  // Activity tracking
  first_fight_date: string | null;
  last_fight_date: string | null;
  created_at: string;
  updated_at: string;
}

export interface DbFighterDivisionMetrics {
  id: number;
  fighter_id: number;
  weight_class: string;
  // Striking metrics
  total_significant_strikes_landed: number;
  total_significant_strikes_attempted: number;
  total_significant_strikes_absorbed: number;
  striking_accuracy: number;
  striking_defense_percentage: number;
  strikes_landed_per_minute: number;
  strikes_absorbed_per_minute: number;
  // Takedown metrics
  total_takedowns_landed: number;
  total_takedowns_attempted: number;
  total_takedowns_defended: number;
  takedown_accuracy: number;
  takedown_defense_percentage: number;
  takedowns_per_15min: number;
  // Grappling metrics
  total_submission_attempts: number;
  total_submission_defenses: number;
  submissions_per_15min: number;
  total_control_time_seconds: number;
  control_time_per_fight: number;
  // Power metrics
  total_knockdowns_scored: number;
  total_knockdowns_absorbed: number;
  knockdown_rate: number;
  // Fight time metrics
  total_fight_time_seconds: number;
  average_fight_time_seconds: number;
  created_at: string;
  updated_at: string;
}

// Combined division fighter data
export interface FighterDivisionData extends DbFighter {
  division_record: DbFighterDivisionRecord;
  division_metrics: DbFighterDivisionMetrics;
}
