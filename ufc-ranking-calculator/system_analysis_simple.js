const fs = require('fs');
const path = require('path');

/**
 * Simple System Analysis - No Database Dependencies
 * 
 * Analyzes the codebase structure and implementation status
 * without requiring database connections that might hang
 */

console.log('🔍 UFC WHR System Analysis - Independent Assessment');
console.log('═'.repeat(70));

// Check database existence
const dbPath = path.join(__dirname, 'data', 'ufc_data.db');
const dbExists = fs.existsSync(dbPath);
const dbSize = dbExists ? fs.statSync(dbPath).size : 0;

console.log('\n📊 DATABASE STATUS:');
console.log(`   Database exists: ${dbExists ? '✅' : '❌'}`);
if (dbExists) {
    console.log(`   Database size: ${(dbSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   Last modified: ${fs.statSync(dbPath).mtime.toISOString()}`);
}

// Check core WHR implementation files
console.log('\n🔧 CORE WHR IMPLEMENTATION:');

const coreFiles = [
    'scripts/whr-unified-algorithm.js',
    'scripts/whr-main-algorithm.js',
    'scripts/temporal-accuracy-infrastructure.js',
    'scripts/iterative-convergence-system.js',
    'scripts/division-specific-base-parameters.js'
];

let implementedCore = 0;
coreFiles.forEach(file => {
    const exists = fs.existsSync(file);
    const size = exists ? fs.statSync(file).size : 0;
    console.log(`   ${exists ? '✅' : '❌'} ${file} ${exists ? `(${(size/1024).toFixed(1)}KB)` : ''}`);
    if (exists) implementedCore++;
});

// Check philosophy compliance components
console.log('\n🎯 PHILOSOPHY COMPLIANCE COMPONENTS:');

const philosophyFiles = [
    'scripts/division-specific-age-curves.js',
    'scripts/division-specific-finishing-impact.js', 
    'scripts/division-specific-statistical-weights.js',
    'scripts/time-decay-optimization.js',
    'scripts/data-driven-parameters.js'
];

let implementedPhilosophy = 0;
philosophyFiles.forEach(file => {
    const exists = fs.existsSync(file);
    const size = exists ? fs.statSync(file).size : 0;
    console.log(`   ${exists ? '✅' : '❌'} ${file} ${exists ? `(${(size/1024).toFixed(1)}KB)` : ''}`);
    if (exists) implementedPhilosophy++;
});

// Check testing and validation
console.log('\n🧪 TESTING & VALIDATION:');

const testFiles = [
    'scripts/testing/whr-unit-tests.js',
    'scripts/testing/whr-integration-tests.js',
    'scripts/testing/whr-validation-framework.js',
    'scripts/testing/comprehensive-system-audit.js'
];

let implementedTests = 0;
testFiles.forEach(file => {
    const exists = fs.existsSync(file);
    const size = exists ? fs.statSync(file).size : 0;
    console.log(`   ${exists ? '✅' : '❌'} ${file} ${exists ? `(${(size/1024).toFixed(1)}KB)` : ''}`);
    if (exists) implementedTests++;
});

// Analyze code quality by examining key files
console.log('\n💻 CODE QUALITY ANALYSIS:');

const keyAnalysisFiles = [
    'scripts/whr-unified-algorithm.js',
    'scripts/division-specific-base-parameters.js',
    'scripts/temporal-accuracy-infrastructure.js'
];

let hardcodedIssues = [];
let philosophyCompliance = [];

keyAnalysisFiles.forEach(file => {
    if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8');
        
        // Check for hardcoded values
        const hardcodedPatterns = [
            /rating.*:\s*\d{4}/g,  // Hardcoded ratings like 1500
            /k_factor.*:\s*\d+/g,  // Hardcoded K-factors
            /initial.*:\s*\d{4}/g  // Hardcoded initial values
        ];
        
        hardcodedPatterns.forEach(pattern => {
            const matches = content.match(pattern);
            if (matches) {
                hardcodedIssues.push(`${file}: ${matches.slice(0, 3).join(', ')}`);
            }
        });
        
        // Check for philosophy compliance indicators
        const philosophyIndicators = [
            { pattern: /division.*specific/gi, aspect: 'Division-Centric' },
            { pattern: /temporal.*accuracy/gi, aspect: 'Temporal Accuracy' },
            { pattern: /strength.*schedule/gi, aspect: 'Strength of Schedule' },
            { pattern: /age.*curve/gi, aspect: 'Age Trajectory' },
            { pattern: /data.*driven/gi, aspect: 'Data-Driven' }
        ];
        
        philosophyIndicators.forEach(indicator => {
            if (content.match(indicator.pattern)) {
                philosophyCompliance.push(`${file}: ${indicator.aspect}`);
            }
        });
    }
});

if (hardcodedIssues.length > 0) {
    console.log('   ⚠️  Potential hardcoded parameters found:');
    hardcodedIssues.slice(0, 5).forEach(issue => console.log(`      ${issue}`));
    if (hardcodedIssues.length > 5) {
        console.log(`      ... and ${hardcodedIssues.length - 5} more`);
    }
} else {
    console.log('   ✅ No obvious hardcoded parameters detected');
}

// Check package.json and dependencies
console.log('\n📦 DEPENDENCIES:');
if (fs.existsSync('package.json')) {
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const deps = pkg.dependencies || {};
    
    const criticalDeps = ['better-sqlite3', 'next'];
    criticalDeps.forEach(dep => {
        console.log(`   ${deps[dep] ? '✅' : '❌'} ${dep}: ${deps[dep] || 'not found'}`);
    });
    
    console.log(`   📊 Total dependencies: ${Object.keys(deps).length}`);
} else {
    console.log('   ❌ package.json not found');
}

// Check source code structure
console.log('\n📁 SOURCE CODE STRUCTURE:');
const srcExists = fs.existsSync('src');
if (srcExists) {
    const srcFiles = fs.readdirSync('src', { recursive: true }).filter(f => f.endsWith('.ts') || f.endsWith('.js'));
    console.log(`   ✅ src/ directory: ${srcFiles.length} files`);
    
    // Check for key source files
    const keySourceFiles = [
        'src/lib/fighter-data-adapter.ts',
        'src/config/finish-multipliers.ts',
        'src/components/rankings-display.tsx'
    ];
    
    keySourceFiles.forEach(file => {
        const exists = fs.existsSync(file);
        console.log(`   ${exists ? '✅' : '❌'} ${file}`);
    });
} else {
    console.log('   ❌ src/ directory not found');
}

// Generate overall assessment
console.log('\n🏆 OVERALL ASSESSMENT:');
console.log('─'.repeat(50));

const coreImplementation = (implementedCore / coreFiles.length * 100).toFixed(1);
const philosophyImplementation = (implementedPhilosophy / philosophyFiles.length * 100).toFixed(1);
const testingImplementation = (implementedTests / testFiles.length * 100).toFixed(1);

console.log(`   Core WHR Implementation: ${coreImplementation}% (${implementedCore}/${coreFiles.length})`);
console.log(`   Philosophy Compliance: ${philosophyImplementation}% (${implementedPhilosophy}/${philosophyFiles.length})`);
console.log(`   Testing Framework: ${testingImplementation}% (${implementedTests}/${testFiles.length})`);

const overallScore = (parseFloat(coreImplementation) + parseFloat(philosophyImplementation) + parseFloat(testingImplementation)) / 3;

console.log(`\n   📊 Overall Implementation: ${overallScore.toFixed(1)}%`);

if (overallScore >= 80) {
    console.log('   🎉 STATUS: PRODUCTION READY');
} else if (overallScore >= 60) {
    console.log('   ⚠️  STATUS: NEEDS IMPROVEMENTS');
} else {
    console.log('   ❌ STATUS: MAJOR WORK REQUIRED');
}

// Philosophy compliance summary
if (philosophyCompliance.length > 0) {
    console.log('\n✅ PHILOSOPHY COMPLIANCE DETECTED:');
    const uniqueAspects = [...new Set(philosophyCompliance.map(p => p.split(': ')[1]))];
    uniqueAspects.forEach(aspect => console.log(`   - ${aspect}`));
}

// Critical issues summary
console.log('\n⚠️  CRITICAL AREAS TO REVIEW:');
const issues = [];

if (!dbExists) issues.push('Database not found');
if (implementedCore < coreFiles.length) issues.push('Core WHR components missing');
if (implementedTests < 2) issues.push('Insufficient testing framework');
if (hardcodedIssues.length > 0) issues.push('Hardcoded parameters detected');

if (issues.length === 0) {
    console.log('   ✅ No critical issues detected');
} else {
    issues.forEach(issue => console.log(`   - ${issue}`));
}

console.log('\n═'.repeat(70));
console.log('🔍 Analysis complete - Review results above');
