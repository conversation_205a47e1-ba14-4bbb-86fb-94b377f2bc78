const Database = require("better-sqlite3");
const path = require("path");
const fs = require("fs");

/**
 * Comprehensive WHR System Analysis
 *
 * This script performs an independent analysis of the current system
 * to determine what's actually implemented vs what's claimed in reports
 */

class WHRSystemAnalyzer {
  constructor() {
    this.dbPath = path.join(__dirname, "data", "ufc_data.db");
    this.db = null;
    this.analysis = {
      philosophy_compliance: {},
      implementation_status: {},
      data_quality: {},
      issues: [],
      recommendations: [],
    };

    console.log("🔍 Starting Independent WHR System Analysis...");
    console.log("═".repeat(60));
  }

  async runCompleteAnalysis() {
    try {
      // Initialize database connection
      this.initializeDatabase();

      // Core Philosophy Compliance Analysis
      await this.analyzePhilosophyCompliance();

      // Implementation Status Analysis
      await this.analyzeImplementationStatus();

      // Data Quality Analysis
      await this.analyzeDataQuality();

      // Database Schema Analysis
      await this.analyzeDatabaseSchema();

      // Code Quality Analysis
      await this.analyzeCodeQuality();

      // Generate final report
      this.generateFinalReport();
    } catch (error) {
      console.error("❌ Analysis failed:", error);
    } finally {
      if (this.db) this.db.close();
    }
  }

  initializeDatabase() {
    try {
      if (!fs.existsSync(this.dbPath)) {
        throw new Error(`Database not found at ${this.dbPath}`);
      }

      this.db = new Database(this.dbPath, { readonly: true });
      console.log("✅ Database connection established");

      // Test basic connectivity
      const result = this.db
        .prepare("SELECT COUNT(*) as count FROM sqlite_master")
        .get();
      console.log(`   Database contains ${result.count} tables`);
    } catch (error) {
      console.error("❌ Database initialization failed:", error.message);
      throw error;
    }
  }

  async analyzePhilosophyCompliance() {
    console.log("\n🎯 PHILOSOPHY COMPLIANCE ANALYSIS");
    console.log("─".repeat(40));

    // 1. Division-Centric Approach
    await this.checkDivisionCentricApproach();

    // 2. Pure Merit-Based Evaluation
    await this.checkMeritBasedEvaluation();

    // 3. Strength of Schedule Foundation
    await this.checkStrengthOfSchedule();

    // 4. Data-Driven Parameter Optimization
    await this.checkDataDrivenParameters();

    // 5. Age and Career Trajectory Integration
    await this.checkAgeTrajectoryIntegration();
  }

  async checkDivisionCentricApproach() {
    console.log("\n📊 Division-Centric Approach:");

    try {
      // Check if division-specific parameters exist
      const divisionParams = this.db
        .prepare(
          `
                SELECT name FROM sqlite_master
                WHERE type='table' AND name LIKE '%division%parameter%'
            `
        )
        .all();

      if (divisionParams.length > 0) {
        console.log("   ✅ Division parameter tables found");

        // Check actual parameter data
        const paramData = this.db
          .prepare(
            `
                    SELECT division, COUNT(*) as param_count
                    FROM division_parameters
                    GROUP BY division
                `
          )
          .all();

        console.log(`   📈 Parameters for ${paramData.length} divisions`);
        paramData.forEach((d) => {
          console.log(`      - ${d.division}: ${d.param_count} parameters`);
        });

        this.analysis.philosophy_compliance.division_centric = "IMPLEMENTED";
      } else {
        console.log("   ❌ No division parameter tables found");
        this.analysis.philosophy_compliance.division_centric =
          "NOT_IMPLEMENTED";
        this.analysis.issues.push(
          "Division-specific parameters not implemented"
        );
      }
    } catch (error) {
      console.log("   ❌ Error checking division parameters:", error.message);
      this.analysis.philosophy_compliance.division_centric = "ERROR";
    }
  }

  async checkMeritBasedEvaluation() {
    console.log("\n🏆 Merit-Based Evaluation:");

    try {
      // Check if UFC rankings are referenced in calculations
      const tables = this.db
        .prepare(
          `
                SELECT name FROM sqlite_master WHERE type='table'
            `
        )
        .all();

      const hasUFCRankings = tables.some(
        (t) =>
          t.name.toLowerCase().includes("ufc_rank") ||
          t.name.toLowerCase().includes("official_rank")
      );

      if (!hasUFCRankings) {
        console.log("   ✅ No UFC ranking tables found - pure merit approach");
        this.analysis.philosophy_compliance.merit_based = "IMPLEMENTED";
      } else {
        console.log(
          "   ⚠️  UFC ranking tables detected - need to verify non-usage"
        );
        this.analysis.philosophy_compliance.merit_based = "NEEDS_VERIFICATION";
      }

      // Check for performance-only assessment
      const whrTables = tables.filter((t) => t.name.includes("whr"));
      console.log(`   📊 Found ${whrTables.length} WHR-related tables`);
    } catch (error) {
      console.log(
        "   ❌ Error checking merit-based evaluation:",
        error.message
      );
    }
  }

  async checkStrengthOfSchedule() {
    console.log("\n💪 Strength of Schedule:");

    try {
      // Check for SoS calculations in code
      const sosFiles = [
        "scripts/iterative-convergence-system.js",
        "scripts/whr-unified-algorithm.js",
        "scripts/whr-main-algorithm.js",
      ];

      let sosImplemented = false;

      for (const file of sosFiles) {
        const filePath = path.join(__dirname, file);
        if (fs.existsSync(filePath)) {
          const content = fs.readFileSync(filePath, "utf8");
          if (
            content.includes("sos") ||
            content.includes("strength") ||
            content.includes("schedule")
          ) {
            console.log(`   ✅ SoS logic found in ${file}`);
            sosImplemented = true;
          }
        }
      }

      if (sosImplemented) {
        this.analysis.philosophy_compliance.strength_of_schedule =
          "IMPLEMENTED";
      } else {
        console.log("   ❌ No SoS implementation found");
        this.analysis.philosophy_compliance.strength_of_schedule =
          "NOT_IMPLEMENTED";
      }
    } catch (error) {
      console.log("   ❌ Error checking SoS:", error.message);
    }
  }

  async checkDataDrivenParameters() {
    console.log("\n📈 Data-Driven Parameters:");

    try {
      // Check for hardcoded values in key files
      const keyFiles = [
        "scripts/whr-unified-algorithm.js",
        "scripts/whr-main-algorithm.js",
        "src/config/finish-multipliers.ts",
      ];

      let hardcodedFound = [];

      for (const file of keyFiles) {
        const filePath = path.join(__dirname, file);
        if (fs.existsSync(filePath)) {
          const content = fs.readFileSync(filePath, "utf8");

          // Look for hardcoded numbers
          const hardcodedPatterns = [
            /k_factor:\s*\d+/g,
            /initial_rating:\s*\d+/g,
            /multiplier:\s*\d+\.?\d*/g,
            /rating.*:\s*\d+/g,
          ];

          hardcodedPatterns.forEach((pattern) => {
            const matches = content.match(pattern);
            if (matches) {
              hardcodedFound.push(`${file}: ${matches.join(", ")}`);
            }
          });
        }
      }

      if (hardcodedFound.length > 0) {
        console.log("   ⚠️  Hardcoded parameters found:");
        hardcodedFound.forEach((h) => console.log(`      ${h}`));
        this.analysis.philosophy_compliance.data_driven =
          "PARTIALLY_IMPLEMENTED";
        this.analysis.issues.push("Hardcoded parameters detected");
      } else {
        console.log("   ✅ No obvious hardcoded parameters found");
        this.analysis.philosophy_compliance.data_driven = "IMPLEMENTED";
      }
    } catch (error) {
      console.log(
        "   ❌ Error checking data-driven parameters:",
        error.message
      );
    }
  }

  async checkAgeTrajectoryIntegration() {
    console.log("\n👴 Age Trajectory Integration:");

    try {
      // Check for age-related tables and calculations
      const ageTables = this.db
        .prepare(
          `
                SELECT name FROM sqlite_master
                WHERE type='table' AND name LIKE '%age%'
            `
        )
        .all();

      console.log(`   📊 Found ${ageTables.length} age-related tables`);

      // Check for age curve files
      const ageCurveFile = path.join(
        __dirname,
        "scripts/division-specific-age-curves.js"
      );
      if (fs.existsSync(ageCurveFile)) {
        console.log("   ✅ Age curve implementation file found");

        const content = fs.readFileSync(ageCurveFile, "utf8");
        if (
          content.includes("division") &&
          content.includes("age") &&
          content.includes("curve")
        ) {
          console.log("   ✅ Division-specific age curves implemented");
          this.analysis.philosophy_compliance.age_trajectory = "IMPLEMENTED";
        } else {
          console.log(
            "   ⚠️  Age curve file exists but may not be division-specific"
          );
          this.analysis.philosophy_compliance.age_trajectory =
            "PARTIALLY_IMPLEMENTED";
        }
      } else {
        console.log("   ❌ No age curve implementation found");
        this.analysis.philosophy_compliance.age_trajectory = "NOT_IMPLEMENTED";
      }
    } catch (error) {
      console.log("   ❌ Error checking age trajectory:", error.message);
    }
  }

  async analyzeImplementationStatus() {
    console.log("\n🔧 IMPLEMENTATION STATUS ANALYSIS");
    console.log("─".repeat(40));

    // Check core WHR components
    await this.checkTemporalAccuracy();
    await this.checkIterativeConvergence();
    await this.checkStatisticalWeights();
    await this.checkTimeDecay();
    await this.checkFinishingImpact();
  }

  async checkTemporalAccuracy() {
    console.log("\n⏰ Temporal Accuracy Infrastructure:");

    try {
      const temporalFile = path.join(
        __dirname,
        "scripts/temporal-accuracy-infrastructure.js"
      );
      if (fs.existsSync(temporalFile)) {
        console.log("   ✅ Temporal accuracy file found");

        const content = fs.readFileSync(temporalFile, "utf8");
        if (
          content.includes("temporal") &&
          content.includes("chronological") &&
          content.includes("snapshot")
        ) {
          console.log("   ✅ Temporal accuracy logic implemented");
          this.analysis.implementation_status.temporal_accuracy = "IMPLEMENTED";
        } else {
          console.log("   ⚠️  File exists but implementation unclear");
          this.analysis.implementation_status.temporal_accuracy = "UNCLEAR";
        }
      } else {
        console.log("   ❌ Temporal accuracy infrastructure not found");
        this.analysis.implementation_status.temporal_accuracy =
          "NOT_IMPLEMENTED";
      }
    } catch (error) {
      console.log("   ❌ Error checking temporal accuracy:", error.message);
    }
  }

  async checkIterativeConvergence() {
    console.log("\n🔄 Iterative Convergence System:");

    try {
      const convergenceFile = path.join(
        __dirname,
        "scripts/iterative-convergence-system.js"
      );
      if (fs.existsSync(convergenceFile)) {
        console.log("   ✅ Iterative convergence file found");

        const content = fs.readFileSync(convergenceFile, "utf8");
        const hasIterations =
          content.includes("iteration") || content.includes("convergence");
        const hasDampening =
          content.includes("dampening") || content.includes("dampen");

        if (hasIterations && hasDampening) {
          console.log("   ✅ Full iterative convergence implemented");
          this.analysis.implementation_status.iterative_convergence =
            "IMPLEMENTED";
        } else {
          console.log("   ⚠️  Partial implementation detected");
          this.analysis.implementation_status.iterative_convergence =
            "PARTIALLY_IMPLEMENTED";
        }
      } else {
        console.log("   ❌ Iterative convergence not found");
        this.analysis.implementation_status.iterative_convergence =
          "NOT_IMPLEMENTED";
      }
    } catch (error) {
      console.log("   ❌ Error checking iterative convergence:", error.message);
    }
  }

  async checkStatisticalWeights() {
    console.log("\n📊 Statistical Weights:");

    try {
      const weightsFile = path.join(
        __dirname,
        "scripts/division-specific-statistical-weights.js"
      );
      if (fs.existsSync(weightsFile)) {
        console.log("   ✅ Statistical weights file found");

        // Check for weights table in database
        const weightsTable = this.db
          .prepare(
            `
                    SELECT name FROM sqlite_master
                    WHERE type='table' AND name LIKE '%statistical%weight%'
                `
          )
          .all();

        if (weightsTable.length > 0) {
          console.log("   ✅ Statistical weights table exists");
          this.analysis.implementation_status.statistical_weights =
            "IMPLEMENTED";
        } else {
          console.log("   ⚠️  File exists but no weights table found");
          this.analysis.implementation_status.statistical_weights =
            "PARTIALLY_IMPLEMENTED";
        }
      } else {
        console.log("   ❌ Statistical weights not implemented");
        this.analysis.implementation_status.statistical_weights =
          "NOT_IMPLEMENTED";
      }
    } catch (error) {
      console.log("   ❌ Error checking statistical weights:", error.message);
    }
  }

  async checkTimeDecay() {
    console.log("\n⏳ Time Decay Optimization:");

    try {
      const timeDecayFile = path.join(
        __dirname,
        "scripts/time-decay-optimization.js"
      );
      if (fs.existsSync(timeDecayFile)) {
        console.log("   ✅ Time decay file found");
        this.analysis.implementation_status.time_decay = "IMPLEMENTED";
      } else {
        console.log("   ❌ Time decay optimization not found");
        this.analysis.implementation_status.time_decay = "NOT_IMPLEMENTED";
      }
    } catch (error) {
      console.log("   ❌ Error checking time decay:", error.message);
    }
  }

  async checkFinishingImpact() {
    console.log("\n🥊 Finishing Impact:");

    try {
      const finishFile = path.join(
        __dirname,
        "scripts/division-specific-finishing-impact.js"
      );
      if (fs.existsSync(finishFile)) {
        console.log("   ✅ Finishing impact file found");
        this.analysis.implementation_status.finishing_impact = "IMPLEMENTED";
      } else {
        console.log("   ❌ Finishing impact not implemented");
        this.analysis.implementation_status.finishing_impact =
          "NOT_IMPLEMENTED";
      }
    } catch (error) {
      console.log("   ❌ Error checking finishing impact:", error.message);
    }
  }

  async analyzeDataQuality() {
    console.log("\n📈 DATA QUALITY ANALYSIS");
    console.log("─".repeat(40));

    try {
      // Basic data counts
      const fighterCount = this.db
        .prepare("SELECT COUNT(*) as count FROM fighters")
        .get().count;
      const fightCount = this.db
        .prepare("SELECT COUNT(*) as count FROM fights")
        .get().count;
      const eventCount = this.db
        .prepare("SELECT COUNT(*) as count FROM events")
        .get().count;

      console.log(`\n📊 Data Counts:`);
      console.log(`   Fighters: ${fighterCount.toLocaleString()}`);
      console.log(`   Fights: ${fightCount.toLocaleString()}`);
      console.log(`   Events: ${eventCount.toLocaleString()}`);

      // Check for missing data
      const missingBirthdates = this.db
        .prepare(
          `
                SELECT COUNT(*) as count FROM fighters WHERE birthdate IS NULL
            `
        )
        .get().count;

      console.log(`\n⚠️  Data Quality Issues:`);
      console.log(
        `   Missing birthdates: ${missingBirthdates} (${(
          (missingBirthdates / fighterCount) *
          100
        ).toFixed(1)}%)`
      );

      // Check divisions
      const divisions = this.db
        .prepare(
          `
                SELECT weight_class, COUNT(*) as fight_count
                FROM fights
                WHERE weight_class IS NOT NULL
                GROUP BY weight_class
                ORDER BY fight_count DESC
            `
        )
        .all();

      console.log(`\n🏆 Divisions (${divisions.length} total):`);
      divisions.slice(0, 10).forEach((d) => {
        console.log(`   ${d.weight_class}: ${d.fight_count} fights`);
      });

      this.analysis.data_quality = {
        fighter_count: fighterCount,
        fight_count: fightCount,
        event_count: eventCount,
        missing_birthdates: missingBirthdates,
        division_count: divisions.length,
      };
    } catch (error) {
      console.log("   ❌ Error analyzing data quality:", error.message);
    }
  }

  async analyzeDatabaseSchema() {
    console.log("\n🗄️  DATABASE SCHEMA ANALYSIS");
    console.log("─".repeat(40));

    try {
      const tables = this.db
        .prepare(
          `
                SELECT name FROM sqlite_master WHERE type='table' ORDER BY name
            `
        )
        .all();

      console.log(`\n📋 All Tables (${tables.length} total):`);

      const whrTables = [];
      const coreTables = [];
      const otherTables = [];

      tables.forEach((t) => {
        if (t.name.includes("whr")) {
          whrTables.push(t.name);
        } else if (
          ["fighters", "fights", "events", "fight_stats"].includes(t.name)
        ) {
          coreTables.push(t.name);
        } else {
          otherTables.push(t.name);
        }
      });

      console.log(`\n🏆 WHR Tables (${whrTables.length}):`);
      whrTables.forEach((t) => console.log(`   - ${t}`));

      console.log(`\n📊 Core Tables (${coreTables.length}):`);
      coreTables.forEach((t) => console.log(`   - ${t}`));

      console.log(`\n📋 Other Tables (${otherTables.length}):`);
      otherTables.slice(0, 10).forEach((t) => console.log(`   - ${t}`));
      if (otherTables.length > 10) {
        console.log(`   ... and ${otherTables.length - 10} more`);
      }

      this.analysis.data_quality.whr_tables = whrTables.length;
      this.analysis.data_quality.core_tables = coreTables.length;
      this.analysis.data_quality.total_tables = tables.length;
    } catch (error) {
      console.log("   ❌ Error analyzing database schema:", error.message);
    }
  }

  async analyzeCodeQuality() {
    console.log("\n💻 CODE QUALITY ANALYSIS");
    console.log("─".repeat(40));

    try {
      const scriptFiles = fs
        .readdirSync(path.join(__dirname, "scripts"))
        .filter((f) => f.endsWith(".js"));

      console.log(`\n📁 Script Files (${scriptFiles.length} total):`);

      const whrFiles = scriptFiles.filter((f) => f.includes("whr"));
      const divisionFiles = scriptFiles.filter((f) => f.includes("division"));
      const testFiles = scriptFiles.filter(
        (f) => f.includes("test") || f.includes("validation")
      );

      console.log(`   WHR-related: ${whrFiles.length}`);
      console.log(`   Division-specific: ${divisionFiles.length}`);
      console.log(`   Testing/Validation: ${testFiles.length}`);

      // Check for main algorithm files
      const mainAlgorithms = [
        "whr-unified-algorithm.js",
        "whr-main-algorithm.js",
        "iterative-convergence-system.js",
      ];

      console.log(`\n🔧 Main Algorithm Files:`);
      mainAlgorithms.forEach((file) => {
        const exists = fs.existsSync(path.join(__dirname, "scripts", file));
        console.log(`   ${exists ? "✅" : "❌"} ${file}`);
      });
    } catch (error) {
      console.log("   ❌ Error analyzing code quality:", error.message);
    }
  }

  generateFinalReport() {
    console.log("\n📋 FINAL ANALYSIS REPORT");
    console.log("═".repeat(60));

    console.log("\n🎯 PHILOSOPHY COMPLIANCE SUMMARY:");
    Object.entries(this.analysis.philosophy_compliance).forEach(
      ([key, value]) => {
        const status =
          value === "IMPLEMENTED"
            ? "✅"
            : value === "PARTIALLY_IMPLEMENTED"
            ? "⚠️"
            : value === "NOT_IMPLEMENTED"
            ? "❌"
            : "❓";
        console.log(
          `   ${status} ${key.replace(/_/g, " ").toUpperCase()}: ${value}`
        );
      }
    );

    console.log("\n🔧 IMPLEMENTATION STATUS SUMMARY:");
    Object.entries(this.analysis.implementation_status).forEach(
      ([key, value]) => {
        const status =
          value === "IMPLEMENTED"
            ? "✅"
            : value === "PARTIALLY_IMPLEMENTED"
            ? "⚠️"
            : value === "NOT_IMPLEMENTED"
            ? "❌"
            : "❓";
        console.log(
          `   ${status} ${key.replace(/_/g, " ").toUpperCase()}: ${value}`
        );
      }
    );

    if (this.analysis.issues.length > 0) {
      console.log("\n⚠️  CRITICAL ISSUES FOUND:");
      this.analysis.issues.forEach((issue, i) => {
        console.log(`   ${i + 1}. ${issue}`);
      });
    }

    console.log("\n📊 DATA SUMMARY:");
    console.log(
      `   Database Size: ${this.analysis.data_quality.total_tables} tables`
    );
    console.log(`   WHR Tables: ${this.analysis.data_quality.whr_tables}`);
    console.log(
      `   Fighters: ${this.analysis.data_quality.fighter_count?.toLocaleString()}`
    );
    console.log(
      `   Fights: ${this.analysis.data_quality.fight_count?.toLocaleString()}`
    );

    // Overall assessment
    const implementedCount = Object.values(
      this.analysis.philosophy_compliance
    ).filter((v) => v === "IMPLEMENTED").length;
    const totalCount = Object.keys(this.analysis.philosophy_compliance).length;
    const completionRate = ((implementedCount / totalCount) * 100).toFixed(1);

    console.log("\n🏆 OVERALL ASSESSMENT:");
    console.log(
      `   Philosophy Compliance: ${completionRate}% (${implementedCount}/${totalCount})`
    );
    console.log(`   Critical Issues: ${this.analysis.issues.length}`);

    if (completionRate >= 80 && this.analysis.issues.length === 0) {
      console.log("   🎉 SYSTEM STATUS: PRODUCTION READY");
    } else if (completionRate >= 60) {
      console.log("   ⚠️  SYSTEM STATUS: NEEDS IMPROVEMENTS");
    } else {
      console.log("   ❌ SYSTEM STATUS: MAJOR WORK REQUIRED");
    }

    console.log("\n═".repeat(60));
  }
}

// Run the analysis
const analyzer = new WHRSystemAnalyzer();
analyzer.runCompleteAnalysis().catch(console.error);
