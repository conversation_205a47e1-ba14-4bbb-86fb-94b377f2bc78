# WHR System Unprocessed Fights Analysis

## Summary

The WHR system has **98 unprocessed fights** out of the total fights in the database. These fights are excluded based on specific criteria defined in the `temporal-accuracy-infrastructure.js` file.

## Exclusion Criteria

The WHR system filters out fights based on these conditions:

1. **No Contest Results** (`result_method LIKE '%No Contest%'`)
   - 78 fights excluded
   - Includes marijuana-related NCs and other NC reasons

2. **Disqualifications** (`result_method LIKE '%DQ%'`)
   - 20 fights excluded
   - DQ results are not considered valid competitive outcomes

3. **Catch Weight Fights** (`weight_class LIKE '%Catchweight%'`)
   - 1 fight excluded
   - Non-standard weight classes are not tracked

4. **Invalid Fight Status** (`fight_status != 'valid'`)
   - 69 fights marked as `excluded_nc`
   - 29 fights marked as `valid` (but excluded for other reasons)

## Breakdown by Weight Class

- Welterweight: 16 fights
- Lightweight: 15 fights
- Bantamweight: 15 fights
- Middleweight: 15 fights
- Light Heavyweight: 14 fights
- Featherweight: 9 fights
- Heavyweight: 8 fights
- Flyweight: 3 fights
- Women's Strawweight: 2 fights
- Catch Weight: 1 fight

## Notable Examples

### Early UFC Events (2005-2012)
- **UFC 79 (2007)**: <PERSON> vs <PERSON><PERSON> - DQ
- **UFC 111 (2010)**: <PERSON> vs <PERSON> - DQ
- **UFC 142 (2012)**: <PERSON>k Silva vs <PERSON> Prater - DQ

### No Contest Reasons
- **Marijuana**: 6 fights (e.g., Chris Clements vs Matthew Riddle, UFC 149)
- **Other**: 72 fights (various reasons including eye pokes, illegal strikes, etc.)

### Recent Examples
The most recent unprocessed fights include various No Contest and DQ results from 2013-2024.

## Why These Exclusions Make Sense

1. **No Contest**: These fights don't have a valid competitive outcome, making them unsuitable for rating calculations
2. **Disqualifications**: DQ results don't reflect actual fighting ability differences
3. **Catch Weight**: These fights occur outside standard weight divisions, making cross-comparison difficult
4. **Invalid Status**: Database-level exclusion for data quality reasons

## Conclusion

The 98 unprocessed fights represent approximately 1.3% of all fights in the database (98 out of ~7,357 total). The exclusion criteria are reasonable and ensure that only legitimate competitive outcomes within standard weight divisions are used for WHR rating calculations. This maintains the integrity and accuracy of the rating system.