{"permissions": {"allow": ["Bash(sqlite3:*)", "Bash(grep:*)", "Bash(node:*)", "Bash(rm:*)", "Bash(npm install:*)", "Bash(ls:*)", "<PERSON><PERSON>(chmod:*)", "Bash(bash:*)", "WebFetch(domain:github.com)", "WebFetch(domain:raw.githubusercontent.com)", "<PERSON><PERSON>(python3:*)", "Bash(awk:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(python:*)", "Bash(npm run analyze:age:*)", "Bash(npx tsx:*)", "Bash(npm run test-db:*)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(curl:*)", "Bash(cp:*)", "Bash(npx tsc:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(sed:*)", "mcp__ide__getDiagnostics", "Bash(find:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(diff:*)", "Bash(npx ts-node:*)", "<PERSON><PERSON>(true)", "Bash(npm run dev:*)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "<PERSON><PERSON>(echo:*)", "Bash(rg:*)", "Bash(npm test:*)", "<PERSON><PERSON>(timeout:*)"], "deny": []}, "enableAllProjectMcpServers": false}